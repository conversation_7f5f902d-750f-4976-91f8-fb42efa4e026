"""
API client for communicating with Exo Piper backend
"""

import requests
from typing import Dict, List, Any
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry


class APIClient:
    def __init__(self, api_url: str, api_key: str):
        self.api_url = api_url.rstrip('/')
        self.api_key = api_key
        
        # Setup session with retries
        self.session = requests.Session()
        retry_strategy = Retry(
            total=3,
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
        )
        adapter = HTTPAdapter(max_retries=retry_strategy)
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)
        
        # Set headers
        self.session.headers.update({
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json',
            'User-Agent': 'perf-audit-cli/1.0.0'
        })
    
    def get_user_info(self) -> Dict[str, Any]:
        """Get current user information"""
        response = self.session.get(f'{self.api_url}/api/v1/auth/me')
        response.raise_for_status()
        return response.json()
    
    def get_workloads(self) -> List[Dict[str, Any]]:
        """Get user's workloads"""
        response = self.session.get(f'{self.api_url}/api/v1/workloads/')
        response.raise_for_status()
        return response.json()['workloads']
    
    def create_workload(self, workload_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create a new workload"""
        response = self.session.post(
            f'{self.api_url}/api/v1/workloads/',
            json=workload_data
        )
        response.raise_for_status()
        return response.json()
    
    def run_benchmark(self, workload_id: int) -> Dict[str, Any]:
        """Start a benchmark job"""
        response = self.session.post(
            f'{self.api_url}/api/v1/benchmarks/run/{workload_id}'
        )
        response.raise_for_status()
        return response.json()
    
    def get_jobs(self) -> List[Dict[str, Any]]:
        """Get benchmark jobs"""
        response = self.session.get(f'{self.api_url}/api/v1/benchmarks/jobs')
        response.raise_for_status()
        return response.json()['jobs']
    
    def get_job_status(self, job_id: str) -> Dict[str, Any]:
        """Get status of a specific job"""
        response = self.session.get(f'{self.api_url}/api/v1/benchmarks/jobs/{job_id}')
        response.raise_for_status()
        return response.json()
    
    def submit_results(self, workload_name: str, results: Dict[str, Any]) -> None:
        """Submit benchmark results (placeholder)"""
        # TODO: Implement result submission endpoint
        # For now, this is a placeholder
        print(f"Would submit results for {workload_name}: {len(results)} data points")
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get performance summary"""
        response = self.session.get(f'{self.api_url}/api/v1/reports/performance-summary')
        response.raise_for_status()
        return response.json()
