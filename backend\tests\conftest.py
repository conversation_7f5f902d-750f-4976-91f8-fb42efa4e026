"""
Test configuration and fixtures for Exo Piper backend tests
"""

import pytest
import asyncio
from typing import As<PERSON><PERSON><PERSON>ator, Generator
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool

from app.main import app
from app.core.database import Base, get_db
from app.core.config import settings
from app.models.user import User
from app.models.workload import Workload
from app.models.benchmark import <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>ult, JobStatus
from app.core.security import get_password_hash, create_access_token


# Test database URL (in-memory SQLite)
TEST_DATABASE_URL = "sqlite+aiosqlite:///:memory:"


@pytest.fixture(scope="session")
def event_loop() -> Generator:
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
async def test_engine():
    """Create test database engine"""
    engine = create_async_engine(
        TEST_DATABASE_URL,
        connect_args={"check_same_thread": False},
        poolclass=StaticPool,
    )
    
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    
    yield engine
    
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.drop_all)
    
    await engine.dispose()


@pytest.fixture
async def test_session(test_engine) -> AsyncGenerator[AsyncSession, None]:
    """Create test database session"""
    async_session = sessionmaker(
        test_engine, class_=AsyncSession, expire_on_commit=False
    )
    
    async with async_session() as session:
        yield session


@pytest.fixture
async def test_client(test_session: AsyncSession) -> AsyncGenerator[AsyncClient, None]:
    """Create test HTTP client"""
    
    def override_get_db():
        return test_session
    
    app.dependency_overrides[get_db] = override_get_db
    
    async with AsyncClient(app=app, base_url="http://test") as client:
        yield client
    
    app.dependency_overrides.clear()


@pytest.fixture
async def test_user(test_session: AsyncSession) -> User:
    """Create test user"""
    user = User(
        username="testuser",
        email="<EMAIL>",
        hashed_password=get_password_hash("testpassword"),
        plan_type="free",
        is_active=True
    )
    test_session.add(user)
    await test_session.commit()
    await test_session.refresh(user)
    return user


@pytest.fixture
async def test_user_token(test_user: User) -> str:
    """Create test user access token"""
    return create_access_token(data={"sub": test_user.email})


@pytest.fixture
async def authenticated_client(test_client: AsyncClient, test_user_token: str) -> AsyncClient:
    """Create authenticated test client"""
    test_client.headers.update({"Authorization": f"Bearer {test_user_token}"})
    return test_client


@pytest.fixture
async def test_workload(test_session: AsyncSession, test_user: User) -> Workload:
    """Create test workload"""
    workload = Workload(
        user_id=test_user.id,
        name="test_mergesort",
        workload_type="mergesort",
        description="Test merge sort workload",
        config={
            "max_size": 1000,
            "iterations": 3,
            "step_factor": 2
        },
        is_active=True
    )
    test_session.add(workload)
    await test_session.commit()
    await test_session.refresh(workload)
    return workload


@pytest.fixture
async def test_benchmark_job(test_session: AsyncSession, test_user: User, test_workload: Workload) -> BenchmarkJob:
    """Create test benchmark job"""
    job = BenchmarkJob(
        user_id=test_user.id,
        workload_id=test_workload.id,
        job_id="test-job-123",
        status=JobStatus.COMPLETED,
        agent_info={"cpu_count": 4, "memory_gb": 8},
        docker_image="exopiper/agent:latest"
    )
    test_session.add(job)
    await test_session.commit()
    await test_session.refresh(job)
    return job


@pytest.fixture
async def test_benchmark_results(test_session: AsyncSession, test_benchmark_job: BenchmarkJob) -> list[BenchmarkResult]:
    """Create test benchmark results"""
    results = []
    
    # Create sample results with realistic complexity pattern
    input_sizes = [100, 200, 400, 800, 1600]
    base_time = 1000000  # 1ms in nanoseconds
    
    for i, size in enumerate(input_sizes):
        # Simulate O(n log n) complexity for merge sort
        import math
        expected_time = int(base_time * size * math.log2(size))
        
        result = BenchmarkResult(
            job_id=test_benchmark_job.id,
            input_size=size,
            execution_time_ns=expected_time,
            clean_time_ns=int(expected_time * 0.95),  # 5% overhead
            hardware_type="CPU",
            hardware_info={"cpu": "test-cpu", "cores": 4},
            lambda_value=base_time if i == 0 else None,  # Will be calculated
            p_exponent=1.1 if i == 0 else None,  # Will be calculated
            memory_usage_mb=size * 0.001,  # Proportional to input size
            metadata={"algorithm": "mergesort", "iteration": i}
        )
        results.append(result)
        test_session.add(result)
    
    await test_session.commit()
    
    for result in results:
        await test_session.refresh(result)
    
    return results


@pytest.fixture
def mock_docker_service():
    """Mock Docker service for testing"""
    class MockDockerService:
        async def run_benchmark(self, workload_type: str, config: dict, job_id: str):
            return {
                "status": "success",
                "results": [
                    {
                        "input_size": 100,
                        "execution_time_ns": 1000000,
                        "clean_time_ns": 950000,
                        "hardware_type": "CPU",
                        "hardware_info": {"cpu": "mock-cpu"},
                        "memory_usage_mb": 0.1,
                        "metadata": {"algorithm": workload_type}
                    }
                ],
                "logs": "Mock benchmark execution completed",
                "artifacts": {"mock": True}
            }
    
    return MockDockerService()


@pytest.fixture
def mock_storage_service():
    """Mock storage service for testing"""
    class MockStorageService:
        def upload_benchmark_logs(self, job_id: str, logs: str, log_type: str = "execution"):
            return f"logs/{job_id}/{log_type}_mock.log"
        
        def upload_benchmark_artifacts(self, job_id: str, artifacts: dict):
            return f"artifacts/{job_id}/artifacts_mock.json"
        
        def get_object_url(self, object_key: str):
            return f"http://mock-storage/{object_key}"
    
    return MockStorageService()
