"""
Webhook service for payment notifications and external integrations
"""

import asyncio
import logging
import json
import httpx
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_

from ..core.database import AsyncSessionLocal
from ..core.config import settings
from ..models.payment import Payment, PaymentWebhook, PaymentStatus

logger = logging.getLogger(__name__)


class WebhookService:
    """Service for managing payment webhooks and notifications"""
    
    def __init__(self):
        self.http_client = httpx.AsyncClient(timeout=30.0)
    
    async def send_payment_webhook(
        self,
        payment_id: int,
        event_type: str,
        webhook_url: str = None,
        custom_payload: Dict[str, Any] = None
    ) -> bool:
        """
        Send webhook notification for payment event
        
        Args:
            payment_id: Payment ID
            event_type: Event type (payment_confirmed, payment_failed, etc.)
            webhook_url: Custom webhook URL (optional)
            custom_payload: Additional payload data
            
        Returns:
            True if webhook sent successfully, False otherwise
        """
        
        async with AsyncSessionLocal() as db:
            # Get payment details
            payment_query = select(Payment).where(Payment.id == payment_id)
            payment_result = await db.execute(payment_query)
            payment = payment_result.scalar_one_or_none()
            
            if not payment:
                logger.error(f"Payment {payment_id} not found for webhook")
                return False
            
            # Build webhook payload
            payload = {
                "event_type": event_type,
                "timestamp": datetime.utcnow().isoformat(),
                "payment": {
                    "id": payment.id,
                    "user_id": payment.user_id,
                    "amount": str(payment.amount),
                    "currency": payment.currency,
                    "network": payment.network,
                    "target_plan": payment.target_plan,
                    "status": payment.status.value,
                    "transaction_hash": payment.transaction_hash,
                    "created_at": payment.created_at.isoformat(),
                    "confirmed_at": payment.confirmed_at.isoformat() if payment.confirmed_at else None
                }
            }
            
            # Add custom payload if provided
            if custom_payload:
                payload.update(custom_payload)
            
            # Use default webhook URL if not provided
            if not webhook_url:
                webhook_url = getattr(settings, 'DEFAULT_WEBHOOK_URL', None)
            
            if not webhook_url:
                logger.warning(f"No webhook URL configured for payment {payment_id}")
                return False
            
            # Create webhook record
            webhook = PaymentWebhook(
                payment_id=payment_id,
                event_type=event_type,
                webhook_url=webhook_url,
                request_payload=json.dumps(payload),
                status="pending"
            )
            
            db.add(webhook)
            await db.commit()
            await db.refresh(webhook)
            
            # Send webhook
            success = await self._send_webhook_request(webhook, payload, db)
            
            return success
    
    async def _send_webhook_request(
        self,
        webhook: PaymentWebhook,
        payload: Dict[str, Any],
        db: AsyncSession
    ) -> bool:
        """Send the actual webhook HTTP request"""
        
        try:
            headers = {
                "Content-Type": "application/json",
                "User-Agent": "ExoPiper-Webhook/1.0",
                "X-ExoPiper-Event": webhook.event_type,
                "X-ExoPiper-Delivery": str(webhook.id)
            }
            
            # Add signature if secret is configured
            if hasattr(settings, 'WEBHOOK_SECRET') and settings.WEBHOOK_SECRET:
                import hmac
                import hashlib
                
                signature = hmac.new(
                    settings.WEBHOOK_SECRET.encode(),
                    json.dumps(payload).encode(),
                    hashlib.sha256
                ).hexdigest()
                
                headers["X-ExoPiper-Signature"] = f"sha256={signature}"
            
            response = await self.http_client.post(
                webhook.webhook_url,
                json=payload,
                headers=headers
            )
            
            # Update webhook record
            webhook.attempts += 1
            webhook.response_status = response.status_code
            webhook.response_body = response.text[:1000]  # Limit response body size
            webhook.sent_at = datetime.utcnow()
            
            if response.status_code in [200, 201, 202]:
                webhook.status = "sent"
                await db.commit()
                logger.info(f"Webhook {webhook.id} sent successfully")
                return True
            else:
                webhook.status = "failed"
                
                # Schedule retry if not max attempts reached
                if webhook.attempts < webhook.max_attempts:
                    webhook.next_retry_at = datetime.utcnow() + timedelta(minutes=5 * webhook.attempts)
                    webhook.status = "pending"
                else:
                    webhook.status = "max_retries_reached"
                
                await db.commit()
                logger.warning(f"Webhook {webhook.id} failed with status {response.status_code}")
                return False
                
        except Exception as e:
            # Update webhook record with error
            webhook.attempts += 1
            webhook.response_body = str(e)[:1000]
            webhook.sent_at = datetime.utcnow()
            
            if webhook.attempts < webhook.max_attempts:
                webhook.next_retry_at = datetime.utcnow() + timedelta(minutes=5 * webhook.attempts)
                webhook.status = "pending"
            else:
                webhook.status = "max_retries_reached"
            
            await db.commit()
            logger.error(f"Webhook {webhook.id} error: {e}")
            return False
    
    async def retry_failed_webhooks(self):
        """Retry failed webhooks that are due for retry"""
        
        async with AsyncSessionLocal() as db:
            # Get webhooks ready for retry
            retry_query = select(PaymentWebhook).where(
                and_(
                    PaymentWebhook.status == "pending",
                    PaymentWebhook.next_retry_at <= datetime.utcnow(),
                    PaymentWebhook.attempts < PaymentWebhook.max_attempts
                )
            )
            
            retry_result = await db.execute(retry_query)
            webhooks_to_retry = retry_result.scalars().all()
            
            for webhook in webhooks_to_retry:
                try:
                    # Parse payload
                    payload = json.loads(webhook.request_payload)
                    
                    # Retry webhook
                    await self._send_webhook_request(webhook, payload, db)
                    
                except Exception as e:
                    logger.error(f"Error retrying webhook {webhook.id}: {e}")
            
            logger.info(f"Retried {len(webhooks_to_retry)} webhooks")
    
    async def send_plan_upgrade_notification(self, user_id: int, old_plan: str, new_plan: str):
        """Send notification when user plan is upgraded"""
        
        payload = {
            "event_type": "plan_upgraded",
            "user_id": user_id,
            "old_plan": old_plan,
            "new_plan": new_plan,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        # Send to configured notification endpoints
        notification_urls = getattr(settings, 'PLAN_UPGRADE_WEBHOOKS', [])
        
        for url in notification_urls:
            try:
                await self.http_client.post(url, json=payload)
                logger.info(f"Plan upgrade notification sent to {url}")
            except Exception as e:
                logger.error(f"Failed to send plan upgrade notification to {url}: {e}")
    
    async def send_payment_reminder(self, payment_id: int):
        """Send payment reminder for pending payments"""
        
        async with AsyncSessionLocal() as db:
            payment_query = select(Payment).where(Payment.id == payment_id)
            payment_result = await db.execute(payment_query)
            payment = payment_result.scalar_one_or_none()
            
            if not payment or payment.status != PaymentStatus.PENDING:
                return
            
            # Check if payment is close to expiry (within 2 hours)
            time_until_expiry = payment.expires_at - datetime.utcnow()
            
            if time_until_expiry.total_seconds() <= 7200:  # 2 hours
                await self.send_payment_webhook(
                    payment_id=payment_id,
                    event_type="payment_reminder",
                    custom_payload={
                        "expires_in_minutes": int(time_until_expiry.total_seconds() / 60),
                        "reminder_type": "expiry_warning"
                    }
                )
    
    async def get_webhook_history(self, payment_id: int) -> List[Dict[str, Any]]:
        """Get webhook history for a payment"""
        
        async with AsyncSessionLocal() as db:
            webhooks_query = select(PaymentWebhook).where(
                PaymentWebhook.payment_id == payment_id
            ).order_by(PaymentWebhook.created_at.desc())
            
            webhooks_result = await db.execute(webhooks_query)
            webhooks = webhooks_result.scalars().all()
            
            return [
                {
                    "id": webhook.id,
                    "event_type": webhook.event_type,
                    "webhook_url": webhook.webhook_url,
                    "status": webhook.status,
                    "attempts": webhook.attempts,
                    "response_status": webhook.response_status,
                    "created_at": webhook.created_at.isoformat(),
                    "sent_at": webhook.sent_at.isoformat() if webhook.sent_at else None,
                    "next_retry_at": webhook.next_retry_at.isoformat() if webhook.next_retry_at else None
                }
                for webhook in webhooks
            ]
    
    async def cleanup_old_webhooks(self, days_old: int = 30):
        """Clean up old webhook records"""
        
        async with AsyncSessionLocal() as db:
            cutoff_date = datetime.utcnow() - timedelta(days=days_old)
            
            old_webhooks_query = select(PaymentWebhook).where(
                PaymentWebhook.created_at < cutoff_date
            )
            
            old_result = await db.execute(old_webhooks_query)
            old_webhooks = old_result.scalars().all()
            
            for webhook in old_webhooks:
                await db.delete(webhook)
            
            await db.commit()
            
            logger.info(f"Cleaned up {len(old_webhooks)} old webhook records")


# Global webhook service instance
webhook_service = WebhookService()
