"""
Docker runner for executing benchmarks locally
"""

import docker
import json
import time
from typing import Dict, List, Any
from rich.console import Console

console = Console()


class DockerRunner:
    def __init__(self):
        try:
            self.client = docker.from_env()
        except docker.errors.DockerException as e:
            console.print(f"❌ Docker not available: {e}", style="red")
            raise
    
    def run_benchmark(self, workload_name: str, workload_config: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Run a benchmark using Docker agent"""
        
        workload_type = workload_config['type']
        config = workload_config['config']
        
        console.print(f"🐳 Starting Docker container for {workload_name}")
        
        # Prepare environment variables
        env_vars = {
            'WORKLOAD_TYPE': workload_type,
            'WORKLOAD_CONFIG': json.dumps(config),
            'WORKLOAD_NAME': workload_name
        }
        
        try:
            # Run the benchmark container
            # For now, we'll use a simple Python image and simulate the benchmark
            container = self.client.containers.run(
                image='python:3.11-slim',
                command=self._get_benchmark_command(workload_type, config),
                environment=env_vars,
                detach=True,
                remove=True,
                mem_limit='1g',
                cpu_count=1
            )
            
            # Wait for completion and get logs
            result = container.wait()
            logs = container.logs().decode('utf-8')
            
            if result['StatusCode'] != 0:
                raise Exception(f"Benchmark failed with exit code {result['StatusCode']}: {logs}")
            
            # Parse results from logs
            results = self._parse_benchmark_results(logs)
            
            console.print(f"✅ Benchmark completed: {len(results)} data points")
            return results
            
        except docker.errors.ContainerError as e:
            console.print(f"❌ Container error: {e}", style="red")
            raise
        except docker.errors.ImageNotFound as e:
            console.print(f"❌ Image not found: {e}", style="red")
            raise
        except Exception as e:
            console.print(f"❌ Unexpected error: {e}", style="red")
            raise
    
    def _get_benchmark_command(self, workload_type: str, config: Dict[str, Any]) -> List[str]:
        """Get the command to run for a specific workload type"""
        
        if workload_type == 'mergesort':
            return [
                'python', '-c', '''
import time
import random
import json
import os

def mergesort(arr):
    if len(arr) <= 1:
        return arr
    
    mid = len(arr) // 2
    left = mergesort(arr[:mid])
    right = mergesort(arr[mid:])
    
    return merge(left, right)

def merge(left, right):
    result = []
    i = j = 0
    
    while i < len(left) and j < len(right):
        if left[i] <= right[j]:
            result.append(left[i])
            i += 1
        else:
            result.append(right[j])
            j += 1
    
    result.extend(left[i:])
    result.extend(right[j:])
    return result

config = json.loads(os.environ["WORKLOAD_CONFIG"])
max_size = config.get("max_size", 10000)
iterations = config.get("iterations", 3)
step_factor = config.get("step_factor", 2)

results = []
size = 100

while size <= max_size:
    times = []
    for _ in range(iterations):
        arr = [random.randint(1, 1000) for _ in range(size)]
        start_time = time.perf_counter_ns()
        mergesort(arr)
        end_time = time.perf_counter_ns()
        times.append(end_time - start_time)
    
    avg_time = sum(times) / len(times)
    results.append({
        "input_size": size,
        "execution_time_ns": int(avg_time),
        "hardware_type": "CPU",
        "iterations": iterations
    })
    
    size *= step_factor

print("BENCHMARK_RESULTS_START")
print(json.dumps(results))
print("BENCHMARK_RESULTS_END")
'''
            ]
        
        elif workload_type == '3sat':
            return [
                'python', '-c', '''
import time
import random
import json
import os

def generate_3sat_instance(num_vars, num_clauses):
    clauses = []
    for _ in range(num_clauses):
        clause = []
        for _ in range(3):
            var = random.randint(1, num_vars)
            if random.random() < 0.5:
                var = -var
            clause.append(var)
        clauses.append(clause)
    return clauses

def solve_3sat_bruteforce(clauses, num_vars):
    # Simple brute force solver
    for assignment in range(2**num_vars):
        if evaluate_assignment(clauses, assignment, num_vars):
            return True
    return False

def evaluate_assignment(clauses, assignment, num_vars):
    for clause in clauses:
        satisfied = False
        for literal in clause:
            var_idx = abs(literal) - 1
            var_value = (assignment >> var_idx) & 1
            if (literal > 0 and var_value) or (literal < 0 and not var_value):
                satisfied = True
                break
        if not satisfied:
            return False
    return True

config = json.loads(os.environ["WORKLOAD_CONFIG"])
max_vars = config.get("max_variables", 20)
iterations = config.get("iterations", 3)
clause_ratio = config.get("clause_ratio", 4.3)

results = []
num_vars = 5

while num_vars <= max_vars:
    num_clauses = int(num_vars * clause_ratio)
    times = []
    
    for _ in range(iterations):
        clauses = generate_3sat_instance(num_vars, num_clauses)
        start_time = time.perf_counter_ns()
        solve_3sat_bruteforce(clauses, num_vars)
        end_time = time.perf_counter_ns()
        times.append(end_time - start_time)
    
    avg_time = sum(times) / len(times)
    results.append({
        "input_size": num_vars,
        "execution_time_ns": int(avg_time),
        "hardware_type": "CPU",
        "iterations": iterations,
        "num_clauses": num_clauses
    })
    
    num_vars += 2

print("BENCHMARK_RESULTS_START")
print(json.dumps(results))
print("BENCHMARK_RESULTS_END")
'''
            ]
        
        else:
            raise ValueError(f"Unknown workload type: {workload_type}")
    
    def _parse_benchmark_results(self, logs: str) -> List[Dict[str, Any]]:
        """Parse benchmark results from container logs"""
        
        lines = logs.strip().split('\n')
        start_idx = None
        end_idx = None
        
        for i, line in enumerate(lines):
            if line.strip() == "BENCHMARK_RESULTS_START":
                start_idx = i + 1
            elif line.strip() == "BENCHMARK_RESULTS_END":
                end_idx = i
                break
        
        if start_idx is None or end_idx is None:
            raise Exception("Could not find benchmark results in logs")
        
        results_json = '\n'.join(lines[start_idx:end_idx])
        
        try:
            return json.loads(results_json)
        except json.JSONDecodeError as e:
            raise Exception(f"Failed to parse benchmark results: {e}")
