import { test, expect } from '@playwright/test'

test.describe('Authentication Flow', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/')
  })

  test('should display login page', async ({ page }) => {
    await expect(page).toHaveTitle(/Exo Piper/)
    await expect(page.locator('h1')).toContainText('Login')
    await expect(page.locator('input[type="email"]')).toBeVisible()
    await expect(page.locator('input[type="password"]')).toBeVisible()
    await expect(page.locator('button[type="submit"]')).toBeVisible()
  })

  test('should show validation errors for empty form', async ({ page }) => {
    await page.click('button[type="submit"]')
    
    await expect(page.locator('text=Email é obrigatório')).toBeVisible()
    await expect(page.locator('text=<PERSON><PERSON> é obrigatória')).toBeVisible()
  })

  test('should show error for invalid credentials', async ({ page }) => {
    await page.fill('input[type="email"]', '<EMAIL>')
    await page.fill('input[type="password"]', 'wrongpassword')
    await page.click('button[type="submit"]')
    
    await expect(page.locator('text=Credenciais inválidas')).toBeVisible()
  })

  test('should login successfully with valid credentials', async ({ page }) => {
    // Create test user first (this would be done in test setup)
    await page.fill('input[type="email"]', '<EMAIL>')
    await page.fill('input[type="password"]', 'testpassword')
    await page.click('button[type="submit"]')
    
    // Should redirect to dashboard
    await expect(page).toHaveURL('/dashboard')
    await expect(page.locator('h1')).toContainText('Dashboard')
  })

  test('should navigate to register page', async ({ page }) => {
    await page.click('text=Criar conta')
    
    await expect(page).toHaveURL('/register')
    await expect(page.locator('h1')).toContainText('Criar Conta')
  })

  test('should register new user successfully', async ({ page }) => {
    await page.goto('/register')
    
    const timestamp = Date.now()
    const testEmail = `test${timestamp}@example.com`
    
    await page.fill('input[name="username"]', `testuser${timestamp}`)
    await page.fill('input[name="email"]', testEmail)
    await page.fill('input[name="password"]', 'testpassword123')
    await page.fill('input[name="confirmPassword"]', 'testpassword123')
    
    await page.click('button[type="submit"]')
    
    // Should redirect to dashboard after successful registration
    await expect(page).toHaveURL('/dashboard')
    await expect(page.locator('text=Bem-vindo')).toBeVisible()
  })

  test('should logout successfully', async ({ page }) => {
    // Login first
    await page.fill('input[type="email"]', '<EMAIL>')
    await page.fill('input[type="password"]', 'testpassword')
    await page.click('button[type="submit"]')
    
    await expect(page).toHaveURL('/dashboard')
    
    // Logout
    await page.click('[data-testid="user-menu"]')
    await page.click('text=Sair')
    
    // Should redirect to login page
    await expect(page).toHaveURL('/')
    await expect(page.locator('h1')).toContainText('Login')
  })

  test('should protect dashboard route when not authenticated', async ({ page }) => {
    await page.goto('/dashboard')
    
    // Should redirect to login
    await expect(page).toHaveURL('/')
    await expect(page.locator('h1')).toContainText('Login')
  })

  test('should remember user session', async ({ page, context }) => {
    // Login
    await page.fill('input[type="email"]', '<EMAIL>')
    await page.fill('input[type="password"]', 'testpassword')
    await page.click('button[type="submit"]')
    
    await expect(page).toHaveURL('/dashboard')
    
    // Open new page in same context
    const newPage = await context.newPage()
    await newPage.goto('/dashboard')
    
    // Should still be authenticated
    await expect(newPage).toHaveURL('/dashboard')
    await expect(newPage.locator('h1')).toContainText('Dashboard')
  })

  test('should handle password reset flow', async ({ page }) => {
    await page.click('text=Esqueci minha senha')
    
    await expect(page).toHaveURL('/forgot-password')
    await expect(page.locator('h1')).toContainText('Recuperar Senha')
    
    await page.fill('input[type="email"]', '<EMAIL>')
    await page.click('button[type="submit"]')
    
    await expect(page.locator('text=Email de recuperação enviado')).toBeVisible()
  })
})
