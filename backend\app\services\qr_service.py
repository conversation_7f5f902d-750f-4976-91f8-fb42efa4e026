"""
QR Code generation service for payment requests
"""

import qrcode
import qrcode.image.svg
from io import BytesIO
import base64
from typing import Dict, Any
from decimal import Decimal

class QRCodeService:
    """Service for generating QR codes for payments"""
    
    @staticmethod
    def generate_payment_qr(
        wallet_address: str,
        amount: Decimal,
        currency: str = "USDT",
        network: str = "bep20",
        contract_address: str = None
    ) -> Dict[str, Any]:
        """
        Generate QR code for payment
        
        Args:
            wallet_address: Destination wallet address
            amount: Payment amount
            currency: Currency (USDT)
            network: Network (bep20, erc20)
            contract_address: Token contract address
            
        Returns:
            Dictionary with QR code data and image
        """
        
        # Generate payment URL based on network
        if network == "bep20":
            # BEP20 payment URL format for BSC
            payment_url = f"bnb:{wallet_address}?amount={amount}&token={contract_address}"
        elif network == "erc20":
            # ERC20 payment URL format for Ethereum
            # Convert amount to wei (USDT has 6 decimals)
            amount_wei = int(amount * 10**6)
            payment_url = f"ethereum:{contract_address}/transfer?address={wallet_address}&uint256={amount_wei}"
        else:
            # Generic format
            payment_url = f"{currency}:{wallet_address}?amount={amount}"
        
        # Generate QR code
        qr = qrcode.QRCode(
            version=1,
            error_correction=qrcode.constants.ERROR_CORRECT_L,
            box_size=10,
            border=4,
        )
        qr.add_data(payment_url)
        qr.make(fit=True)
        
        # Create PNG image
        img = qr.make_image(fill_color="black", back_color="white")
        
        # Convert to base64
        buffer = BytesIO()
        img.save(buffer, format='PNG')
        img_base64 = base64.b64encode(buffer.getvalue()).decode()
        
        # Create SVG version for web
        factory = qrcode.image.svg.SvgPathImage
        svg_qr = qrcode.QRCode(image_factory=factory)
        svg_qr.add_data(payment_url)
        svg_qr.make(fit=True)
        
        svg_buffer = BytesIO()
        svg_img = svg_qr.make_image()
        svg_img.save(svg_buffer)
        svg_data = svg_buffer.getvalue().decode()
        
        return {
            "payment_url": payment_url,
            "qr_code_png_base64": f"data:image/png;base64,{img_base64}",
            "qr_code_svg": svg_data,
            "network": network,
            "amount": str(amount),
            "currency": currency,
            "wallet_address": wallet_address
        }
    
    @staticmethod
    def generate_bitcoin_qr(
        bitcoin_address: str,
        amount_btc: Decimal,
        label: str = None,
        message: str = None
    ) -> Dict[str, Any]:
        """
        Generate QR code for Bitcoin payment (for TANOS integration)
        
        Args:
            bitcoin_address: Bitcoin address
            amount_btc: Amount in BTC
            label: Payment label
            message: Payment message
            
        Returns:
            Dictionary with QR code data and image
        """
        
        # Bitcoin URI format
        bitcoin_uri = f"bitcoin:{bitcoin_address}?amount={amount_btc}"
        
        if label:
            bitcoin_uri += f"&label={label}"
        if message:
            bitcoin_uri += f"&message={message}"
        
        # Generate QR code
        qr = qrcode.QRCode(
            version=1,
            error_correction=qrcode.constants.ERROR_CORRECT_L,
            box_size=10,
            border=4,
        )
        qr.add_data(bitcoin_uri)
        qr.make(fit=True)
        
        # Create PNG image
        img = qr.make_image(fill_color="black", back_color="white")
        
        # Convert to base64
        buffer = BytesIO()
        img.save(buffer, format='PNG')
        img_base64 = base64.b64encode(buffer.getvalue()).decode()
        
        return {
            "bitcoin_uri": bitcoin_uri,
            "qr_code_png_base64": f"data:image/png;base64,{img_base64}",
            "amount_btc": str(amount_btc),
            "bitcoin_address": bitcoin_address
        }
    
    @staticmethod
    def generate_tanos_swap_qr(
        swap_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Generate QR code for TANOS atomic swap
        
        Args:
            swap_data: TANOS swap information
            
        Returns:
            Dictionary with QR code data and image
        """
        
        # TANOS swap URL format (custom protocol)
        tanos_uri = f"tanos://swap?id={swap_data['swap_id']}&commitment={swap_data['commitment']}&amount={swap_data['amount']}"
        
        # Generate QR code
        qr = qrcode.QRCode(
            version=1,
            error_correction=qrcode.constants.ERROR_CORRECT_M,  # Higher error correction for complex data
            box_size=8,
            border=4,
        )
        qr.add_data(tanos_uri)
        qr.make(fit=True)
        
        # Create PNG image
        img = qr.make_image(fill_color="black", back_color="white")
        
        # Convert to base64
        buffer = BytesIO()
        img.save(buffer, format='PNG')
        img_base64 = base64.b64encode(buffer.getvalue()).decode()
        
        return {
            "tanos_uri": tanos_uri,
            "qr_code_png_base64": f"data:image/png;base64,{img_base64}",
            "swap_id": swap_data['swap_id'],
            "commitment": swap_data['commitment']
        }


# Global QR service instance
qr_service = QRCodeService()
