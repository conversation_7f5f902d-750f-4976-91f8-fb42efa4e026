#!/bin/bash

# Exo Piper System Optimization Script
# Optimizes the system for production performance

set -e

echo "⚡ Exo Piper System Optimization"
echo "================================"

# Colors
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Database optimizations
optimize_database() {
    log_info "Optimizing PostgreSQL database..."
    
    # Create optimized PostgreSQL configuration
    cat > postgres-optimization.conf << 'EOF'
# PostgreSQL Optimization for Exo Piper
# Add these to postgresql.conf

# Memory settings
shared_buffers = 256MB
effective_cache_size = 1GB
work_mem = 4MB
maintenance_work_mem = 64MB

# Checkpoint settings
checkpoint_completion_target = 0.9
wal_buffers = 16MB
default_statistics_target = 100

# Connection settings
max_connections = 200

# Query optimization
random_page_cost = 1.1
effective_io_concurrency = 200

# Logging
log_min_duration_statement = 1000
log_checkpoints = on
log_connections = on
log_disconnections = on
log_lock_waits = on
EOF

    # Apply database optimizations via Docker
    if docker-compose ps postgres | grep -q "Up"; then
        log_info "Applying database optimizations..."
        
        # Create indexes for better performance
        docker-compose exec -T postgres psql -U exoPiper -d exoPiper << 'SQL'
-- Performance indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_benchmark_jobs_user_created 
ON benchmark_jobs(user_id, created_at DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_benchmark_results_job_input 
ON benchmark_results(job_id, input_size);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_payments_user_status 
ON payments(user_id, status, created_at DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_workloads_user_updated 
ON workloads(user_id, updated_at DESC);

-- Analyze tables for query optimization
ANALYZE;

-- Update statistics
UPDATE pg_stat_user_tables SET n_tup_ins = 0, n_tup_upd = 0, n_tup_del = 0;
SQL

        log_success "Database optimizations applied"
    else
        log_warning "Database not running, skipping optimizations"
    fi
}

# Redis optimizations
optimize_redis() {
    log_info "Optimizing Redis cache..."
    
    # Create Redis optimization config
    cat > redis-optimization.conf << 'EOF'
# Redis Optimization for Exo Piper
# Add these to redis.conf

# Memory optimization
maxmemory 512mb
maxmemory-policy allkeys-lru

# Persistence optimization
save 900 1
save 300 10
save 60 10000

# Network optimization
tcp-keepalive 300
timeout 0

# Performance
hash-max-ziplist-entries 512
hash-max-ziplist-value 64
list-max-ziplist-size -2
set-max-intset-entries 512
zset-max-ziplist-entries 128
zset-max-ziplist-value 64
EOF

    if docker-compose ps redis | grep -q "Up"; then
        log_info "Applying Redis optimizations..."
        
        # Apply Redis optimizations
        docker-compose exec -T redis redis-cli << 'REDIS'
CONFIG SET maxmemory 512mb
CONFIG SET maxmemory-policy allkeys-lru
CONFIG SET save "900 1 300 10 60 10000"
CONFIG SET tcp-keepalive 300
CONFIG REWRITE
REDIS

        log_success "Redis optimizations applied"
    else
        log_warning "Redis not running, skipping optimizations"
    fi
}

# Application optimizations
optimize_application() {
    log_info "Optimizing application settings..."
    
    # Create optimized environment variables
    cat >> .env.production << 'EOF'

# Performance optimizations
UVICORN_WORKERS=4
UVICORN_WORKER_CLASS=uvicorn.workers.UvicornWorker
UVICORN_BACKLOG=2048
UVICORN_MAX_REQUESTS=1000
UVICORN_MAX_REQUESTS_JITTER=100

# Database connection pooling
DATABASE_POOL_SIZE=20
DATABASE_MAX_OVERFLOW=30
DATABASE_POOL_TIMEOUT=30
DATABASE_POOL_RECYCLE=3600

# Redis connection pooling
REDIS_POOL_SIZE=20
REDIS_POOL_MAX_CONNECTIONS=50

# Celery optimizations
CELERY_WORKER_PREFETCH_MULTIPLIER=1
CELERY_WORKER_MAX_TASKS_PER_CHILD=1000
CELERY_TASK_ACKS_LATE=true
CELERY_WORKER_DISABLE_RATE_LIMITS=true

# Cache settings
CACHE_TTL=3600
CACHE_MAX_SIZE=1000

# File upload limits
MAX_UPLOAD_SIZE=10485760
MAX_CODE_LENGTH=100000
EOF

    log_success "Application optimizations configured"
}

# Docker optimizations
optimize_docker() {
    log_info "Optimizing Docker configuration..."
    
    # Create optimized docker-compose override
    cat > docker-compose.override.yml << 'EOF'
version: '3.8'

services:
  backend:
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'
    environment:
      - UVICORN_WORKERS=4
      - UVICORN_WORKER_CLASS=uvicorn.workers.UvicornWorker
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  frontend:
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000"]
      interval: 30s
      timeout: 10s
      retries: 3

  postgres:
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'
    command: >
      postgres
      -c shared_buffers=256MB
      -c effective_cache_size=1GB
      -c work_mem=4MB
      -c maintenance_work_mem=64MB
      -c checkpoint_completion_target=0.9
      -c wal_buffers=16MB
      -c default_statistics_target=100
      -c random_page_cost=1.1
      -c effective_io_concurrency=200
      -c max_connections=200

  redis:
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'
    command: >
      redis-server
      --maxmemory 512mb
      --maxmemory-policy allkeys-lru
      --save 900 1
      --save 300 10
      --save 60 10000

  celery:
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'
    environment:
      - CELERY_WORKER_PREFETCH_MULTIPLIER=1
      - CELERY_WORKER_MAX_TASKS_PER_CHILD=1000

  celery-payments:
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'
EOF

    log_success "Docker optimizations configured"
}

# System monitoring setup
setup_monitoring() {
    log_info "Setting up system monitoring..."
    
    # Create monitoring script
    cat > monitor-performance.sh << 'EOF'
#!/bin/bash

# Performance monitoring script for Exo Piper

echo "=== Exo Piper Performance Monitor ==="
echo "Date: $(date)"
echo

# Docker stats
echo "=== Docker Container Stats ==="
docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.MemPerc}}\t{{.NetIO}}\t{{.BlockIO}}"

echo
echo "=== Database Performance ==="
docker-compose exec -T postgres psql -U exoPiper -d exoPiper -c "
SELECT 
    schemaname,
    tablename,
    attname,
    n_distinct,
    correlation
FROM pg_stats 
WHERE schemaname = 'public' 
ORDER BY tablename, attname;
"

echo
echo "=== Redis Performance ==="
docker-compose exec -T redis redis-cli INFO memory | grep -E "(used_memory|used_memory_peak|mem_fragmentation_ratio)"

echo
echo "=== System Resources ==="
echo "CPU Usage:"
top -bn1 | grep "Cpu(s)" | awk '{print $2 + $4"%"}'

echo "Memory Usage:"
free -h | awk 'NR==2{printf "%.2f%%\n", $3*100/$2 }'

echo "Disk Usage:"
df -h / | awk 'NR==2{print $5}'

echo
echo "=== Application Metrics ==="
curl -s http://localhost:8000/health | jq '.' 2>/dev/null || echo "Backend not responding"

echo
echo "=== Recent Errors ==="
docker-compose logs --tail=10 backend | grep -i error || echo "No recent errors"
EOF

    chmod +x monitor-performance.sh
    
    log_success "Performance monitoring setup complete"
}

# Cleanup and optimization
cleanup_system() {
    log_info "Cleaning up system..."
    
    # Remove unused Docker images
    docker image prune -f
    
    # Remove unused volumes
    docker volume prune -f
    
    # Remove unused networks
    docker network prune -f
    
    # Clean up logs
    docker-compose logs --tail=1000 > system-logs-backup.log 2>&1
    
    log_success "System cleanup completed"
}

# Performance testing
run_performance_tests() {
    log_info "Running performance tests..."
    
    # Create performance test script
    cat > performance-test.sh << 'EOF'
#!/bin/bash

echo "=== Performance Test Results ==="

# Test API response time
echo "API Response Time:"
curl -o /dev/null -s -w "Connect: %{time_connect}s\nTTFB: %{time_starttransfer}s\nTotal: %{time_total}s\n" http://localhost:8000/health

# Test database query performance
echo -e "\nDatabase Query Performance:"
docker-compose exec -T postgres psql -U exoPiper -d exoPiper -c "
EXPLAIN ANALYZE SELECT COUNT(*) FROM benchmark_jobs WHERE created_at > NOW() - INTERVAL '24 hours';
"

# Test Redis performance
echo -e "\nRedis Performance:"
docker-compose exec -T redis redis-cli --latency-history -i 1 | head -5

# Test concurrent connections
echo -e "\nConcurrent Connection Test:"
for i in {1..10}; do
    curl -s http://localhost:8000/health > /dev/null &
done
wait
echo "10 concurrent requests completed"
EOF

    chmod +x performance-test.sh
    ./performance-test.sh
    
    log_success "Performance tests completed"
}

# Main optimization function
main() {
    log_info "Starting system optimization..."
    
    optimize_database
    optimize_redis
    optimize_application
    optimize_docker
    setup_monitoring
    cleanup_system
    
    if [ "$1" = "--test" ]; then
        run_performance_tests
    fi
    
    log_success "System optimization completed!"
    
    echo
    echo "📊 Optimization Summary:"
    echo "✅ Database indexes and configuration optimized"
    echo "✅ Redis cache configuration optimized"
    echo "✅ Application settings tuned for performance"
    echo "✅ Docker resource limits configured"
    echo "✅ Monitoring scripts created"
    echo "✅ System cleanup performed"
    echo
    echo "🚀 Next steps:"
    echo "1. Restart services: docker-compose restart"
    echo "2. Monitor performance: ./monitor-performance.sh"
    echo "3. Run performance tests: ./performance-test.sh"
    echo "4. Check logs: docker-compose logs -f"
}

# Handle command line arguments
case "$1" in
    "database")
        optimize_database
        ;;
    "redis")
        optimize_redis
        ;;
    "docker")
        optimize_docker
        ;;
    "monitor")
        setup_monitoring
        ;;
    "test")
        run_performance_tests
        ;;
    "cleanup")
        cleanup_system
        ;;
    *)
        main "$@"
        ;;
esac
