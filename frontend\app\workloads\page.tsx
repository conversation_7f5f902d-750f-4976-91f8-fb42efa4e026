'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { 
  PlusIcon, 
  PlayIcon, 
  PencilIcon, 
  TrashIcon,
  ChartBarIcon,
  ClockIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline'

interface Workload {
  id: number
  name: string
  workload_type: string
  description: string
  config: any
  is_active: boolean
  created_at: string
  updated_at: string
}

export default function WorkloadsPage() {
  const router = useRouter()
  const [workloads, setWorkloads] = useState<Workload[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [filter, setFilter] = useState('all')

  useEffect(() => {
    fetchWorkloads()
  }, [])

  const fetchWorkloads = async () => {
    try {
      const token = localStorage.getItem('token')
      const response = await fetch('/api/v1/workloads/', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })
      
      if (response.ok) {
        const data = await response.json()
        setWorkloads(data.workloads || [])
      }
    } catch (error) {
      console.error('Error fetching workloads:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleRunBenchmark = async (workloadId: number) => {
    try {
      const token = localStorage.getItem('token')
      const response = await fetch(`/api/v1/benchmarks/run/${workloadId}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })
      
      if (response.ok) {
        alert('Benchmark iniciado com sucesso!')
      }
    } catch (error) {
      console.error('Error running benchmark:', error)
      alert('Erro ao iniciar benchmark')
    }
  }

  const handleDeleteWorkload = async (workloadId: number) => {
    if (!confirm('Tem certeza que deseja excluir este workload?')) return

    try {
      const token = localStorage.getItem('token')
      const response = await fetch(`/api/v1/workloads/${workloadId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })
      
      if (response.ok) {
        fetchWorkloads() // Refresh list
      }
    } catch (error) {
      console.error('Error deleting workload:', error)
      alert('Erro ao excluir workload')
    }
  }

  const filteredWorkloads = workloads.filter(workload => {
    if (filter === 'active') return workload.is_active
    if (filter === 'inactive') return !workload.is_active
    return true
  })

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pt-BR')
  }

  const getWorkloadTypeIcon = (type: string) => {
    switch (type) {
      case 'mergesort':
        return '🔄'
      case '3sat':
        return '🧮'
      case 'vec2vec':
        return '📊'
      case 'mlmodel':
        return '🤖'
      default:
        return '⚡'
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center py-4 lg:py-6 space-y-4 sm:space-y-0">
            <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">Workloads</h1>
            <button
              onClick={() => router.push('/workloads/new')}
              className="btn-primary flex items-center justify-center sm:justify-start"
            >
              <PlusIcon className="h-5 w-5 mr-2" />
              Novo Workload
            </button>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8">
        {/* Filters */}
        <div className="mb-6">
          <div className="flex flex-wrap gap-2 sm:gap-4">
            <button
              onClick={() => setFilter('all')}
              className={`px-3 py-2 text-sm rounded-lg ${
                filter === 'all' 
                  ? 'bg-primary-600 text-white' 
                  : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'
              }`}
            >
              Todos ({workloads.length})
            </button>
            <button
              onClick={() => setFilter('active')}
              className={`px-3 py-2 text-sm rounded-lg ${
                filter === 'active' 
                  ? 'bg-primary-600 text-white' 
                  : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'
              }`}
            >
              Ativos ({workloads.filter(w => w.is_active).length})
            </button>
            <button
              onClick={() => setFilter('inactive')}
              className={`px-3 py-2 text-sm rounded-lg ${
                filter === 'inactive' 
                  ? 'bg-primary-600 text-white' 
                  : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'
              }`}
            >
              Inativos ({workloads.filter(w => !w.is_active).length})
            </button>
          </div>
        </div>

        {/* Workloads Grid */}
        {isLoading ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="card animate-pulse">
                <div className="h-4 bg-gray-300 rounded mb-3"></div>
                <div className="h-3 bg-gray-300 rounded mb-2"></div>
                <div className="h-3 bg-gray-300 rounded w-2/3"></div>
              </div>
            ))}
          </div>
        ) : filteredWorkloads.length === 0 ? (
          <div className="text-center py-12">
            <ChartBarIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">Nenhum workload encontrado</h3>
            <p className="mt-1 text-sm text-gray-500">
              {filter === 'all' 
                ? 'Comece criando seu primeiro workload.'
                : `Nenhum workload ${filter === 'active' ? 'ativo' : 'inativo'} encontrado.`
              }
            </p>
            {filter === 'all' && (
              <div className="mt-6">
                <button
                  onClick={() => router.push('/workloads/new')}
                  className="btn-primary"
                >
                  Criar Workload
                </button>
              </div>
            )}
          </div>
        ) : (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
            {filteredWorkloads.map((workload) => (
              <div key={workload.id} className="card">
                <div className="flex items-start justify-between mb-3">
                  <div className="flex items-center">
                    <span className="text-2xl mr-3">{getWorkloadTypeIcon(workload.workload_type)}</span>
                    <div>
                      <h3 className="font-medium text-gray-900 text-sm sm:text-base">{workload.name}</h3>
                      <p className="text-xs sm:text-sm text-gray-500 capitalize">{workload.workload_type}</p>
                    </div>
                  </div>
                  <span className={`px-2 py-1 text-xs rounded-full ${
                    workload.is_active 
                      ? 'bg-green-100 text-green-800' 
                      : 'bg-gray-100 text-gray-800'
                  }`}>
                    {workload.is_active ? 'Ativo' : 'Inativo'}
                  </span>
                </div>

                <p className="text-xs sm:text-sm text-gray-600 mb-4 line-clamp-2">
                  {workload.description || 'Sem descrição'}
                </p>

                <div className="flex flex-col space-y-2">
                  <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2">
                    <button
                      onClick={() => router.push(`/workloads/${workload.id}`)}
                      className="btn-secondary text-sm py-2 px-3 flex items-center justify-center"
                    >
                      <ChartBarIcon className="h-4 w-4 mr-1" />
                      Ver Detalhes
                    </button>
                    <button
                      onClick={() => handleRunBenchmark(workload.id)}
                      className="btn-primary text-sm py-2 px-3 flex items-center justify-center"
                      disabled={!workload.is_active}
                    >
                      <PlayIcon className="h-4 w-4 mr-1" />
                      Executar
                    </button>
                  </div>
                  
                  <div className="flex justify-between items-center pt-2 border-t border-gray-200">
                    <span className="text-xs text-gray-500">
                      {formatDate(workload.created_at)}
                    </span>
                    <div className="flex space-x-1">
                      <button
                        onClick={() => router.push(`/workloads/${workload.id}/edit`)}
                        className="p-1 text-gray-400 hover:text-gray-600"
                        title="Editar"
                      >
                        <PencilIcon className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => handleDeleteWorkload(workload.id)}
                        className="p-1 text-gray-400 hover:text-red-600"
                        title="Excluir"
                      >
                        <TrashIcon className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}
