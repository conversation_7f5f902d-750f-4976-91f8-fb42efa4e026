"""
Configuration settings for Exo Piper
"""

from pydantic_settings import BaseSettings
from typing import List, Optional
import os


class Settings(BaseSettings):
    """Application settings"""
    
    # Basic app settings
    APP_NAME: str = "Exo Piper"
    VERSION: str = "1.0.0"
    ENVIRONMENT: str = "development"
    DEBUG: bool = True
    
    # Security
    SECRET_KEY: str = "your-secret-key-change-in-production"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    REFRESH_TOKEN_EXPIRE_DAYS: int = 7
    ALGORITHM: str = "HS256"
    
    # Database
    DATABASE_URL: str = "postgresql://exoPiper:exoPiper_dev@localhost:5432/exoPiper"
    
    # Redis
    REDIS_URL: str = "redis://localhost:6379/0"
    
    # MinIO/S3
    MINIO_ENDPOINT: str = "localhost:9000"
    MINIO_ACCESS_KEY: str = "exoPiper"
    MINIO_SECRET_KEY: str = "exoPiper_dev"
    MINIO_SECURE: bool = False
    BUCKET_NAME: str = "exoPiper-storage"
    
    # CORS
    ALLOWED_HOSTS: List[str] = ["http://localhost:3000", "http://localhost:8000"]
    
    # Celery
    CELERY_BROKER_URL: str = "redis://localhost:6379/0"
    CELERY_RESULT_BACKEND: str = "redis://localhost:6379/0"
    
    # Docker
    DOCKER_SOCKET: str = "/var/run/docker.sock"
    BENCHMARK_IMAGE: str = "exoPiper/agent:latest"
    
    # Billing & Plans
    STRIPE_SECRET_KEY: Optional[str] = None
    STRIPE_WEBHOOK_SECRET: Optional[str] = None
    
    # Plan limits
    FREE_PLAN_WORKLOADS: int = 2
    FREE_PLAN_RETENTION_DAYS: int = 7
    PRO_PLAN_WORKLOADS: int = 5
    PRO_PLAN_RETENTION_DAYS: int = 30
    TEAM_PLAN_WORKLOADS: int = 20
    TEAM_PLAN_RETENTION_DAYS: int = 90
    
    # Performance thresholds
    DELTA_P_THRESHOLD: float = 0.05  # Threshold for p exponent variation
    DELTA_LAMBDA_THRESHOLD: float = 0.1  # Threshold for λ variation
    
    # Email settings (for alerts)
    SMTP_HOST: Optional[str] = None
    SMTP_PORT: int = 587
    SMTP_USER: Optional[str] = None
    SMTP_PASSWORD: Optional[str] = None
    
    # Slack webhook for alerts
    SLACK_WEBHOOK_URL: Optional[str] = None
    
    class Config:
        env_file = ".env"
        case_sensitive = True


# Global settings instance
settings = Settings()
