#!/bin/bash

# Exo Piper Backup System
# Automated backup script for database, files, and configurations

set -e

echo "💾 Exo Piper Backup System"
echo "=========================="

# Configuration
BACKUP_DIR="/var/backups/exopiper"
RETENTION_DAYS=30
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
BACKUP_NAME="exopiper_backup_$TIMESTAMP"

# Colors
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Create backup directory
create_backup_dir() {
    log_info "Creating backup directory..."
    
    mkdir -p "$BACKUP_DIR/$BACKUP_NAME"
    cd "$BACKUP_DIR/$BACKUP_NAME"
    
    log_info "Backup directory: $BACKUP_DIR/$BACKUP_NAME"
}

# Backup PostgreSQL database
backup_database() {
    log_info "Backing up PostgreSQL database..."
    
    # Get database password from environment
    if [ -f ".env.production" ]; then
        source .env.production
    fi
    
    # Create database dump
    docker-compose -f docker-compose.production.yml exec -T postgres pg_dump \
        -U exoPiper \
        -d exoPiper \
        --no-password \
        --clean \
        --if-exists \
        > database_backup.sql
    
    # Compress the dump
    gzip database_backup.sql
    
    log_info "Database backup completed: database_backup.sql.gz"
}

# Backup MinIO data
backup_minio() {
    log_info "Backing up MinIO object storage..."
    
    # Create MinIO data backup
    docker run --rm \
        --volumes-from $(docker-compose -f docker-compose.production.yml ps -q minio) \
        -v "$BACKUP_DIR/$BACKUP_NAME:/backup" \
        alpine:latest \
        tar czf /backup/minio_data.tar.gz -C /data .
    
    log_info "MinIO backup completed: minio_data.tar.gz"
}

# Backup Redis data
backup_redis() {
    log_info "Backing up Redis data..."
    
    # Create Redis backup
    docker-compose -f docker-compose.production.yml exec -T redis redis-cli BGSAVE
    
    # Wait for backup to complete
    sleep 5
    
    # Copy the dump file
    docker run --rm \
        --volumes-from $(docker-compose -f docker-compose.production.yml ps -q redis) \
        -v "$BACKUP_DIR/$BACKUP_NAME:/backup" \
        alpine:latest \
        cp /data/dump.rdb /backup/redis_dump.rdb
    
    log_info "Redis backup completed: redis_dump.rdb"
}

# Backup configuration files
backup_configs() {
    log_info "Backing up configuration files..."
    
    # Create configs directory
    mkdir -p configs
    
    # Copy important configuration files
    cp ../../.env.production configs/ 2>/dev/null || log_warning ".env.production not found"
    cp ../../docker-compose.production.yml configs/ 2>/dev/null || log_warning "docker-compose.production.yml not found"
    cp -r ../../nginx configs/ 2>/dev/null || log_warning "nginx config not found"
    
    # Create a system info file
    cat > configs/system_info.txt << EOF
Backup Date: $(date)
System: $(uname -a)
Docker Version: $(docker --version)
Docker Compose Version: $(docker-compose --version)
Disk Usage: $(df -h /)
Memory: $(free -h)

Running Services:
$(docker-compose -f ../../docker-compose.production.yml ps)

Docker Images:
$(docker images --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}")
EOF
    
    log_info "Configuration backup completed"
}

# Backup application logs
backup_logs() {
    log_info "Backing up application logs..."
    
    mkdir -p logs
    
    # Export logs from all services
    services=("backend" "frontend" "celery" "celery-beat" "celery-payments" "nginx" "postgres" "redis" "minio")
    
    for service in "${services[@]}"; do
        log_info "Exporting logs for $service..."
        docker-compose -f ../../docker-compose.production.yml logs --no-color "$service" > "logs/${service}.log" 2>/dev/null || log_warning "Could not export logs for $service"
    done
    
    log_info "Logs backup completed"
}

# Create backup archive
create_archive() {
    log_info "Creating backup archive..."
    
    cd "$BACKUP_DIR"
    
    # Create compressed archive
    tar czf "${BACKUP_NAME}.tar.gz" "$BACKUP_NAME"
    
    # Remove uncompressed directory
    rm -rf "$BACKUP_NAME"
    
    # Calculate archive size
    ARCHIVE_SIZE=$(du -h "${BACKUP_NAME}.tar.gz" | cut -f1)
    
    log_info "Backup archive created: ${BACKUP_NAME}.tar.gz ($ARCHIVE_SIZE)"
}

# Clean old backups
cleanup_old_backups() {
    log_info "Cleaning up old backups (keeping last $RETENTION_DAYS days)..."
    
    cd "$BACKUP_DIR"
    
    # Find and delete old backups
    find . -name "exopiper_backup_*.tar.gz" -type f -mtime +$RETENTION_DAYS -delete
    
    # Count remaining backups
    BACKUP_COUNT=$(find . -name "exopiper_backup_*.tar.gz" -type f | wc -l)
    
    log_info "Cleanup completed. $BACKUP_COUNT backups remaining."
}

# Verify backup integrity
verify_backup() {
    log_info "Verifying backup integrity..."
    
    cd "$BACKUP_DIR"
    
    # Test archive integrity
    if tar tzf "${BACKUP_NAME}.tar.gz" > /dev/null 2>&1; then
        log_info "Backup archive integrity verified"
    else
        log_error "Backup archive is corrupted!"
        exit 1
    fi
}

# Send backup notification
send_notification() {
    log_info "Sending backup notification..."
    
    # Calculate total backup size
    TOTAL_SIZE=$(du -sh "$BACKUP_DIR" | cut -f1)
    
    # Create notification message
    MESSAGE="✅ Exo Piper Backup Completed
    
📅 Date: $(date)
📦 Archive: ${BACKUP_NAME}.tar.gz
💾 Size: $ARCHIVE_SIZE
🗂️ Total Backups: $BACKUP_COUNT
💿 Total Size: $TOTAL_SIZE

Components backed up:
• PostgreSQL database
• MinIO object storage
• Redis cache
• Configuration files
• Application logs"

    # Send to Slack if webhook is configured
    if [ -n "$SLACK_WEBHOOK_URL" ]; then
        curl -X POST -H 'Content-type: application/json' \
            --data "{\"text\":\"$MESSAGE\"}" \
            "$SLACK_WEBHOOK_URL" > /dev/null 2>&1 || log_warning "Failed to send Slack notification"
    fi
    
    # Log notification
    echo "$MESSAGE" > "$BACKUP_DIR/last_backup.log"
    
    log_info "Backup notification sent"
}

# Restore function
restore_backup() {
    local backup_file="$1"
    
    if [ -z "$backup_file" ]; then
        log_error "Please specify backup file to restore"
        echo "Usage: $0 restore /path/to/backup.tar.gz"
        exit 1
    fi
    
    if [ ! -f "$backup_file" ]; then
        log_error "Backup file not found: $backup_file"
        exit 1
    fi
    
    log_warning "This will restore from backup and may overwrite current data!"
    read -p "Are you sure you want to continue? (yes/no): " confirm
    
    if [ "$confirm" != "yes" ]; then
        log_info "Restore cancelled"
        exit 0
    fi
    
    log_info "Restoring from backup: $backup_file"
    
    # Extract backup
    RESTORE_DIR="/tmp/exopiper_restore_$(date +%s)"
    mkdir -p "$RESTORE_DIR"
    tar xzf "$backup_file" -C "$RESTORE_DIR"
    
    # Stop services
    log_info "Stopping services..."
    docker-compose -f docker-compose.production.yml down
    
    # Restore database
    log_info "Restoring database..."
    docker-compose -f docker-compose.production.yml up -d postgres
    sleep 10
    
    gunzip -c "$RESTORE_DIR"/*/database_backup.sql.gz | \
        docker-compose -f docker-compose.production.yml exec -T postgres psql -U exoPiper -d exoPiper
    
    # Restore MinIO data
    log_info "Restoring MinIO data..."
    docker-compose -f docker-compose.production.yml up -d minio
    sleep 10
    
    docker run --rm \
        --volumes-from $(docker-compose -f docker-compose.production.yml ps -q minio) \
        -v "$RESTORE_DIR:/restore" \
        alpine:latest \
        sh -c "rm -rf /data/* && tar xzf /restore/*/minio_data.tar.gz -C /data"
    
    # Restore Redis data
    log_info "Restoring Redis data..."
    docker-compose -f docker-compose.production.yml up -d redis
    sleep 5
    
    docker run --rm \
        --volumes-from $(docker-compose -f docker-compose.production.yml ps -q redis) \
        -v "$RESTORE_DIR:/restore" \
        alpine:latest \
        cp /restore/*/redis_dump.rdb /data/dump.rdb
    
    # Restart all services
    log_info "Starting all services..."
    docker-compose -f docker-compose.production.yml up -d
    
    # Cleanup
    rm -rf "$RESTORE_DIR"
    
    log_info "Restore completed successfully!"
}

# List available backups
list_backups() {
    log_info "Available backups in $BACKUP_DIR:"
    
    if [ ! -d "$BACKUP_DIR" ]; then
        log_warning "Backup directory does not exist"
        return
    fi
    
    cd "$BACKUP_DIR"
    
    echo "Date       Time     Size    Filename"
    echo "----------------------------------------"
    
    for backup in exopiper_backup_*.tar.gz; do
        if [ -f "$backup" ]; then
            # Extract date and time from filename
            datetime=$(echo "$backup" | sed 's/exopiper_backup_\([0-9]\{8\}\)_\([0-9]\{6\}\).*/\1 \2/')
            date_part=$(echo "$datetime" | cut -d' ' -f1)
            time_part=$(echo "$datetime" | cut -d' ' -f2)
            
            # Format date and time
            formatted_date=$(echo "$date_part" | sed 's/\([0-9]\{4\}\)\([0-9]\{2\}\)\([0-9]\{2\}\)/\1-\2-\3/')
            formatted_time=$(echo "$time_part" | sed 's/\([0-9]\{2\}\)\([0-9]\{2\}\)\([0-9]\{2\}\)/\1:\2:\3/')
            
            # Get file size
            size=$(du -h "$backup" | cut -f1)
            
            printf "%-10s %-8s %-7s %s\n" "$formatted_date" "$formatted_time" "$size" "$backup"
        fi
    done
}

# Main function
main() {
    case "$1" in
        "restore")
            restore_backup "$2"
            ;;
        "list")
            list_backups
            ;;
        *)
            # Load environment variables
            if [ -f ".env.production" ]; then
                source .env.production
            fi
            
            # Run backup
            create_backup_dir
            backup_database
            backup_minio
            backup_redis
            backup_configs
            backup_logs
            create_archive
            verify_backup
            cleanup_old_backups
            send_notification
            
            log_info "Backup completed successfully!"
            log_info "Backup location: $BACKUP_DIR/${BACKUP_NAME}.tar.gz"
            ;;
    esac
}

# Check if running as root
if [[ $EUID -eq 0 ]]; then
   log_warning "Running as root. Consider using a dedicated backup user."
fi

# Run main function
main "$@"
