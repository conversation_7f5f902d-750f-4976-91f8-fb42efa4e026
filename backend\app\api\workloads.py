"""
Workloads API endpoints
"""

from fastapi import API<PERSON>outer, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func
from typing import List

from ..core.database import get_db
from ..core.security import get_current_user
from ..models.user import User
from ..models.workload import Workload
from ..schemas.workload import WorkloadCreate, WorkloadUpdate, WorkloadResponse, WorkloadListResponse

router = APIRouter()


@router.post("/", response_model=WorkloadResponse)
async def create_workload(
    workload_data: WorkloadCreate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Create a new workload"""
    
    # Check plan limits
    result = await db.execute(
        select(func.count(Workload.id)).where(
            (Workload.user_id == current_user.id) & (Workload.is_active == True)
        )
    )
    workload_count = result.scalar()
    
    # Plan limits check
    from ..core.config import settings
    if current_user.plan_type.value == "free" and workload_count >= settings.FREE_PLAN_WORKLOADS:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=f"Free plan limited to {settings.FREE_PLAN_WORKLOADS} workloads"
        )
    elif current_user.plan_type.value == "pro" and workload_count >= settings.PRO_PLAN_WORKLOADS:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=f"Pro plan limited to {settings.PRO_PLAN_WORKLOADS} workloads"
        )
    elif current_user.plan_type.value == "team" and workload_count >= settings.TEAM_PLAN_WORKLOADS:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=f"Team plan limited to {settings.TEAM_PLAN_WORKLOADS} workloads"
        )
    
    # Create workload
    new_workload = Workload(
        user_id=current_user.id,
        name=workload_data.name,
        description=workload_data.description,
        workload_type=workload_data.workload_type,
        config=workload_data.config,
        schedule_enabled=workload_data.schedule_enabled,
        schedule_cron=workload_data.schedule_cron
    )
    
    db.add(new_workload)
    await db.commit()
    await db.refresh(new_workload)
    
    return new_workload


@router.get("/", response_model=WorkloadListResponse)
async def list_workloads(
    page: int = Query(1, ge=1),
    per_page: int = Query(10, ge=1, le=100),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """List user's workloads"""
    
    # Get total count
    count_result = await db.execute(
        select(func.count(Workload.id)).where(Workload.user_id == current_user.id)
    )
    total = count_result.scalar()
    
    # Get workloads with pagination
    offset = (page - 1) * per_page
    result = await db.execute(
        select(Workload)
        .where(Workload.user_id == current_user.id)
        .order_by(Workload.created_at.desc())
        .offset(offset)
        .limit(per_page)
    )
    workloads = result.scalars().all()
    
    return WorkloadListResponse(
        workloads=workloads,
        total=total,
        page=page,
        per_page=per_page
    )


@router.get("/{workload_id}", response_model=WorkloadResponse)
async def get_workload(
    workload_id: int,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get a specific workload"""
    
    result = await db.execute(
        select(Workload).where(
            (Workload.id == workload_id) & (Workload.user_id == current_user.id)
        )
    )
    workload = result.scalar_one_or_none()
    
    if not workload:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Workload not found"
        )
    
    return workload


@router.put("/{workload_id}", response_model=WorkloadResponse)
async def update_workload(
    workload_id: int,
    workload_data: WorkloadUpdate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Update a workload"""
    
    result = await db.execute(
        select(Workload).where(
            (Workload.id == workload_id) & (Workload.user_id == current_user.id)
        )
    )
    workload = result.scalar_one_or_none()
    
    if not workload:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Workload not found"
        )
    
    # Update fields
    update_data = workload_data.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(workload, field, value)
    
    await db.commit()
    await db.refresh(workload)
    
    return workload


@router.delete("/{workload_id}")
async def delete_workload(
    workload_id: int,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Delete a workload"""
    
    result = await db.execute(
        select(Workload).where(
            (Workload.id == workload_id) & (Workload.user_id == current_user.id)
        )
    )
    workload = result.scalar_one_or_none()
    
    if not workload:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Workload not found"
        )
    
    await db.delete(workload)
    await db.commit()
    
    return {"message": "Workload deleted successfully"}
