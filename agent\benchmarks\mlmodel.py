"""
Machine Learning Model benchmark implementation
"""

import numpy as np
from sklearn.linear_model import LinearRegression, LogisticRegression
from sklearn.tree import DecisionTreeClassifier
from sklearn.ensemble import RandomForestClassifier
from sklearn.neural_network import MLPClassifier
from sklearn.datasets import make_classification, make_regression
from typing import List, Dict, Any
from .base import BaseBenchmark


class MLModelBenchmark(BaseBenchmark):
    """Machine Learning model training and inference benchmark"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.validate_config(['max_features'])
        
        # Supported model types
        self.model_types = self.config.get('model_types', ['linear', 'tree', 'neural'])
        
    def get_algorithm_name(self) -> str:
        return "mlmodel"
    
    def generate_classification_data(self, n_samples: int, n_features: int) -> tuple:
        """Generate synthetic classification dataset"""
        X, y = make_classification(
            n_samples=n_samples,
            n_features=n_features,
            n_informative=min(n_features, 10),
            n_redundant=0,
            n_clusters_per_class=1,
            random_state=42
        )
        return X, y
    
    def generate_regression_data(self, n_samples: int, n_features: int) -> tuple:
        """Generate synthetic regression dataset"""
        X, y = make_regression(
            n_samples=n_samples,
            n_features=n_features,
            n_informative=min(n_features, 10),
            noise=0.1,
            random_state=42
        )
        return X, y
    
    def benchmark_linear_model(self, n_features: int) -> None:
        """Benchmark linear regression model"""
        n_samples = max(n_features * 10, 1000)  # Ensure enough samples
        X, y = self.generate_regression_data(n_samples, n_features)
        
        model = LinearRegression()
        model.fit(X, y)
        
        # Make predictions
        predictions = model.predict(X[:100])  # Predict on subset
        
        if len(predictions) != 100:
            raise ValueError(f"Expected 100 predictions, got {len(predictions)}")
    
    def benchmark_logistic_model(self, n_features: int) -> None:
        """Benchmark logistic regression model"""
        n_samples = max(n_features * 10, 1000)
        X, y = self.generate_classification_data(n_samples, n_features)
        
        model = LogisticRegression(max_iter=1000, random_state=42)
        model.fit(X, y)
        
        # Make predictions
        predictions = model.predict(X[:100])
        probabilities = model.predict_proba(X[:100])
        
        if len(predictions) != 100 or probabilities.shape[0] != 100:
            raise ValueError("Prediction shape mismatch")
    
    def benchmark_tree_model(self, n_features: int) -> None:
        """Benchmark decision tree model"""
        n_samples = max(n_features * 10, 1000)
        X, y = self.generate_classification_data(n_samples, n_features)
        
        model = DecisionTreeClassifier(max_depth=10, random_state=42)
        model.fit(X, y)
        
        # Make predictions
        predictions = model.predict(X[:100])
        
        if len(predictions) != 100:
            raise ValueError(f"Expected 100 predictions, got {len(predictions)}")
    
    def benchmark_forest_model(self, n_features: int) -> None:
        """Benchmark random forest model"""
        n_samples = max(n_features * 10, 1000)
        X, y = self.generate_classification_data(n_samples, n_features)
        
        model = RandomForestClassifier(
            n_estimators=10,  # Small number for speed
            max_depth=5,
            random_state=42
        )
        model.fit(X, y)
        
        # Make predictions
        predictions = model.predict(X[:100])
        
        if len(predictions) != 100:
            raise ValueError(f"Expected 100 predictions, got {len(predictions)}")
    
    def benchmark_neural_model(self, n_features: int) -> None:
        """Benchmark neural network model"""
        n_samples = max(n_features * 10, 1000)
        X, y = self.generate_classification_data(n_samples, n_features)
        
        # Small network for reasonable training time
        hidden_layer_size = min(n_features, 50)
        
        model = MLPClassifier(
            hidden_layer_sizes=(hidden_layer_size,),
            max_iter=100,  # Limited iterations for speed
            random_state=42
        )
        model.fit(X, y)
        
        # Make predictions
        predictions = model.predict(X[:100])
        
        if len(predictions) != 100:
            raise ValueError(f"Expected 100 predictions, got {len(predictions)}")
    
    def get_input_sizes(self) -> List[int]:
        """Generate feature counts for benchmarking"""
        max_features = self.config.get('max_features', 1000)
        min_features = self.config.get('min_features', 10)
        step_factor = self.config.get('step_factor', 2)
        
        sizes = []
        current = min_features
        
        while current <= max_features:
            sizes.append(current)
            current = int(current * step_factor)
        
        return sizes
    
    def run_model_benchmark(self, model_type: str, n_features: int) -> Dict[str, Any]:
        """Run benchmark for a specific model type and feature count"""
        
        model_map = {
            'linear': self.benchmark_linear_model,
            'logistic': self.benchmark_logistic_model,
            'tree': self.benchmark_tree_model,
            'forest': self.benchmark_forest_model,
            'neural': self.benchmark_neural_model,
        }
        
        if model_type not in model_map:
            raise ValueError(f"Unknown model type: {model_type}")
        
        benchmark_func = model_map[model_type]
        result = self.run_multiple_iterations(benchmark_func, n_features)
        
        # Add model-specific metadata
        result['metadata'] = {
            'model_type': model_type,
            'n_features': n_features,
            'n_samples': max(n_features * 10, 1000),
            'library': 'scikit-learn'
        }
        
        return result
    
    def run(self) -> List[Dict[str, Any]]:
        """Run ML model benchmark"""
        self.log("Starting ML Model benchmark")
        
        input_sizes = self.get_input_sizes()
        results = []
        
        for model_type in self.model_types:
            self.log(f"Benchmarking model type: {model_type}")
            
            for n_features in input_sizes:
                self.log(f"Running {model_type} with {n_features} features")
                
                try:
                    result = self.run_model_benchmark(model_type, n_features)
                    
                    # Modify algorithm name to include model type
                    result['algorithm'] = f"mlmodel_{model_type}"
                    
                    results.append(result)
                    
                    self.log(f"Completed {model_type}({n_features}): {result['execution_time_ns']} ns")
                    
                except Exception as e:
                    self.log(f"Error with {model_type}({n_features}): {e}")
                    continue
        
        self.log(f"ML Model benchmark completed: {len(results)} data points")
        return results
