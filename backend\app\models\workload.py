"""
Workload model for benchmark configurations
"""

from sqlalchemy import <PERSON>umn, Integer, String, Text, Boolean, DateTime, ForeignKey, JSON
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship

from ..core.database import Base


class Workload(Base):
    __tablename__ = "workloads"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # Workload identification
    name = Column(String, nullable=False)
    description = Column(Text, nullable=True)
    workload_type = Column(String, nullable=False)  # mergesort, 3sat, mlmodel, etc.
    
    # Configuration
    config = Column(JSON, nullable=False)  # Workload-specific configuration
    is_active = Column(Boolean, default=True)
    
    # Scheduling
    schedule_enabled = Column(Boolean, default=False)
    schedule_cron = Column(String, nullable=True)  # Cron expression
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    user = relationship("User", back_populates="workloads")
    benchmark_jobs = relationship("BenchmarkJob", back_populates="workload")
    
    def __repr__(self):
        return f"<Workload(id={self.id}, name='{self.name}', type='{self.workload_type}')>"
