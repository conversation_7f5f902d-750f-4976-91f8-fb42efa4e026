"""
Exo Piper Benchmark Agent
Main entry point for running benchmarks in Docker containers
"""

import os
import sys
import json
import time
import traceback
from typing import Dict, Any, List
from pathlib import Path

from hardware_profiler import HardwareProfiler, get_docker_stats
from benchmarks.mergesort import MergeSortBenchmark
from benchmarks.sat3 import SAT3Benchmark
from benchmarks.vec2vec import Vec2VecBenchmark
from benchmarks.mlmodel import MLModelBenchmark


class BenchmarkAgent:
    """Main benchmark execution agent"""
    
    def __init__(self):
        self.profiler = HardwareProfiler()
        self.output_dir = Path("/app/output")
        self.output_dir.mkdir(exist_ok=True)
        
        # Available benchmarks
        self.benchmarks = {
            "mergesort": MergeSortBenchmark,
            "3sat": SAT3Benchmark,
            "vec2vec": Vec2VecBenchmark,
            "mlmodel": MLModelBenchmark,
        }
    
    def run_benchmark(self, workload_type: str, config: Dict[str, Any]) -> Dict[str, Any]:
        """Run a specific benchmark with given configuration"""
        
        if workload_type not in self.benchmarks:
            raise ValueError(f"Unknown benchmark type: {workload_type}")
        
        print(f"🚀 Starting {workload_type} benchmark...")
        
        # Get hardware profile
        hardware_profile = self.profiler.get_full_profile()
        docker_stats = get_docker_stats()
        
        # Measure overhead
        overhead_stats = self.profiler.measure_overhead()
        
        try:
            # Initialize benchmark
            benchmark_class = self.benchmarks[workload_type]
            benchmark = benchmark_class(config)
            
            # Run benchmark
            start_time = time.perf_counter_ns()
            results = benchmark.run()
            end_time = time.perf_counter_ns()
            
            total_duration_ns = end_time - start_time
            
            # Apply overhead correction to results
            for result in results:
                if 'execution_time_ns' in result:
                    result['clean_time_ns'] = max(
                        result['execution_time_ns'] - overhead_stats['total_overhead_ns'],
                        result['execution_time_ns'] * 0.1  # Minimum 10% of original time
                    )
            
            # Prepare output
            output = {
                "status": "success",
                "workload_type": workload_type,
                "config": config,
                "results": results,
                "hardware_profile": hardware_profile,
                "docker_stats": docker_stats,
                "overhead_stats": overhead_stats,
                "total_duration_ns": total_duration_ns,
                "timestamp": time.time(),
                "logs": benchmark.get_logs() if hasattr(benchmark, 'get_logs') else [],
                "artifacts": {
                    "benchmark_config": config,
                    "hardware_profile": hardware_profile,
                    "overhead_correction": overhead_stats,
                    "execution_summary": {
                        "total_data_points": len(results),
                        "total_duration_seconds": total_duration_ns / 1e9,
                        "average_time_per_point": total_duration_ns / len(results) if results else 0,
                    }
                }
            }
            
            print(f"✅ {workload_type} benchmark completed: {len(results)} data points")
            return output
            
        except Exception as e:
            error_output = {
                "status": "error",
                "workload_type": workload_type,
                "config": config,
                "error": str(e),
                "traceback": traceback.format_exc(),
                "hardware_profile": hardware_profile,
                "docker_stats": docker_stats,
                "timestamp": time.time(),
            }
            
            print(f"❌ {workload_type} benchmark failed: {e}")
            return error_output
    
    def save_output(self, output: Dict[str, Any], filename: str = "benchmark_output.json"):
        """Save benchmark output to file"""
        output_file = self.output_dir / filename
        with open(output_file, 'w') as f:
            json.dump(output, f, indent=2, default=str)
        print(f"📁 Output saved to {output_file}")


def main():
    """Main entry point"""
    
    # Get configuration from environment variables
    workload_type = os.environ.get('WORKLOAD_TYPE', 'mergesort')
    config_json = os.environ.get('WORKLOAD_CONFIG', '{}')
    job_id = os.environ.get('JOB_ID', 'unknown')
    
    try:
        config = json.loads(config_json)
    except json.JSONDecodeError as e:
        print(f"❌ Invalid configuration JSON: {e}")
        sys.exit(1)
    
    print(f"🎯 Job ID: {job_id}")
    print(f"🔧 Workload: {workload_type}")
    print(f"⚙️  Config: {config}")
    
    # Initialize agent
    agent = BenchmarkAgent()
    
    # Run benchmark
    output = agent.run_benchmark(workload_type, config)
    
    # Save output
    output_filename = f"benchmark_{job_id}_{workload_type}.json"
    agent.save_output(output, output_filename)
    
    # Print results summary to stdout for Docker logs
    if output['status'] == 'success':
        print(f"\n📊 BENCHMARK RESULTS:")
        print(f"Results: {len(output['results'])} data points")
        print(f"Duration: {output['total_duration_ns'] / 1e9:.2f} seconds")
        
        # Print results in JSON format for parsing
        print(f"\n🔍 JSON_OUTPUT_START")
        print(json.dumps(output, default=str))
        print(f"JSON_OUTPUT_END")
        
        sys.exit(0)
    else:
        print(f"\n❌ BENCHMARK FAILED:")
        print(f"Error: {output['error']}")
        sys.exit(1)


if __name__ == "__main__":
    main()
