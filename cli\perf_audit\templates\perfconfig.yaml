# Exo Piper Performance Auditor Configuration
# This file defines the workloads to be benchmarked

workloads:
  # Merge Sort Algorithm Benchmark
  mergesort:
    type: mergesort
    description: "Benchmark merge sort algorithm with varying input sizes"
    config:
      max_size: 100000        # Maximum array size to test
      iterations: 5           # Number of iterations per size
      step_factor: 2          # Multiply size by this factor each step
      
  # 3-SAT Problem Benchmark  
  3sat:
    type: 3sat
    description: "Benchmark 3-SAT solver with varying number of variables"
    config:
      max_variables: 20       # Maximum number of variables
      iterations: 3           # Number of iterations per size
      clause_ratio: 4.3       # Clauses per variable ratio
      
  # Vector-to-Vector Operations
  vec2vec:
    type: vec2vec
    description: "Benchmark vector operations (dot product, matrix multiply)"
    config:
      max_dimension: 1000     # Maximum vector/matrix dimension
      iterations: 10          # Number of iterations per size
      operations: ["dot", "matmul", "norm"]
      
  # Machine Learning Model Inference
  mlmodel:
    type: mlmodel
    description: "Benchmark ML model inference times"
    config:
      model_types: ["linear", "tree", "neural"]
      max_features: 1000      # Maximum number of features
      iterations: 5           # Number of iterations per configuration
      
# Global settings
settings:
  # Hardware detection
  detect_hardware: true
  
  # Overhead measurement
  measure_overhead: true
  overhead_iterations: 100
  
  # Results processing
  apply_log_regression: true
  min_data_points: 5
  
  # Thresholds for alerts
  delta_p_threshold: 0.05     # Alert if p exponent changes by more than this
  delta_lambda_threshold: 0.1 # Alert if λ constant changes by more than this
  
  # Output options
  save_raw_data: true
  generate_plots: true
  export_formats: ["json", "csv"]
