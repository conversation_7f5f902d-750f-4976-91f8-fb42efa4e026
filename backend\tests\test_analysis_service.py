"""
Tests for complexity analysis service
"""

import pytest
import math
from app.services.analysis_service import ComplexityAnalysisService
from app.models.benchmark import BenchmarkResult


class TestComplexityAnalysisService:
    """Test complexity analysis functionality"""
    
    def setup_method(self):
        """Set up test fixtures"""
        self.analysis_service = ComplexityAnalysisService()
    
    def create_mock_results(self, algorithm_complexity: str = "nlogn", noise_factor: float = 0.1):
        """Create mock benchmark results with known complexity"""
        results = []
        base_time = 1000000  # 1ms in nanoseconds
        
        input_sizes = [100, 200, 400, 800, 1600, 3200]
        
        for size in input_sizes:
            if algorithm_complexity == "linear":
                expected_time = base_time * size
            elif algorithm_complexity == "nlogn":
                expected_time = base_time * size * math.log2(size)
            elif algorithm_complexity == "quadratic":
                expected_time = base_time * size * size / 1000  # Scale down for reasonable numbers
            elif algorithm_complexity == "exponential":
                expected_time = base_time * (2 ** min(size/100, 20))  # Cap to prevent overflow
            else:
                expected_time = base_time * size
            
            # Add some noise
            import random
            noise = random.uniform(-noise_factor, noise_factor)
            actual_time = int(expected_time * (1 + noise))
            
            result = BenchmarkResult(
                job_id=1,
                input_size=size,
                execution_time_ns=actual_time,
                clean_time_ns=actual_time,
                hardware_type="CPU"
            )
            results.append(result)
        
        return results
    
    def test_analyze_linear_complexity(self):
        """Test analysis of linear complexity algorithm"""
        results = self.create_mock_results("linear", noise_factor=0.05)
        
        analysis = self.analysis_service.analyze_complexity(results)
        
        assert analysis["status"] == "success"
        assert "lambda_value" in analysis
        assert "p_exponent" in analysis
        assert "r_squared" in analysis
        
        # For linear complexity, p should be close to 1
        p_exponent = analysis["p_exponent"]
        assert 0.8 <= p_exponent <= 1.2, f"Expected p ≈ 1 for linear, got {p_exponent}"
        
        # R-squared should be high for clean data
        r_squared = analysis["r_squared"]
        assert r_squared >= 0.9, f"Expected high R² for clean data, got {r_squared}"
    
    def test_analyze_nlogn_complexity(self):
        """Test analysis of O(n log n) complexity algorithm"""
        results = self.create_mock_results("nlogn", noise_factor=0.05)
        
        analysis = self.analysis_service.analyze_complexity(results)
        
        assert analysis["status"] == "success"
        
        # For n log n complexity, p should be close to 1.1-1.3
        p_exponent = analysis["p_exponent"]
        assert 1.0 <= p_exponent <= 1.4, f"Expected p ≈ 1.1-1.3 for nlogn, got {p_exponent}"
        
        # R-squared should be high
        r_squared = analysis["r_squared"]
        assert r_squared >= 0.9, f"Expected high R² for clean data, got {r_squared}"
    
    def test_analyze_quadratic_complexity(self):
        """Test analysis of quadratic complexity algorithm"""
        results = self.create_mock_results("quadratic", noise_factor=0.05)
        
        analysis = self.analysis_service.analyze_complexity(results)
        
        assert analysis["status"] == "success"
        
        # For quadratic complexity, p should be close to 2
        p_exponent = analysis["p_exponent"]
        assert 1.8 <= p_exponent <= 2.2, f"Expected p ≈ 2 for quadratic, got {p_exponent}"
    
    def test_insufficient_data_points(self):
        """Test analysis with insufficient data points"""
        results = self.create_mock_results("linear")[:2]  # Only 2 points
        
        analysis = self.analysis_service.analyze_complexity(results)
        
        assert analysis["status"] == "insufficient_data"
        assert "message" in analysis
    
    def test_noisy_data_analysis(self):
        """Test analysis with noisy data"""
        results = self.create_mock_results("nlogn", noise_factor=0.3)  # 30% noise
        
        analysis = self.analysis_service.analyze_complexity(results)
        
        assert analysis["status"] == "success"
        
        # Should still detect approximate complexity
        p_exponent = analysis["p_exponent"]
        assert 0.8 <= p_exponent <= 1.6, f"Expected p in reasonable range for noisy nlogn, got {p_exponent}"
        
        # R-squared should be lower but still meaningful
        r_squared = analysis["r_squared"]
        assert r_squared >= 0.5, f"Expected reasonable R² for noisy data, got {r_squared}"
    
    def test_quality_metrics(self):
        """Test quality metrics calculation"""
        results = self.create_mock_results("nlogn", noise_factor=0.1)
        
        analysis = self.analysis_service.analyze_complexity(results)
        
        assert "quality_metrics" in analysis
        quality = analysis["quality_metrics"]
        
        # Check required quality metrics
        assert "r_squared" in quality
        assert "mape" in quality  # Mean Absolute Percentage Error
        assert "rmse" in quality  # Root Mean Square Error
        assert "confidence_interval" in quality
        
        # Quality metrics should be reasonable
        assert 0 <= quality["r_squared"] <= 1
        assert quality["mape"] >= 0
        assert quality["rmse"] >= 0
    
    def test_performance_insights(self):
        """Test performance insights generation"""
        results = self.create_mock_results("quadratic", noise_factor=0.1)
        
        analysis = self.analysis_service.analyze_complexity(results)
        
        assert "insights" in analysis
        insights = analysis["insights"]
        
        # Should be a list of insight strings
        assert isinstance(insights, list)
        assert len(insights) > 0
        
        # For quadratic complexity, should mention scaling concerns
        insight_text = " ".join(insights).lower()
        assert any(word in insight_text for word in ["quadratic", "scale", "efficiency"])
    
    def test_hardware_constant_calculation(self):
        """Test lambda (hardware constant) calculation"""
        results = self.create_mock_results("linear", noise_factor=0.05)
        
        analysis = self.analysis_service.analyze_complexity(results)
        
        lambda_value = analysis["lambda_value"]
        
        # Lambda should be positive and reasonable
        assert lambda_value > 0
        
        # For our mock data, lambda should be in expected range
        assert 500000 <= lambda_value <= 2000000  # 0.5ms to 2ms per unit
    
    def test_statistical_significance(self):
        """Test statistical significance of results"""
        results = self.create_mock_results("nlogn", noise_factor=0.05)
        
        analysis = self.analysis_service.analyze_complexity(results)
        
        # Should include confidence intervals
        quality = analysis["quality_metrics"]
        assert "confidence_interval" in quality
        
        ci = quality["confidence_interval"]
        assert "p_lower" in ci
        assert "p_upper" in ci
        assert "lambda_lower" in ci
        assert "lambda_upper" in ci
        
        # Confidence intervals should be reasonable
        p_exponent = analysis["p_exponent"]
        assert ci["p_lower"] <= p_exponent <= ci["p_upper"]
    
    def test_outlier_detection(self):
        """Test outlier detection and handling"""
        results = self.create_mock_results("linear", noise_factor=0.05)
        
        # Add an outlier
        outlier = BenchmarkResult(
            job_id=1,
            input_size=1000,
            execution_time_ns=50000000,  # Much higher than expected
            clean_time_ns=50000000,
            hardware_type="CPU"
        )
        results.append(outlier)
        
        analysis = self.analysis_service.analyze_complexity(results)
        
        # Analysis should still succeed and be reasonable
        assert analysis["status"] == "success"
        
        # Should detect and report outliers
        if "outliers_detected" in analysis:
            assert analysis["outliers_detected"] > 0
    
    def test_empty_results(self):
        """Test analysis with empty results"""
        results = []
        
        analysis = self.analysis_service.analyze_complexity(results)
        
        assert analysis["status"] == "insufficient_data"
    
    def test_single_data_point(self):
        """Test analysis with single data point"""
        results = self.create_mock_results("linear")[:1]
        
        analysis = self.analysis_service.analyze_complexity(results)
        
        assert analysis["status"] == "insufficient_data"
