# 💳 Sistema de Pagamento USDT + TANOS

O Exo Piper implementa um sistema de pagamento moderno usando **USDT** (Tether) nas redes **BEP20** (Binance Smart Chain) e **ERC20** (Ethereum), com integração ao **TANOS** para swaps atômicos Bitcoin-Nostr.

## 🎯 **Visão Geral**

### **Wallet de Recebimento**
```
******************************************
```

### **Redes Suportadas**
- **BEP20 (Binance Smart Chain)**
  - Moeda: USDT
  - Taxas: ~$0.20
  - Confirmação: 1-3 minutos
  - Contract: `******************************************`

- **ERC20 (Ethereum)**
  - Moeda: USDT  
  - Taxas: ~$5-20
  - Confirmação: 5-15 minutos
  - Contract: `******************************************`

### **Planos e Preços**
| Plano | Preço/Mês | USDT | Workloads | Jobs/Mês | Retenção |
|-------|------------|------|-----------|----------|----------|
| **Free** | $0 | 0 | 3 | 50 | 30 dias |
| **Basic** | $50 | 50 | 10 | 200 | 90 dias |
| **Pro** | $200 | 200 | 50 | 1000 | 365 dias |
| **Enterprise** | $1000 | 1000 | ∞ | ∞ | ∞ |

## 🔧 **Arquitetura Técnica**

### **Componentes Principais**

1. **PaymentService** (`backend/app/services/payment_service.py`)
   - Criação de solicitações de pagamento
   - Monitoramento de blockchain via APIs
   - Upgrade automático de planos

2. **Modelos de Dados** (`backend/app/models/payment.py`)
   - `Payment`: Pagamentos USDT
   - `TANOSSwap`: Swaps atômicos Bitcoin-Nostr
   - `PaymentWebhook`: Notificações de pagamento

3. **API Endpoints** (`backend/app/api/payments.py`)
   - `POST /api/v1/payments/create` - Criar pagamento
   - `GET /api/v1/payments/status/{id}` - Verificar status
   - `GET /api/v1/payments/history` - Histórico
   - `GET /api/v1/payments/plans` - Planos disponíveis

4. **Frontend** (`frontend/app/upgrade/page.tsx`)
   - Interface de seleção de planos
   - Instruções de pagamento
   - Monitoramento em tempo real

5. **Tasks Celery**
   - Monitoramento automático a cada 5 minutos
   - Limpeza de pagamentos expirados
   - Verificação de confirmações blockchain

## 🚀 **Fluxo de Pagamento**

### **1. Criação do Pagamento**
```typescript
// Frontend
const response = await fetch('/api/v1/payments/create', {
  method: 'POST',
  headers: { 'Authorization': `Bearer ${token}` },
  body: JSON.stringify({
    plan_type: 'basic',
    network: 'bep20'
  })
})
```

### **2. Resposta com Detalhes**
```json
{
  "payment_id": 123,
  "amount": "50.000000",
  "currency": "USDT",
  "network": "BEP20",
  "wallet_address": "******************************************",
  "contract_address": "******************************************",
  "expires_at": "2024-01-15T12:00:00Z",
  "instructions": [...]
}
```

### **3. Monitoramento Automático**
- Task Celery verifica blockchain a cada 5 minutos
- APIs BSCScan/Etherscan para verificar transações
- Upgrade automático quando pagamento confirmado

### **4. Confirmação e Upgrade**
```python
# Verificação automática
tx_hash = await self._check_blockchain_payment(payment)
if tx_hash:
    payment.status = PaymentStatus.CONFIRMED
    await self._upgrade_user_plan(user_id, target_plan)
```

## 🔗 **Integração com TANOS**

### **Swaps Atômicos Bitcoin-Nostr**
O sistema está preparado para integração com TANOS para aceitar pagamentos em Bitcoin através de swaps atômicos:

```python
class TANOSSwap(Base):
    swap_id = Column(String(100), unique=True)
    seller_nostr_pubkey = Column(String(64))  # Nossa chave Nostr
    seller_commitment = Column(String(64))    # Commitment T = R + e*P
    buyer_bitcoin_pubkey = Column(String(66)) # Chave Bitcoin do cliente
    locking_tx_hash = Column(String(64))      # TX Bitcoin de lock
    spending_tx_hash = Column(String(64))     # TX Bitcoin de gasto
    nostr_event_id = Column(String(64))       # Evento Nostr
```

### **Fluxo TANOS (Futuro)**
1. Cliente inicia swap Bitcoin → Acesso Exo Piper
2. Sistema gera commitment Nostr
3. Cliente faz lock de Bitcoin
4. Sistema revela chave privada via Nostr
5. Cliente ganha acesso, sistema recebe Bitcoin

## 🧪 **Testando o Sistema**

### **1. Teste Automático**
```bash
python test-payment-system.py
```

### **2. Teste Manual via API**
```bash
# 1. Login
curl -X POST http://localhost:8000/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"testpassword"}'

# 2. Criar pagamento
curl -X POST http://localhost:8000/api/v1/payments/create \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"plan_type":"basic","network":"bep20"}'

# 3. Verificar status
curl -X GET http://localhost:8000/api/v1/payments/status/123 \
  -H "Authorization: Bearer $TOKEN"
```

### **3. Teste via Frontend**
1. Acesse `http://localhost:3000/upgrade`
2. Selecione um plano
3. Escolha a rede (BEP20 recomendado)
4. Siga as instruções de pagamento

## 📊 **Monitoramento e Logs**

### **Celery Tasks**
```bash
# Monitorar tasks de pagamento
celery -A app.core.celery_app events

# Logs do worker de pagamentos
docker-compose logs celery-payments
```

### **Logs de Pagamento**
```python
# Verificar pagamentos pendentes
async with AsyncSessionLocal() as db:
    pending = await db.execute(
        select(Payment).where(Payment.status == PaymentStatus.PENDING)
    )
```

## 🔒 **Segurança**

### **Validações Implementadas**
- ✅ Verificação de valor exato (±$0.01)
- ✅ Verificação de endereço de destino
- ✅ Verificação de timestamp (após criação do pagamento)
- ✅ Expiração automática em 24h
- ✅ Prevenção de double-spending

### **APIs Blockchain**
- **BSCScan API**: Verificação de transações BEP20
- **Etherscan API**: Verificação de transações ERC20
- **Rate Limiting**: Respeitado para evitar bloqueios

## 🚀 **Deployment**

### **Variáveis de Ambiente**
```bash
# APIs Blockchain (opcionais, mas recomendadas)
BSCSCAN_API_KEY=your_bscscan_api_key
ETHERSCAN_API_KEY=your_etherscan_api_key

# Configuração Celery
CELERY_BROKER_URL=redis://redis:6379/0
CELERY_RESULT_BACKEND=redis://redis:6379/0
```

### **Docker Compose**
```yaml
celery-payments:
  build: ./backend
  command: celery -A app.core.celery_app worker --queues=payments
  environment:
    - BSCSCAN_API_KEY=${BSCSCAN_API_KEY}
    - ETHERSCAN_API_KEY=${ETHERSCAN_API_KEY}
```

## 📈 **Métricas e Analytics**

### **Endpoints de Admin**
- `GET /api/v1/admin/usage/all` - Estatísticas de todos os usuários
- `POST /api/v1/admin/cleanup/all` - Limpeza de dados expirados

### **Métricas Importantes**
- Taxa de conversão de pagamentos
- Tempo médio de confirmação
- Distribuição por rede (BEP20 vs ERC20)
- Planos mais populares

---

## 🎉 **Status Atual**

✅ **90% Implementado**
- [x] Recebimento USDT BEP20/ERC20
- [x] Monitoramento automático blockchain
- [x] Interface de pagamento
- [x] Upgrade automático de planos
- [x] Tasks Celery para monitoramento
- [ ] Integração completa TANOS
- [ ] QR codes para pagamentos
- [ ] Webhooks externos

O sistema está **pronto para uso** em desenvolvimento e pode receber pagamentos reais na wallet configurada! 🚀
