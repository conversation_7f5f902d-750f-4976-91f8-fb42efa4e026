# Build Exo Piper Agent Docker Image (PowerShell)

Write-Host "🐳 Building Exo Piper Agent Docker Image..." -ForegroundColor Cyan

# Navigate to agent directory
Set-Location agent

# Build the Docker image
docker build -t exopiper/agent:latest .

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Agent image built successfully!" -ForegroundColor Green
    Write-Host "📦 Image: exopiper/agent:latest" -ForegroundColor Yellow
    
    # Show image info
    docker images exopiper/agent:latest
    
    Write-Host ""
    Write-Host "🚀 To test the agent:" -ForegroundColor Cyan
    Write-Host "docker run --rm -e WORKLOAD_TYPE=mergesort -e WORKLOAD_CONFIG='{`"max_size`":1000,`"iterations`":3}' -e JOB_ID=test exopiper/agent:latest" -ForegroundColor White
} else {
    Write-Host "❌ Failed to build agent image" -ForegroundColor Red
    exit 1
}
