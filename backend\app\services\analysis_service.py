"""
Complexity Analysis Service - Teorema da Relatividade da Complexidade
"""

import numpy as np
import pandas as pd
from scipy import stats
from typing import List, Dict, Any, Tuple, Optional
from sklearn.linear_model import LinearRegression
from sklearn.metrics import r2_score
import math

from ..models.benchmark import BenchmarkResult


class ComplexityAnalysisService:
    """
    Implementa o Teorema da Relatividade da Complexidade
    
    O teorema estabelece que:
    T(n) = λ * n^p
    
    Onde:
    - T(n) = tempo de execução para entrada de tamanho n
    - λ = constante de hardware (intercepto em escala log)
    - p = expoente algorítmico (inclinação em escala log)
    
    Aplicando log em ambos os lados:
    log(T(n)) = log(λ) + p * log(n)
    
    Isso permite separar efeitos de hardware (λ) dos efeitos algorítmicos (p)
    """
    
    def __init__(self):
        self.min_data_points = 3
        self.confidence_level = 0.95
    
    def analyze_complexity(self, results: List[BenchmarkResult]) -> Dict[str, Any]:
        """
        Analisa complexidade dos resultados de benchmark
        
        Returns:
            Dict contendo λ, p, métricas de qualidade e análise estatística
        """
        
        if len(results) < self.min_data_points:
            return {
                "status": "insufficient_data",
                "message": f"Need at least {self.min_data_points} data points",
                "data_points": len(results)
            }
        
        # Preparar dados para análise
        data = self._prepare_data(results)
        
        if len(data) < self.min_data_points:
            return {
                "status": "insufficient_valid_data",
                "message": "Not enough valid data points after filtering",
                "data_points": len(data)
            }
        
        # Realizar regressão log-log
        regression_result = self._perform_log_log_regression(data)
        
        # Calcular métricas de qualidade
        quality_metrics = self._calculate_quality_metrics(data, regression_result)
        
        # Detectar outliers
        outliers = self._detect_outliers(data, regression_result)
        
        # Análise de hardware
        hardware_analysis = self._analyze_hardware_effects(results)
        
        return {
            "status": "success",
            "lambda_value": regression_result["lambda"],
            "p_exponent": regression_result["p"],
            "log_lambda": regression_result["log_lambda"],
            "r_squared": regression_result["r_squared"],
            "quality_metrics": quality_metrics,
            "outliers": outliers,
            "hardware_analysis": hardware_analysis,
            "data_points": len(data),
            "algorithm_classification": self._classify_algorithm(regression_result["p"]),
            "performance_insights": self._generate_insights(regression_result, quality_metrics)
        }
    
    def _prepare_data(self, results: List[BenchmarkResult]) -> pd.DataFrame:
        """Prepara dados para análise, filtrando valores inválidos"""
        
        data = []
        for result in results:
            # Usar clean_time_ns se disponível, senão execution_time_ns
            time_ns = result.clean_time_ns if result.clean_time_ns else result.execution_time_ns
            
            # Filtrar valores inválidos
            if (result.input_size > 0 and 
                time_ns > 0 and 
                not math.isnan(time_ns) and 
                not math.isinf(time_ns)):
                
                data.append({
                    "input_size": result.input_size,
                    "time_ns": time_ns,
                    "log_n": math.log10(result.input_size),
                    "log_t": math.log10(time_ns),
                    "hardware_type": result.hardware_type,
                    "memory_usage_mb": result.memory_usage_mb or 0
                })
        
        return pd.DataFrame(data)
    
    def _perform_log_log_regression(self, data: pd.DataFrame) -> Dict[str, float]:
        """
        Realiza regressão linear em escala log-log
        
        log(T) = log(λ) + p * log(n)
        """
        
        X = data[["log_n"]].values
        y = data["log_t"].values
        
        # Regressão linear
        model = LinearRegression()
        model.fit(X, y)
        
        # Extrair parâmetros
        p_exponent = model.coef_[0]  # Inclinação = expoente algorítmico
        log_lambda = model.intercept_  # Intercepto = log(λ)
        lambda_value = 10 ** log_lambda  # λ = constante de hardware
        
        # Calcular R²
        y_pred = model.predict(X)
        r_squared = r2_score(y, y_pred)
        
        return {
            "p": p_exponent,
            "lambda": lambda_value,
            "log_lambda": log_lambda,
            "r_squared": r_squared,
            "model": model
        }
    
    def _calculate_quality_metrics(self, data: pd.DataFrame, regression: Dict) -> Dict[str, Any]:
        """Calcula métricas de qualidade da análise"""
        
        model = regression["model"]
        X = data[["log_n"]].values
        y = data["log_t"].values
        y_pred = model.predict(X)
        
        # Erro médio absoluto em escala log
        mae_log = np.mean(np.abs(y - y_pred))
        
        # Erro médio absoluto em escala linear
        t_actual = 10 ** y
        t_pred = 10 ** y_pred
        mae_linear = np.mean(np.abs(t_actual - t_pred))
        
        # Erro percentual médio
        mape = np.mean(np.abs((t_actual - t_pred) / t_actual)) * 100
        
        # Intervalo de confiança para p
        n_points = len(data)
        if n_points > 2:
            # Calcular erro padrão
            residuals = y - y_pred
            mse = np.mean(residuals ** 2)
            se_p = np.sqrt(mse / np.sum((X.flatten() - np.mean(X)) ** 2))
            
            # t-statistic para 95% de confiança
            t_stat = stats.t.ppf(0.975, n_points - 2)
            p_confidence_interval = [
                regression["p"] - t_stat * se_p,
                regression["p"] + t_stat * se_p
            ]
        else:
            p_confidence_interval = [regression["p"], regression["p"]]
        
        return {
            "mae_log": mae_log,
            "mae_linear": mae_linear,
            "mape": mape,
            "p_confidence_interval": p_confidence_interval,
            "data_range": {
                "min_n": int(data["input_size"].min()),
                "max_n": int(data["input_size"].max()),
                "min_time_ns": int(data["time_ns"].min()),
                "max_time_ns": int(data["time_ns"].max())
            }
        }
    
    def _detect_outliers(self, data: pd.DataFrame, regression: Dict) -> List[Dict]:
        """Detecta outliers usando resíduos da regressão"""
        
        model = regression["model"]
        X = data[["log_n"]].values
        y = data["log_t"].values
        y_pred = model.predict(X)
        
        residuals = y - y_pred
        std_residuals = np.std(residuals)
        
        outliers = []
        for i, residual in enumerate(residuals):
            if abs(residual) > 2 * std_residuals:  # 2 desvios padrão
                outliers.append({
                    "index": i,
                    "input_size": int(data.iloc[i]["input_size"]),
                    "time_ns": int(data.iloc[i]["time_ns"]),
                    "residual": residual,
                    "severity": "high" if abs(residual) > 3 * std_residuals else "medium"
                })
        
        return outliers
    
    def _analyze_hardware_effects(self, results: List[BenchmarkResult]) -> Dict[str, Any]:
        """Analisa efeitos de hardware nos resultados"""
        
        hardware_types = {}
        for result in results:
            hw_type = result.hardware_type or "Unknown"
            if hw_type not in hardware_types:
                hardware_types[hw_type] = []
            
            time_ns = result.clean_time_ns if result.clean_time_ns else result.execution_time_ns
            hardware_types[hw_type].append({
                "input_size": result.input_size,
                "time_ns": time_ns,
                "memory_mb": result.memory_usage_mb or 0
            })
        
        # Calcular estatísticas por tipo de hardware
        hw_stats = {}
        for hw_type, hw_results in hardware_types.items():
            if len(hw_results) > 0:
                times = [r["time_ns"] for r in hw_results]
                hw_stats[hw_type] = {
                    "count": len(hw_results),
                    "avg_time_ns": np.mean(times),
                    "std_time_ns": np.std(times),
                    "min_time_ns": np.min(times),
                    "max_time_ns": np.max(times)
                }
        
        return {
            "hardware_types": list(hardware_types.keys()),
            "hardware_stats": hw_stats,
            "dominant_hardware": max(hw_stats.keys(), key=lambda k: hw_stats[k]["count"]) if hw_stats else None
        }
    
    def _classify_algorithm(self, p_exponent: float) -> Dict[str, Any]:
        """Classifica o algoritmo baseado no expoente p"""
        
        classifications = [
            (1.0, "O(n)", "Linear", "Excellent"),
            (1.5, "O(n^1.5)", "Sub-quadratic", "Good"),
            (2.0, "O(n²)", "Quadratic", "Acceptable"),
            (2.5, "O(n^2.5)", "Super-quadratic", "Poor"),
            (3.0, "O(n³)", "Cubic", "Bad"),
            (float('inf'), f"O(n^{p_exponent:.2f})", "Polynomial", "Very Bad")
        ]
        
        for threshold, big_o, category, performance in classifications:
            if p_exponent <= threshold:
                return {
                    "big_o_notation": big_o,
                    "category": category,
                    "performance_rating": performance,
                    "p_exponent": round(p_exponent, 3)
                }
        
        return {
            "big_o_notation": f"O(n^{p_exponent:.2f})",
            "category": "Unknown",
            "performance_rating": "Unknown",
            "p_exponent": round(p_exponent, 3)
        }
    
    def _generate_insights(self, regression: Dict, quality: Dict) -> List[str]:
        """Gera insights baseados na análise"""
        
        insights = []
        
        p = regression["p"]
        r2 = regression["r_squared"]
        
        # Insights sobre qualidade do fit
        if r2 > 0.95:
            insights.append("Excellent fit: The algorithm follows a clear power law relationship")
        elif r2 > 0.85:
            insights.append("Good fit: The algorithm generally follows a power law with some variation")
        elif r2 > 0.70:
            insights.append("Moderate fit: There may be additional factors affecting performance")
        else:
            insights.append("Poor fit: The algorithm may not follow a simple power law")
        
        # Insights sobre complexidade
        if p < 1.2:
            insights.append("Linear or near-linear complexity - very efficient algorithm")
        elif p < 1.8:
            insights.append("Sub-quadratic complexity - reasonably efficient")
        elif p < 2.2:
            insights.append("Quadratic complexity - efficiency decreases with larger inputs")
        else:
            insights.append("High polynomial complexity - may not scale well for large inputs")
        
        # Insights sobre variabilidade
        mape = quality.get("mape", 0)
        if mape < 10:
            insights.append("Low performance variability - consistent execution times")
        elif mape < 25:
            insights.append("Moderate performance variability - some inconsistency in execution")
        else:
            insights.append("High performance variability - execution times are inconsistent")
        
        return insights
    
    def compare_analyses(self, current: Dict, previous: Dict) -> Dict[str, Any]:
        """Compara duas análises para detectar regressões"""
        
        if not all(key in current and key in previous for key in ["p_exponent", "lambda_value"]):
            return {"status": "insufficient_data"}
        
        delta_p = current["p_exponent"] - previous["p_exponent"]
        delta_lambda_pct = ((current["lambda_value"] - previous["lambda_value"]) / 
                           previous["lambda_value"]) * 100
        
        # Determinar significância das mudanças
        p_significant = abs(delta_p) > 0.05  # Threshold configurável
        lambda_significant = abs(delta_lambda_pct) > 10  # 10% change
        
        regression_detected = False
        if delta_p > 0.05:  # Aumento significativo na complexidade
            regression_detected = True
        
        return {
            "status": "comparison_complete",
            "delta_p": delta_p,
            "delta_lambda_percent": delta_lambda_pct,
            "p_significant": p_significant,
            "lambda_significant": lambda_significant,
            "regression_detected": regression_detected,
            "performance_change": self._classify_performance_change(delta_p, delta_lambda_pct)
        }
    
    def _classify_performance_change(self, delta_p: float, delta_lambda_pct: float) -> str:
        """Classifica mudança de performance"""
        
        if delta_p > 0.1:
            return "significant_regression"
        elif delta_p > 0.05:
            return "minor_regression"
        elif delta_p < -0.05:
            return "improvement"
        elif abs(delta_lambda_pct) > 20:
            return "hardware_change"
        else:
            return "stable"
