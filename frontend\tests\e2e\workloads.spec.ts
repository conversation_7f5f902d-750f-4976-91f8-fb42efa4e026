import { test, expect } from '@playwright/test'

test.describe('Workloads Management', () => {
  test.beforeEach(async ({ page }) => {
    // Login before each test
    await page.goto('/')
    await page.fill('input[type="email"]', '<EMAIL>')
    await page.fill('input[type="password"]', 'testpassword')
    await page.click('button[type="submit"]')
    await expect(page).toHaveURL('/dashboard')
  })

  test('should display workloads page', async ({ page }) => {
    await page.click('text=Workloads')
    
    await expect(page).toHaveURL('/workloads')
    await expect(page.locator('h1')).toContainText('Meus Workloads')
    await expect(page.locator('button:has-text("Novo Workload")')).toBeVisible()
  })

  test('should create new workload', async ({ page }) => {
    await page.goto('/workloads')
    await page.click('button:has-text("Novo Workload")')
    
    await expect(page).toHaveURL('/workloads/new')
    await expect(page.locator('h1')).toContainText('Novo Workload')
    
    // Fill workload form
    await page.fill('input[name="name"]', 'Test Bubble Sort')
    await page.fill('textarea[name="description"]', 'Implementation of bubble sort algorithm')
    await page.selectOption('select[name="language"]', 'python')
    await page.selectOption('select[name="complexity_target"]', 'O(n^2)')
    
    // Fill code editor
    const codeEditor = page.locator('[data-testid="code-editor"]')
    await codeEditor.fill(`def bubble_sort(arr):
    n = len(arr)
    for i in range(n):
        for j in range(0, n-i-1):
            if arr[j] > arr[j+1]:
                arr[j], arr[j+1] = arr[j+1], arr[j]
    return arr`)
    
    await page.click('button[type="submit"]')
    
    // Should redirect to workloads list
    await expect(page).toHaveURL('/workloads')
    await expect(page.locator('text=Test Bubble Sort')).toBeVisible()
    await expect(page.locator('text=Workload criado com sucesso')).toBeVisible()
  })

  test('should validate workload form', async ({ page }) => {
    await page.goto('/workloads/new')
    
    // Try to submit empty form
    await page.click('button[type="submit"]')
    
    await expect(page.locator('text=Nome é obrigatório')).toBeVisible()
    await expect(page.locator('text=Código é obrigatório')).toBeVisible()
  })

  test('should edit existing workload', async ({ page }) => {
    await page.goto('/workloads')
    
    // Click edit button on first workload
    await page.click('[data-testid="workload-edit"]:first-child')
    
    await expect(page.locator('h1')).toContainText('Editar Workload')
    
    // Update name
    await page.fill('input[name="name"]', 'Updated Bubble Sort')
    await page.click('button[type="submit"]')
    
    // Should redirect back to workloads list
    await expect(page).toHaveURL('/workloads')
    await expect(page.locator('text=Updated Bubble Sort')).toBeVisible()
    await expect(page.locator('text=Workload atualizado com sucesso')).toBeVisible()
  })

  test('should delete workload', async ({ page }) => {
    await page.goto('/workloads')
    
    // Get initial workload count
    const initialCount = await page.locator('[data-testid="workload-item"]').count()
    
    // Click delete button on first workload
    await page.click('[data-testid="workload-delete"]:first-child')
    
    // Confirm deletion in modal
    await expect(page.locator('text=Confirmar exclusão')).toBeVisible()
    await page.click('button:has-text("Excluir")')
    
    // Should show success message and update list
    await expect(page.locator('text=Workload excluído com sucesso')).toBeVisible()
    
    // Check that workload count decreased
    const finalCount = await page.locator('[data-testid="workload-item"]').count()
    expect(finalCount).toBe(initialCount - 1)
  })

  test('should view workload details', async ({ page }) => {
    await page.goto('/workloads')
    
    // Click on first workload
    await page.click('[data-testid="workload-item"]:first-child')
    
    await expect(page.locator('h1')).toContainText('Detalhes do Workload')
    await expect(page.locator('[data-testid="workload-code"]')).toBeVisible()
    await expect(page.locator('button:has-text("Executar Benchmark")')).toBeVisible()
  })

  test('should run benchmark from workload details', async ({ page }) => {
    await page.goto('/workloads')
    await page.click('[data-testid="workload-item"]:first-child')
    
    await page.click('button:has-text("Executar Benchmark")')
    
    // Should open benchmark configuration modal
    await expect(page.locator('text=Configurar Benchmark')).toBeVisible()
    
    // Configure benchmark
    await page.fill('input[name="input_sizes"]', '10,20,50,100')
    await page.fill('input[name="iterations"]', '3')
    
    await page.click('button:has-text("Iniciar Benchmark")')
    
    // Should redirect to benchmark status page
    await expect(page).toHaveURL(/\/benchmarks\//)
    await expect(page.locator('text=Benchmark iniciado')).toBeVisible()
  })

  test('should filter workloads by language', async ({ page }) => {
    await page.goto('/workloads')
    
    // Select Python filter
    await page.selectOption('[data-testid="language-filter"]', 'python')
    
    // All visible workloads should be Python
    const workloadLanguages = await page.locator('[data-testid="workload-language"]').allTextContents()
    workloadLanguages.forEach(language => {
      expect(language.toLowerCase()).toContain('python')
    })
  })

  test('should search workloads', async ({ page }) => {
    await page.goto('/workloads')
    
    // Search for specific workload
    await page.fill('[data-testid="search-input"]', 'bubble')
    
    // Should filter results
    await expect(page.locator('[data-testid="workload-item"]')).toHaveCount(1)
    await expect(page.locator('text=bubble')).toBeVisible()
  })

  test('should handle workload limits', async ({ page }) => {
    await page.goto('/workloads')
    
    // Try to create workloads up to the limit
    // This test assumes the user has a limit
    const createButton = page.locator('button:has-text("Novo Workload")')
    
    // If user is at limit, button should be disabled or show warning
    if (await createButton.isDisabled()) {
      await expect(page.locator('text=Limite de workloads atingido')).toBeVisible()
    }
  })

  test('should show workload statistics', async ({ page }) => {
    await page.goto('/workloads')
    
    // Should show workload count and usage stats
    await expect(page.locator('[data-testid="workload-count"]')).toBeVisible()
    await expect(page.locator('[data-testid="usage-stats"]')).toBeVisible()
  })

  test('should export workload', async ({ page }) => {
    await page.goto('/workloads')
    await page.click('[data-testid="workload-item"]:first-child')
    
    // Click export button
    await page.click('button:has-text("Exportar")')
    
    // Should trigger download
    const downloadPromise = page.waitForEvent('download')
    await page.click('button:has-text("Baixar JSON")')
    const download = await downloadPromise
    
    expect(download.suggestedFilename()).toMatch(/\.json$/)
  })

  test('should import workload', async ({ page }) => {
    await page.goto('/workloads')
    
    await page.click('button:has-text("Importar")')
    
    // Should show import modal
    await expect(page.locator('text=Importar Workload')).toBeVisible()
    
    // Upload file
    const fileInput = page.locator('input[type="file"]')
    await fileInput.setInputFiles({
      name: 'test-workload.json',
      mimeType: 'application/json',
      buffer: Buffer.from(JSON.stringify({
        name: 'Imported Workload',
        description: 'Test import',
        code: 'def test(): return True',
        language: 'python'
      }))
    })
    
    await page.click('button:has-text("Importar")')
    
    // Should show success message
    await expect(page.locator('text=Workload importado com sucesso')).toBeVisible()
    await expect(page.locator('text=Imported Workload')).toBeVisible()
  })
})
