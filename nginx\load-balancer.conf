# Exo Piper Load Balancer Configuration
# High-performance Nginx configuration for production

# Main context
user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

# Worker connections
events {
    worker_connections 4096;
    use epoll;
    multi_accept on;
}

http {
    # Basic settings
    include /etc/nginx/mime.types;
    default_type application/octet-stream;
    
    # Logging format
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for" '
                    'rt=$request_time uct="$upstream_connect_time" '
                    'uht="$upstream_header_time" urt="$upstream_response_time"';
    
    access_log /var/log/nginx/access.log main;
    
    # Performance optimizations
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    keepalive_requests 1000;
    types_hash_max_size 2048;
    server_tokens off;
    
    # Buffer sizes
    client_body_buffer_size 128k;
    client_max_body_size 10m;
    client_header_buffer_size 1k;
    large_client_header_buffers 4 4k;
    output_buffers 1 32k;
    postpone_output 1460;
    
    # Timeouts
    client_body_timeout 12;
    client_header_timeout 12;
    send_timeout 10;
    
    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;
    
    # Rate limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=login:10m rate=5r/m;
    limit_req_zone $binary_remote_addr zone=register:10m rate=2r/m;
    
    # Connection limiting
    limit_conn_zone $binary_remote_addr zone=conn_limit_per_ip:10m;
    limit_conn_zone $server_name zone=conn_limit_per_server:10m;
    
    # Backend server pools
    upstream backend_api {
        least_conn;
        server backend-1:8000 max_fails=3 fail_timeout=30s weight=1;
        server backend-2:8000 max_fails=3 fail_timeout=30s weight=1;
        server backend-3:8000 max_fails=3 fail_timeout=30s weight=1;
        
        # Health check (requires nginx-plus or custom module)
        # health_check interval=10s fails=3 passes=2;
        
        keepalive 32;
    }
    
    upstream celery_workers {
        least_conn;
        server celery-1:8000 max_fails=3 fail_timeout=30s;
        server celery-2:8000 max_fails=3 fail_timeout=30s;
        keepalive 16;
    }
    
    upstream frontend_servers {
        least_conn;
        server frontend-1:3000 max_fails=3 fail_timeout=30s;
        server frontend-2:3000 max_fails=3 fail_timeout=30s;
        keepalive 16;
    }
    
    # Cache configuration
    proxy_cache_path /var/cache/nginx/api levels=1:2 keys_zone=api_cache:10m max_size=1g inactive=60m use_temp_path=off;
    proxy_cache_path /var/cache/nginx/static levels=1:2 keys_zone=static_cache:10m max_size=2g inactive=24h use_temp_path=off;
    
    # Security headers map
    map $sent_http_content_type $security_headers {
        ~*text/html "nosniff";
        default "";
    }
    
    # SSL configuration
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    ssl_stapling on;
    ssl_stapling_verify on;
    
    # Main server block
    server {
        listen 80;
        listen [::]:80;
        server_name exopiper.com www.exopiper.com;
        
        # Redirect HTTP to HTTPS
        return 301 https://$server_name$request_uri;
    }
    
    # HTTPS server block
    server {
        listen 443 ssl http2;
        listen [::]:443 ssl http2;
        server_name exopiper.com www.exopiper.com;
        
        # SSL certificates
        ssl_certificate /etc/ssl/certs/exopiper.com.crt;
        ssl_certificate_key /etc/ssl/private/exopiper.com.key;
        
        # Security headers
        add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
        add_header X-Frame-Options "DENY" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header Referrer-Policy "strict-origin-when-cross-origin" always;
        add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' https://api.exopiper.com wss://api.exopiper.com;" always;
        
        # Connection limits
        limit_conn conn_limit_per_ip 20;
        limit_conn conn_limit_per_server 1000;
        
        # Root and index
        root /var/www/html;
        index index.html;
        
        # Frontend routes
        location / {
            try_files $uri $uri/ @frontend;
            
            # Cache static assets
            location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
                expires 1y;
                add_header Cache-Control "public, immutable";
                add_header Vary "Accept-Encoding";
                
                proxy_pass http://frontend_servers;
                proxy_cache static_cache;
                proxy_cache_valid 200 24h;
                proxy_cache_use_stale error timeout updating http_500 http_502 http_503 http_504;
            }
        }
        
        # Frontend fallback
        location @frontend {
            proxy_pass http://frontend_servers;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # Timeouts
            proxy_connect_timeout 5s;
            proxy_send_timeout 60s;
            proxy_read_timeout 60s;
            
            # Buffering
            proxy_buffering on;
            proxy_buffer_size 4k;
            proxy_buffers 8 4k;
        }
        
        # API routes
        location /api/ {
            # Rate limiting
            limit_req zone=api burst=20 nodelay;
            
            # Special rate limits for auth endpoints
            location /api/v1/auth/login {
                limit_req zone=login burst=5 nodelay;
                proxy_pass http://backend_api;
            }
            
            location /api/v1/auth/register {
                limit_req zone=register burst=2 nodelay;
                proxy_pass http://backend_api;
            }
            
            # Default API proxy
            proxy_pass http://backend_api;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # Timeouts
            proxy_connect_timeout 5s;
            proxy_send_timeout 300s;  # Longer for benchmark operations
            proxy_read_timeout 300s;
            
            # Caching for GET requests
            proxy_cache api_cache;
            proxy_cache_methods GET HEAD;
            proxy_cache_valid 200 5m;
            proxy_cache_valid 404 1m;
            proxy_cache_bypass $http_cache_control;
            add_header X-Cache-Status $upstream_cache_status;
            
            # Don't cache auth and user-specific endpoints
            location ~* /api/v1/(auth|user|workloads|benchmarks) {
                proxy_cache off;
                proxy_pass http://backend_api;
            }
        }
        
        # WebSocket support for real-time updates
        location /ws/ {
            proxy_pass http://backend_api;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # WebSocket timeouts
            proxy_read_timeout 86400s;
            proxy_send_timeout 86400s;
        }
        
        # Health check endpoint
        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }
        
        # Nginx status (for monitoring)
        location /nginx_status {
            stub_status on;
            access_log off;
            allow 127.0.0.1;
            allow 10.0.0.0/8;
            allow **********/12;
            allow ***********/16;
            deny all;
        }
        
        # Block common attack patterns
        location ~* \.(php|asp|aspx|jsp)$ {
            return 444;
        }
        
        location ~* /\. {
            deny all;
            access_log off;
            log_not_found off;
        }
        
        # Error pages
        error_page 404 /404.html;
        error_page 500 502 503 504 /50x.html;
        
        location = /50x.html {
            root /usr/share/nginx/html;
        }
    }
    
    # API subdomain
    server {
        listen 443 ssl http2;
        listen [::]:443 ssl http2;
        server_name api.exopiper.com;
        
        # SSL certificates
        ssl_certificate /etc/ssl/certs/api.exopiper.com.crt;
        ssl_certificate_key /etc/ssl/private/api.exopiper.com.key;
        
        # Security headers
        add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
        add_header X-Frame-Options "DENY" always;
        add_header X-Content-Type-Options "nosniff" always;
        
        # CORS headers for API
        add_header Access-Control-Allow-Origin "https://exopiper.com" always;
        add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
        add_header Access-Control-Allow-Headers "Authorization, Content-Type, X-Requested-With" always;
        add_header Access-Control-Allow-Credentials "true" always;
        
        # Handle preflight requests
        location / {
            if ($request_method = 'OPTIONS') {
                add_header Access-Control-Allow-Origin "https://exopiper.com";
                add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS";
                add_header Access-Control-Allow-Headers "Authorization, Content-Type, X-Requested-With";
                add_header Access-Control-Max-Age 1728000;
                add_header Content-Type "text/plain charset=UTF-8";
                add_header Content-Length 0;
                return 204;
            }
            
            # Rate limiting
            limit_req zone=api burst=50 nodelay;
            
            proxy_pass http://backend_api;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # Timeouts
            proxy_connect_timeout 5s;
            proxy_send_timeout 300s;
            proxy_read_timeout 300s;
        }
    }
}
