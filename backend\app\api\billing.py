"""
Billing API endpoints
"""

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession

from ..core.database import get_db
from ..core.security import get_current_user
from ..models.user import User, PlanType

router = APIRouter()


@router.get("/plans")
async def get_available_plans():
    """Get available subscription plans"""
    
    return {
        "plans": [
            {
                "name": "Free",
                "price": 0,
                "currency": "USD",
                "interval": "month",
                "features": [
                    "2 workloads",
                    "7 days retention",
                    "Basic support"
                ]
            },
            {
                "name": "Pro",
                "price": 29,
                "currency": "USD", 
                "interval": "month",
                "features": [
                    "5 workloads",
                    "30 days retention",
                    "Email alerts",
                    "Priority support"
                ]
            },
            {
                "name": "Team",
                "price": 79,
                "currency": "USD",
                "interval": "month", 
                "features": [
                    "20 workloads",
                    "90 days retention",
                    "Multiple users",
                    "SSO integration",
                    "Slack alerts",
                    "Premium support"
                ]
            }
        ]
    }


@router.get("/subscription")
async def get_current_subscription(
    current_user: User = Depends(get_current_user)
):
    """Get current user's subscription details"""
    
    return {
        "plan_type": current_user.plan_type.value,
        "plan_expires_at": current_user.plan_expires_at,
        "is_active": current_user.is_active
    }


@router.post("/upgrade")
async def upgrade_plan(
    plan_type: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Upgrade user's plan (placeholder for Stripe integration)"""
    
    if plan_type not in ["pro", "team"]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid plan type"
        )
    
    # TODO: Implement Stripe payment processing
    # For now, just return a placeholder response
    
    return {
        "message": f"Plan upgrade to {plan_type} not yet implemented",
        "current_plan": current_user.plan_type.value,
        "requested_plan": plan_type
    }
