#!/bin/bash

# Test script for Exo Piper system

echo "🧪 Testing Exo Piper System..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    local status=$1
    local message=$2
    case $status in
        "SUCCESS")
            echo -e "${GREEN}✅ $message${NC}"
            ;;
        "ERROR")
            echo -e "${RED}❌ $message${NC}"
            ;;
        "WARNING")
            echo -e "${YELLOW}⚠️  $message${NC}"
            ;;
        "INFO")
            echo -e "${BLUE}ℹ️  $message${NC}"
            ;;
    esac
}

# Test 1: Check Docker
print_status "INFO" "Testing Docker availability..."
if command -v docker &> /dev/null; then
    if docker info &> /dev/null; then
        print_status "SUCCESS" "Docker is running"
    else
        print_status "ERROR" "Docker is installed but not running"
        exit 1
    fi
else
    print_status "ERROR" "Docker is not installed"
    exit 1
fi

# Test 2: Check Docker Compose
print_status "INFO" "Testing Docker Compose availability..."
if command -v docker-compose &> /dev/null; then
    print_status "SUCCESS" "Docker Compose is available"
else
    print_status "ERROR" "Docker Compose is not installed"
    exit 1
fi

# Test 3: Build Agent Image
print_status "INFO" "Building Agent Docker image..."
cd agent
if docker build -t exopiper/agent:latest . &> /dev/null; then
    print_status "SUCCESS" "Agent image built successfully"
else
    print_status "ERROR" "Failed to build agent image"
    exit 1
fi
cd ..

# Test 4: Test Agent
print_status "INFO" "Testing Agent with sample workload..."
AGENT_OUTPUT=$(docker run --rm \
    -e WORKLOAD_TYPE=mergesort \
    -e WORKLOAD_CONFIG='{"max_size":1000,"iterations":2}' \
    -e JOB_ID=test \
    exopiper/agent:latest 2>&1)

if echo "$AGENT_OUTPUT" | grep -q "JSON_OUTPUT_START"; then
    print_status "SUCCESS" "Agent executed successfully"
else
    print_status "ERROR" "Agent execution failed"
    echo "$AGENT_OUTPUT"
    exit 1
fi

# Test 5: Start Infrastructure
print_status "INFO" "Starting infrastructure services..."
if docker-compose up -d postgres redis minio &> /dev/null; then
    print_status "SUCCESS" "Infrastructure services started"
    sleep 10  # Wait for services to be ready
else
    print_status "ERROR" "Failed to start infrastructure"
    exit 1
fi

# Test 6: Check Service Health
print_status "INFO" "Checking service health..."

# Check PostgreSQL
if docker-compose exec -T postgres pg_isready -U exoPiper &> /dev/null; then
    print_status "SUCCESS" "PostgreSQL is healthy"
else
    print_status "WARNING" "PostgreSQL health check failed"
fi

# Check Redis
if docker-compose exec -T redis redis-cli ping | grep -q "PONG"; then
    print_status "SUCCESS" "Redis is healthy"
else
    print_status "WARNING" "Redis health check failed"
fi

# Check MinIO
if curl -f http://localhost:9000/minio/health/live &> /dev/null; then
    print_status "SUCCESS" "MinIO is healthy"
else
    print_status "WARNING" "MinIO health check failed"
fi

# Test 7: Start Backend
print_status "INFO" "Starting backend service..."
if docker-compose up -d backend &> /dev/null; then
    print_status "SUCCESS" "Backend service started"
    sleep 15  # Wait for backend to be ready
else
    print_status "ERROR" "Failed to start backend"
    exit 1
fi

# Test 8: Check Backend Health
print_status "INFO" "Checking backend health..."
if curl -f http://localhost:8000/health &> /dev/null; then
    print_status "SUCCESS" "Backend is responding"
else
    print_status "WARNING" "Backend health check failed"
fi

# Test 9: Start Frontend
print_status "INFO" "Starting frontend service..."
if docker-compose up -d frontend &> /dev/null; then
    print_status "SUCCESS" "Frontend service started"
    sleep 10
else
    print_status "ERROR" "Failed to start frontend"
    exit 1
fi

# Test 10: Check Frontend
print_status "INFO" "Checking frontend..."
if curl -f http://localhost:3000 &> /dev/null; then
    print_status "SUCCESS" "Frontend is responding"
else
    print_status "WARNING" "Frontend health check failed"
fi

# Test 11: CLI Installation
print_status "INFO" "Testing CLI installation..."
cd cli
if pip install -e . &> /dev/null; then
    print_status "SUCCESS" "CLI installed successfully"
else
    print_status "ERROR" "Failed to install CLI"
    exit 1
fi
cd ..

# Test 12: CLI Help
print_status "INFO" "Testing CLI help command..."
if perf-audit --help &> /dev/null; then
    print_status "SUCCESS" "CLI is working"
else
    print_status "ERROR" "CLI help command failed"
    exit 1
fi

# Summary
echo ""
print_status "SUCCESS" "System test completed!"
echo ""
print_status "INFO" "Services running:"
echo "  🌐 Frontend: http://localhost:3000"
echo "  🔧 Backend API: http://localhost:8000"
echo "  📚 API Docs: http://localhost:8000/docs"
echo "  🗄️  MinIO Console: http://localhost:9001 (admin/password)"
echo ""
print_status "INFO" "Next steps:"
echo "  1. Register a user at http://localhost:3000/register"
echo "  2. Get API key from dashboard"
echo "  3. Test CLI: perf-audit login --api-key <your-key>"
echo "  4. Run benchmark: perf-audit run --workloads mergesort"
echo ""
print_status "SUCCESS" "Exo Piper is ready! 🚀"
