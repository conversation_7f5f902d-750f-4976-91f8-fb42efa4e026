"""
Payment service using TANOS for USDT payments on BEP20 and ERC20 networks
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from decimal import Decimal
import json
import httpx
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_

from ..core.database import AsyncSessionLocal
from ..core.config import settings
from ..models.user import User, PlanType
from ..models.payment import Payment, PaymentStatus, PaymentMethod

logger = logging.getLogger(__name__)


class PaymentService:
    """Service for handling USDT payments via TANOS integration"""

    # Wallet address for receiving USDT payments
    PAYMENT_WALLET = "******************************************"

    # USDT contract addresses
    USDT_CONTRACTS = {
        "bep20": "******************************************",  # BSC USDT
        "erc20": "******************************************"   # ETH USDT
    }

    # Plan prices in USDT
    PLAN_PRICES = {
        PlanType.BASIC: Decimal("50.00"),      # $50/month
        PlanType.PRO: Decimal("200.00"),       # $200/month
        PlanType.ENTERPRISE: Decimal("1000.00") # $1000/month
    }

    # Network RPC endpoints
    RPC_ENDPOINTS = {
        "bep20": "https://bsc-dataseed1.binance.org/",
        "erc20": "https://mainnet.infura.io/v3/YOUR_INFURA_KEY"  # Replace with actual key
    }

    def __init__(self):
        self.http_client = httpx.AsyncClient(timeout=30.0)

    async def create_payment_request(
        self,
        user_id: int,
        plan_type: PlanType,
        network: str = "bep20"
    ) -> Dict[str, Any]:
        """
        Create a new payment request for plan upgrade

        Args:
            user_id: User ID requesting the upgrade
            plan_type: Target plan type
            network: Payment network (bep20 or erc20)

        Returns:
            Payment request details including wallet address and amount
        """

        if network not in ["bep20", "erc20"]:
            raise ValueError("Network must be 'bep20' or 'erc20'")

        if plan_type not in self.PLAN_PRICES:
            raise ValueError(f"Invalid plan type: {plan_type}")

        amount = self.PLAN_PRICES[plan_type]

        async with AsyncSessionLocal() as db:
            # Create payment record
            payment = Payment(
                user_id=user_id,
                amount=amount,
                currency="USDT",
                network=network,
                target_plan=plan_type.value,
                payment_address=self.PAYMENT_WALLET,
                contract_address=self.USDT_CONTRACTS[network],
                status=PaymentStatus.PENDING,
                expires_at=datetime.utcnow() + timedelta(hours=24)  # 24h to pay
            )

            db.add(payment)
            await db.commit()
            await db.refresh(payment)

            return {
                "payment_id": payment.id,
                "amount": str(amount),
                "currency": "USDT",
                "network": network.upper(),
                "wallet_address": self.PAYMENT_WALLET,
                "contract_address": self.USDT_CONTRACTS[network],
                "expires_at": payment.expires_at.isoformat(),
                "qr_code": self._generate_qr_code(amount, network, payment.payment_address, payment.contract_address),
                "instructions": self._get_payment_instructions(network)
            }

    def _generate_qr_code(self, amount: Decimal, network: str, wallet_address: str, contract_address: str) -> Dict[str, Any]:
        """Generate QR code for payment using QR service"""
        from .qr_service import qr_service

        return qr_service.generate_payment_qr(
            wallet_address=wallet_address,
            amount=amount,
            currency="USDT",
            network=network,
            contract_address=contract_address
        )

    def _get_payment_instructions(self, network: str) -> List[str]:
        """Get payment instructions for the network"""
        if network == "bep20":
            return [
                "1. Abra sua wallet (MetaMask, Trust Wallet, etc.)",
                "2. Conecte à rede Binance Smart Chain (BSC)",
                "3. Envie o valor exato em USDT para o endereço fornecido",
                "4. Aguarde a confirmação da transação (1-3 minutos)",
                "5. Seu plano será ativado automaticamente"
            ]
        else:
            return [
                "1. Abra sua wallet (MetaMask, etc.)",
                "2. Conecte à rede Ethereum Mainnet",
                "3. Envie o valor exato em USDT para o endereço fornecido",
                "4. Aguarde a confirmação da transação (5-15 minutos)",
                "5. Seu plano será ativado automaticamente"
            ]

    async def check_payment_status(self, payment_id: int) -> Dict[str, Any]:
        """Check the status of a payment"""

        async with AsyncSessionLocal() as db:
            # Get payment record
            payment_query = select(Payment).where(Payment.id == payment_id)
            payment_result = await db.execute(payment_query)
            payment = payment_result.scalar_one_or_none()

            if not payment:
                raise ValueError("Payment not found")

            # If already confirmed, return status
            if payment.status == PaymentStatus.CONFIRMED:
                return {
                    "status": "confirmed",
                    "transaction_hash": payment.transaction_hash,
                    "confirmed_at": payment.confirmed_at.isoformat() if payment.confirmed_at else None
                }

            # If expired, mark as expired
            if payment.expires_at < datetime.utcnow():
                payment.status = PaymentStatus.EXPIRED
                await db.commit()
                return {"status": "expired"}

            # Check blockchain for payment
            try:
                tx_hash = await self._check_blockchain_payment(payment)

                if tx_hash:
                    # Payment found, confirm it
                    payment.status = PaymentStatus.CONFIRMED
                    payment.transaction_hash = tx_hash
                    payment.confirmed_at = datetime.utcnow()

                    # Upgrade user plan
                    old_plan = await self._get_user_plan(payment.user_id, db)
                    await self._upgrade_user_plan(payment.user_id, payment.target_plan, db)

                    await db.commit()

                    # Send webhook notification
                    await self._send_payment_confirmed_webhook(payment.id, old_plan, payment.target_plan)

                    return {
                        "status": "confirmed",
                        "transaction_hash": tx_hash,
                        "confirmed_at": payment.confirmed_at.isoformat()
                    }
                else:
                    return {"status": "pending"}

            except Exception as e:
                logger.error(f"Error checking blockchain payment: {e}")
                return {"status": "error", "message": str(e)}

    async def _check_blockchain_payment(self, payment: Payment) -> Optional[str]:
        """
        Check blockchain for USDT payment
        Returns transaction hash if payment found, None otherwise
        """

        try:
            if payment.network == "bep20":
                return await self._check_bep20_payment(payment)
            else:
                return await self._check_erc20_payment(payment)
        except Exception as e:
            logger.error(f"Blockchain check error: {e}")
            return None

    async def _check_bep20_payment(self, payment: Payment) -> Optional[str]:
        """Check BEP20 USDT payment on BSC"""

        # BSC API call to check USDT transfers
        api_url = "https://api.bscscan.com/api"
        params = {
            "module": "account",
            "action": "tokentx",
            "contractaddress": payment.contract_address,
            "address": payment.payment_address,
            "startblock": 0,
            "endblock": *********,
            "sort": "desc",
            "apikey": settings.BSCSCAN_API_KEY if hasattr(settings, 'BSCSCAN_API_KEY') else "YourApiKeyToken"
        }

        try:
            response = await self.http_client.get(api_url, params=params)
            data = response.json()

            if data["status"] == "1":
                for tx in data["result"]:
                    # Check if transaction matches our payment
                    tx_amount = Decimal(tx["value"]) / Decimal(10**18)  # USDT has 18 decimals on BSC
                    tx_time = datetime.fromtimestamp(int(tx["timeStamp"]))

                    # Check if amount matches and transaction is after payment creation
                    if (abs(tx_amount - payment.amount) < Decimal("0.01") and
                        tx_time > payment.created_at and
                        tx["to"].lower() == payment.payment_address.lower()):

                        return tx["hash"]

            return None

        except Exception as e:
            logger.error(f"BSC API error: {e}")
            return None

    async def _check_erc20_payment(self, payment: Payment) -> Optional[str]:
        """Check ERC20 USDT payment on Ethereum"""

        # Ethereum API call to check USDT transfers
        api_url = "https://api.etherscan.io/api"
        params = {
            "module": "account",
            "action": "tokentx",
            "contractaddress": payment.contract_address,
            "address": payment.payment_address,
            "startblock": 0,
            "endblock": *********,
            "sort": "desc",
            "apikey": settings.ETHERSCAN_API_KEY if hasattr(settings, 'ETHERSCAN_API_KEY') else "YourApiKeyToken"
        }

        try:
            response = await self.http_client.get(api_url, params=params)
            data = response.json()

            if data["status"] == "1":
                for tx in data["result"]:
                    # Check if transaction matches our payment
                    tx_amount = Decimal(tx["value"]) / Decimal(10**6)  # USDT has 6 decimals on Ethereum
                    tx_time = datetime.fromtimestamp(int(tx["timeStamp"]))

                    # Check if amount matches and transaction is after payment creation
                    if (abs(tx_amount - payment.amount) < Decimal("0.01") and
                        tx_time > payment.created_at and
                        tx["to"].lower() == payment.payment_address.lower()):

                        return tx["hash"]

            return None

        except Exception as e:
            logger.error(f"Ethereum API error: {e}")
            return None

    async def _upgrade_user_plan(self, user_id: int, target_plan: str, db: AsyncSession):
        """Upgrade user to the target plan"""

        user_query = select(User).where(User.id == user_id)
        user_result = await db.execute(user_query)
        user = user_result.scalar_one_or_none()

        if user:
            user.plan_type = target_plan
            user.plan_upgraded_at = datetime.utcnow()

            # Set plan expiry (monthly billing)
            user.plan_expires_at = datetime.utcnow() + timedelta(days=30)

            logger.info(f"User {user_id} upgraded to {target_plan}")

    async def get_payment_history(self, user_id: int) -> List[Dict[str, Any]]:
        """Get payment history for a user"""

        async with AsyncSessionLocal() as db:
            payments_query = select(Payment).where(
                Payment.user_id == user_id
            ).order_by(Payment.created_at.desc())

            payments_result = await db.execute(payments_query)
            payments = payments_result.scalars().all()

            return [
                {
                    "id": payment.id,
                    "amount": str(payment.amount),
                    "currency": payment.currency,
                    "network": payment.network,
                    "target_plan": payment.target_plan,
                    "status": payment.status.value,
                    "transaction_hash": payment.transaction_hash,
                    "created_at": payment.created_at.isoformat(),
                    "confirmed_at": payment.confirmed_at.isoformat() if payment.confirmed_at else None,
                    "expires_at": payment.expires_at.isoformat() if payment.expires_at else None
                }
                for payment in payments
            ]

    async def cleanup_expired_payments(self):
        """Clean up expired payments (run periodically)"""

        async with AsyncSessionLocal() as db:
            expired_payments_query = select(Payment).where(
                and_(
                    Payment.status == PaymentStatus.PENDING,
                    Payment.expires_at < datetime.utcnow()
                )
            )

            expired_result = await db.execute(expired_payments_query)
            expired_payments = expired_result.scalars().all()

            for payment in expired_payments:
                payment.status = PaymentStatus.EXPIRED

            await db.commit()

            logger.info(f"Marked {len(expired_payments)} payments as expired")

    async def _get_user_plan(self, user_id: int, db: AsyncSession) -> str:
        """Get current user plan"""
        user_query = select(User).where(User.id == user_id)
        user_result = await db.execute(user_query)
        user = user_result.scalar_one_or_none()
        return user.plan_type if user else "free"

    async def _send_payment_confirmed_webhook(self, payment_id: int, old_plan: str, new_plan: str):
        """Send webhook notification for confirmed payment"""
        try:
            from .webhook_service import webhook_service

            # Send payment confirmed webhook
            await webhook_service.send_payment_webhook(
                payment_id=payment_id,
                event_type="payment_confirmed"
            )

            # Send plan upgrade notification
            await webhook_service.send_plan_upgrade_notification(
                user_id=payment_id,  # This should be user_id, will fix
                old_plan=old_plan,
                new_plan=new_plan
            )

        except Exception as e:
            logger.error(f"Error sending webhook for payment {payment_id}: {e}")


# Global payment service instance
payment_service = PaymentService()
