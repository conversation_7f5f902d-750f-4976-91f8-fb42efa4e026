'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import {
  CreditCardIcon,
  CheckIcon,
  ClockIcon,
  ExclamationTriangleIcon,
  ArrowPathIcon,
  QrCodeIcon,
  CopyIcon
} from '@heroicons/react/24/outline'

interface Plan {
  name: string
  display_name: string
  price_usdt: string
  price_usd: string
  available: boolean
  current: boolean
  features: string[]
}

interface PaymentNetwork {
  id: string
  name: string
  currency: string
  fees: string
  confirmation_time: string
}

interface PaymentRequest {
  payment_id: number
  amount: string
  currency: string
  network: string
  wallet_address: string
  contract_address: string
  expires_at: string
  qr_code: {
    payment_url: string
    qr_code_png_base64: string
    qr_code_svg: string
    network: string
    amount: string
    currency: string
    wallet_address: string
  }
  instructions: string[]
}

export default function UpgradePage() {
  const router = useRouter()
  const [plans, setPlans] = useState<Plan[]>([])
  const [networks, setNetworks] = useState<PaymentNetwork[]>([])
  const [selectedPlan, setSelectedPlan] = useState<string>('')
  const [selectedNetwork, setSelectedNetwork] = useState<string>('bep20')
  const [isLoading, setIsLoading] = useState(true)
  const [paymentRequest, setPaymentRequest] = useState<PaymentRequest | null>(null)
  const [paymentStatus, setPaymentStatus] = useState<string>('idle')
  const [copied, setCopied] = useState<string>('')

  useEffect(() => {
    fetchPlans()
  }, [])

  useEffect(() => {
    let interval: NodeJS.Timeout

    if (paymentRequest && paymentStatus === 'pending') {
      // Check payment status every 30 seconds
      interval = setInterval(() => {
        checkPaymentStatus()
      }, 30000)
    }

    return () => {
      if (interval) clearInterval(interval)
    }
  }, [paymentRequest, paymentStatus])

  const fetchPlans = async () => {
    try {
      const token = localStorage.getItem('token')
      const response = await fetch('/api/v1/payments/plans', {
        headers: { 'Authorization': `Bearer ${token}` }
      })

      if (response.ok) {
        const data = await response.json()
        setPlans(data.plans || [])
        setNetworks(data.payment_networks || [])
      }
    } catch (error) {
      console.error('Error fetching plans:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const createPayment = async () => {
    if (!selectedPlan) return

    setIsLoading(true)
    try {
      const token = localStorage.getItem('token')
      const response = await fetch('/api/v1/payments/create', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          plan_type: selectedPlan,
          network: selectedNetwork
        })
      })

      if (response.ok) {
        const data = await response.json()
        setPaymentRequest(data.payment)
        setPaymentStatus('pending')
      } else {
        const error = await response.json()
        alert(`Erro: ${error.detail}`)
      }
    } catch (error) {
      console.error('Error creating payment:', error)
      alert('Erro ao criar pagamento')
    } finally {
      setIsLoading(false)
    }
  }

  const checkPaymentStatus = async () => {
    if (!paymentRequest) return

    try {
      const token = localStorage.getItem('token')
      const response = await fetch(`/api/v1/payments/status/${paymentRequest.payment_id}`, {
        headers: { 'Authorization': `Bearer ${token}` }
      })

      if (response.ok) {
        const data = await response.json()
        setPaymentStatus(data.status)

        if (data.status === 'confirmed') {
          // Payment confirmed, redirect to dashboard
          setTimeout(() => {
            router.push('/dashboard?upgraded=true')
          }, 3000)
        }
      }
    } catch (error) {
      console.error('Error checking payment status:', error)
    }
  }

  const copyToClipboard = async (text: string, type: string) => {
    try {
      await navigator.clipboard.writeText(text)
      setCopied(type)
      setTimeout(() => setCopied(''), 2000)
    } catch (error) {
      console.error('Error copying to clipboard:', error)
    }
  }

  const formatTimeRemaining = (expiresAt: string) => {
    const now = new Date()
    const expires = new Date(expiresAt)
    const diff = expires.getTime() - now.getTime()

    if (diff <= 0) return 'Expirado'

    const hours = Math.floor(diff / (1000 * 60 * 60))
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))

    return `${hours}h ${minutes}m restantes`
  }

  if (isLoading && !paymentRequest) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <ArrowPathIcon className="h-8 w-8 animate-spin text-primary-600" />
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4 lg:py-6">
            <h1 className="text-lg sm:text-2xl font-bold text-gray-900">Upgrade de Plano</h1>
            <button
              onClick={() => router.push('/dashboard')}
              className="btn-secondary"
            >
              Voltar
            </button>
          </div>
        </div>
      </header>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8">
        {!paymentRequest ? (
          <>
            {/* Plan Selection */}
            <div className="mb-8">
              <h2 className="text-xl font-semibold text-gray-900 mb-6">Escolha seu Plano</h2>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {plans.filter(plan => plan.available).map((plan) => (
                  <div
                    key={plan.name}
                    className={`card cursor-pointer transition-all ${
                      selectedPlan === plan.name
                        ? 'ring-2 ring-primary-500 bg-primary-50'
                        : 'hover:shadow-lg'
                    }`}
                    onClick={() => setSelectedPlan(plan.name)}
                  >
                    <div className="text-center mb-4">
                      <h3 className="text-lg font-semibold text-gray-900">{plan.display_name}</h3>
                      <div className="mt-2">
                        <span className="text-3xl font-bold text-primary-600">${plan.price_usd}</span>
                        <span className="text-gray-600">/mês</span>
                      </div>
                      <div className="text-sm text-gray-500 mt-1">
                        {plan.price_usdt} USDT
                      </div>
                    </div>

                    <ul className="space-y-2 mb-4">
                      {plan.features.map((feature, index) => (
                        <li key={index} className="flex items-center text-sm">
                          <CheckIcon className="h-4 w-4 text-green-500 mr-2 flex-shrink-0" />
                          {feature}
                        </li>
                      ))}
                    </ul>

                    {selectedPlan === plan.name && (
                      <div className="text-center">
                        <CheckIcon className="h-6 w-6 text-primary-600 mx-auto" />
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>

            {/* Network Selection */}
            {selectedPlan && (
              <div className="mb-8">
                <h2 className="text-xl font-semibold text-gray-900 mb-6">Escolha a Rede de Pagamento</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {networks.map((network) => (
                    <div
                      key={network.id}
                      className={`card cursor-pointer transition-all ${
                        selectedNetwork === network.id
                          ? 'ring-2 ring-primary-500 bg-primary-50'
                          : 'hover:shadow-lg'
                      }`}
                      onClick={() => setSelectedNetwork(network.id)}
                    >
                      <h3 className="font-semibold text-gray-900 mb-2">{network.name}</h3>
                      <div className="space-y-1 text-sm text-gray-600">
                        <div>Moeda: {network.currency}</div>
                        <div>Taxa: {network.fees}</div>
                        <div>Confirmação: {network.confirmation_time}</div>
                      </div>

                      {selectedNetwork === network.id && (
                        <div className="mt-3">
                          <CheckIcon className="h-5 w-5 text-primary-600" />
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Create Payment Button */}
            {selectedPlan && selectedNetwork && (
              <div className="text-center">
                <button
                  onClick={createPayment}
                  disabled={isLoading}
                  className="btn-primary text-lg px-8 py-3"
                >
                  {isLoading ? (
                    <ArrowPathIcon className="h-5 w-5 animate-spin mr-2" />
                  ) : (
                    <CreditCardIcon className="h-5 w-5 mr-2" />
                  )}
                  Criar Pagamento
                </button>
              </div>
            )}
          </>
        ) : (
          /* Payment Instructions */
          <div className="max-w-2xl mx-auto">
            <div className="card">
              <div className="text-center mb-6">
                <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                  paymentStatus === 'confirmed'
                    ? 'bg-green-100 text-green-800'
                    : paymentStatus === 'expired'
                    ? 'bg-red-100 text-red-800'
                    : 'bg-yellow-100 text-yellow-800'
                }`}>
                  {paymentStatus === 'confirmed' && <CheckIcon className="h-4 w-4 mr-1" />}
                  {paymentStatus === 'pending' && <ClockIcon className="h-4 w-4 mr-1" />}
                  {paymentStatus === 'expired' && <ExclamationTriangleIcon className="h-4 w-4 mr-1" />}

                  {paymentStatus === 'confirmed' && 'Pagamento Confirmado!'}
                  {paymentStatus === 'pending' && 'Aguardando Pagamento'}
                  {paymentStatus === 'expired' && 'Pagamento Expirado'}
                </div>

                {paymentStatus === 'pending' && (
                  <div className="mt-2 text-sm text-gray-600">
                    {formatTimeRemaining(paymentRequest.expires_at)}
                  </div>
                )}
              </div>

              {paymentStatus === 'confirmed' ? (
                <div className="text-center">
                  <CheckIcon className="h-16 w-16 text-green-500 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    Pagamento Confirmado!
                  </h3>
                  <p className="text-gray-600 mb-4">
                    Seu plano foi atualizado com sucesso. Redirecionando...
                  </p>
                </div>
              ) : paymentStatus === 'expired' ? (
                <div className="text-center">
                  <ExclamationTriangleIcon className="h-16 w-16 text-red-500 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    Pagamento Expirado
                  </h3>
                  <p className="text-gray-600 mb-4">
                    O tempo para pagamento expirou. Crie um novo pagamento.
                  </p>
                  <button
                    onClick={() => setPaymentRequest(null)}
                    className="btn-primary"
                  >
                    Criar Novo Pagamento
                  </button>
                </div>
              ) : (
                <>
                  <div className="mb-6">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">
                      Envie {paymentRequest.amount} USDT
                    </h3>

                    {/* Wallet Address */}
                    <div className="mb-4">
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Endereço da Wallet:
                      </label>
                      <div className="flex items-center space-x-2">
                        <input
                          type="text"
                          value={paymentRequest.wallet_address}
                          readOnly
                          className="flex-1 px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-sm"
                        />
                        <button
                          onClick={() => copyToClipboard(paymentRequest.wallet_address, 'address')}
                          className="btn-secondary p-2"
                          title="Copiar endereço"
                        >
                          <CopyIcon className="h-4 w-4" />
                        </button>
                      </div>
                      {copied === 'address' && (
                        <div className="text-sm text-green-600 mt-1">Endereço copiado!</div>
                      )}
                    </div>

                    {/* Amount */}
                    <div className="mb-4">
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Valor Exato:
                      </label>
                      <div className="flex items-center space-x-2">
                        <input
                          type="text"
                          value={`${paymentRequest.amount} USDT`}
                          readOnly
                          className="flex-1 px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-sm"
                        />
                        <button
                          onClick={() => copyToClipboard(paymentRequest.amount, 'amount')}
                          className="btn-secondary p-2"
                          title="Copiar valor"
                        >
                          <CopyIcon className="h-4 w-4" />
                        </button>
                      </div>
                      {copied === 'amount' && (
                        <div className="text-sm text-green-600 mt-1">Valor copiado!</div>
                      )}
                    </div>

                    {/* QR Code */}
                    <div className="text-center mb-4">
                      {paymentRequest.qr_code?.qr_code_png_base64 ? (
                        <div className="inline-block">
                          <img
                            src={paymentRequest.qr_code.qr_code_png_base64}
                            alt="QR Code para pagamento"
                            className="w-48 h-48 border border-gray-200 rounded-lg"
                          />
                          <div className="text-sm text-gray-600 mt-2">
                            Escaneie com sua wallet
                          </div>
                          <div className="text-xs text-gray-500 mt-1">
                            {paymentRequest.qr_code.network.toUpperCase()} - {paymentRequest.qr_code.currency}
                          </div>
                        </div>
                      ) : (
                        <div className="inline-flex items-center justify-center w-48 h-48 bg-gray-100 rounded-lg">
                          <QrCodeIcon className="h-16 w-16 text-gray-400" />
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Instructions */}
                  <div className="mb-6">
                    <h4 className="font-medium text-gray-900 mb-3">Instruções:</h4>
                    <ol className="list-decimal list-inside space-y-2 text-sm text-gray-600">
                      {paymentRequest.instructions.map((instruction, index) => (
                        <li key={index}>{instruction}</li>
                      ))}
                    </ol>
                  </div>

                  {/* Check Status Button */}
                  <div className="text-center">
                    <button
                      onClick={checkPaymentStatus}
                      className="btn-secondary"
                    >
                      <ArrowPathIcon className="h-4 w-4 mr-2" />
                      Verificar Status
                    </button>
                  </div>
                </>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
