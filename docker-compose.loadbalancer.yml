version: '3.8'

services:
  # Load Balancer (Nginx)
  nginx-lb:
    image: nginx:alpine
    container_name: exopiper-nginx-lb
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/load-balancer.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/ssl:ro
      - ./nginx/cache:/var/cache/nginx
      - ./nginx/logs:/var/log/nginx
    depends_on:
      - backend-1
      - backend-2
      - frontend-1
      - frontend-2
    networks:
      - exopiper-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "nginx", "-t"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'

  # Backend instances
  backend-1:
    build:
      context: ./backend
      dockerfile: Dockerfile.production
    container_name: exopiper-backend-1
    environment:
      - DATABASE_URL=postgresql://exoPiper:${POSTGRES_PASSWORD}@postgres:5432/exoPiper
      - REDIS_URL=redis://redis:6379/0
      - SECRET_KEY=${SECRET_KEY}
      - ENVIRONMENT=production
      - INSTANCE_ID=backend-1
    volumes:
      - ./backend/logs:/app/logs
    depends_on:
      - postgres
      - redis
    networks:
      - exopiper-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'

  backend-2:
    build:
      context: ./backend
      dockerfile: Dockerfile.production
    container_name: exopiper-backend-2
    environment:
      - DATABASE_URL=postgresql://exoPiper:${POSTGRES_PASSWORD}@postgres:5432/exoPiper
      - REDIS_URL=redis://redis:6379/0
      - SECRET_KEY=${SECRET_KEY}
      - ENVIRONMENT=production
      - INSTANCE_ID=backend-2
    volumes:
      - ./backend/logs:/app/logs
    depends_on:
      - postgres
      - redis
    networks:
      - exopiper-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'

  # Frontend instances
  frontend-1:
    build:
      context: ./frontend
      dockerfile: Dockerfile.production
    container_name: exopiper-frontend-1
    environment:
      - NEXT_PUBLIC_API_URL=https://api.exopiper.com
      - NODE_ENV=production
      - INSTANCE_ID=frontend-1
    networks:
      - exopiper-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'

  frontend-2:
    build:
      context: ./frontend
      dockerfile: Dockerfile.production
    container_name: exopiper-frontend-2
    environment:
      - NEXT_PUBLIC_API_URL=https://api.exopiper.com
      - NODE_ENV=production
      - INSTANCE_ID=frontend-2
    networks:
      - exopiper-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'

  # Celery workers (multiple instances)
  celery-1:
    build:
      context: ./backend
      dockerfile: Dockerfile.production
    container_name: exopiper-celery-1
    command: celery -A app.core.celery_app worker --loglevel=info --concurrency=4 --queues=benchmarks,analysis
    environment:
      - DATABASE_URL=postgresql://exoPiper:${POSTGRES_PASSWORD}@postgres:5432/exoPiper
      - REDIS_URL=redis://redis:6379/0
      - SECRET_KEY=${SECRET_KEY}
      - ENVIRONMENT=production
      - WORKER_ID=celery-1
    volumes:
      - ./backend/logs:/app/logs
      - /var/run/docker.sock:/var/run/docker.sock
    depends_on:
      - postgres
      - redis
    networks:
      - exopiper-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '2.0'
        reservations:
          memory: 1G
          cpus: '1.0'

  celery-2:
    build:
      context: ./backend
      dockerfile: Dockerfile.production
    container_name: exopiper-celery-2
    command: celery -A app.core.celery_app worker --loglevel=info --concurrency=4 --queues=payments,notifications,cleanup
    environment:
      - DATABASE_URL=postgresql://exoPiper:${POSTGRES_PASSWORD}@postgres:5432/exoPiper
      - REDIS_URL=redis://redis:6379/0
      - SECRET_KEY=${SECRET_KEY}
      - ENVIRONMENT=production
      - WORKER_ID=celery-2
    volumes:
      - ./backend/logs:/app/logs
    depends_on:
      - postgres
      - redis
    networks:
      - exopiper-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'

  # Celery Beat (scheduler)
  celery-beat:
    build:
      context: ./backend
      dockerfile: Dockerfile.production
    container_name: exopiper-celery-beat
    command: celery -A app.core.celery_app beat --loglevel=info
    environment:
      - DATABASE_URL=postgresql://exoPiper:${POSTGRES_PASSWORD}@postgres:5432/exoPiper
      - REDIS_URL=redis://redis:6379/0
      - SECRET_KEY=${SECRET_KEY}
      - ENVIRONMENT=production
    volumes:
      - ./backend/logs:/app/logs
      - celery-beat-data:/app/celerybeat-schedule
    depends_on:
      - postgres
      - redis
    networks:
      - exopiper-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.25'

  # Database (single instance with replication)
  postgres:
    image: postgres:15-alpine
    container_name: exopiper-postgres
    environment:
      - POSTGRES_DB=exoPiper
      - POSTGRES_USER=exoPiper
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_INITDB_ARGS=--auth-host=scram-sha-256
    volumes:
      - postgres-data:/var/lib/postgresql/data
      - ./postgres/postgresql.conf:/etc/postgresql/postgresql.conf:ro
      - ./postgres/init:/docker-entrypoint-initdb.d
    command: postgres -c config_file=/etc/postgresql/postgresql.conf
    networks:
      - exopiper-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U exoPiper -d exoPiper"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '2.0'
        reservations:
          memory: 1G
          cpus: '1.0'

  # Redis (single instance with persistence)
  redis:
    image: redis:7-alpine
    container_name: exopiper-redis
    command: redis-server --appendonly yes --maxmemory 512mb --maxmemory-policy allkeys-lru
    volumes:
      - redis-data:/data
    networks:
      - exopiper-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'

  # MinIO (object storage)
  minio:
    image: minio/minio:latest
    container_name: exopiper-minio
    command: server /data --console-address ":9001"
    environment:
      - MINIO_ROOT_USER=${MINIO_ROOT_USER:-admin}
      - MINIO_ROOT_PASSWORD=${MINIO_ROOT_PASSWORD:-password}
    volumes:
      - minio-data:/data
    networks:
      - exopiper-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'

  # Monitoring with Prometheus
  prometheus:
    image: prom/prometheus:latest
    container_name: exopiper-prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus-data:/prometheus
    networks:
      - exopiper-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'

  # Grafana for visualization
  grafana:
    image: grafana/grafana:latest
    container_name: exopiper-grafana
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin}
    volumes:
      - grafana-data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    networks:
      - exopiper-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'

volumes:
  postgres-data:
  redis-data:
  minio-data:
  prometheus-data:
  grafana-data:
  celery-beat-data:

networks:
  exopiper-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
