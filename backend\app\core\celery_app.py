"""
Celery configuration for async task processing
"""

from celery import Celery
from .config import settings
from .celery_beat_schedule import beat_schedule, task_routes

# Create Celery app
celery_app = Celery(
    "exoPiper",
    broker=settings.CELERY_BROKER_URL,
    backend=settings.CELERY_RESULT_BACKEND,
    include=[
        "app.services.benchmark_tasks",
        "app.services.analysis_tasks",
        "app.services.notification_tasks"
    ]
)

# Celery configuration
celery_app.conf.update(
    task_serializer="json",
    accept_content=["json"],
    result_serializer="json",
    timezone="UTC",
    enable_utc=True,
    task_track_started=True,
    task_time_limit=30 * 60,  # 30 minutes
    task_soft_time_limit=25 * 60,  # 25 minutes
    worker_prefetch_multiplier=1,
    worker_max_tasks_per_child=1000,

    # Task routing
    task_routes=task_routes,
)

# Beat schedule for periodic tasks
celery_app.conf.beat_schedule = beat_schedule

celery_app.conf.timezone = "UTC"
