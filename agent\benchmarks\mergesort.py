"""
Merge Sort benchmark implementation
"""

import random
from typing import List, Dict, Any
from .base import BaseBenchmark


class MergeSortBenchmark(BaseBenchmark):
    """Merge Sort algorithm benchmark"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.validate_config(['max_size'])
    
    def get_algorithm_name(self) -> str:
        return "mergesort"
    
    def mergesort(self, arr: List[int]) -> List[int]:
        """Merge sort implementation"""
        if len(arr) <= 1:
            return arr
        
        mid = len(arr) // 2
        left = self.mergesort(arr[:mid])
        right = self.mergesort(arr[mid:])
        
        return self.merge(left, right)
    
    def merge(self, left: List[int], right: List[int]) -> List[int]:
        """Merge two sorted arrays"""
        result = []
        i = j = 0
        
        while i < len(left) and j < len(right):
            if left[i] <= right[j]:
                result.append(left[i])
                i += 1
            else:
                result.append(right[j])
                j += 1
        
        result.extend(left[i:])
        result.extend(right[j:])
        return result
    
    def generate_test_data(self, size: int) -> List[int]:
        """Generate random test data"""
        return [random.randint(1, size * 10) for _ in range(size)]
    
    def benchmark_mergesort(self, input_size: int) -> None:
        """Benchmark merge sort for a specific input size"""
        test_data = self.generate_test_data(input_size)
        sorted_data = self.mergesort(test_data.copy())
        
        # Verify correctness
        expected = sorted(test_data)
        if sorted_data != expected:
            raise ValueError(f"Merge sort failed for input size {input_size}")
    
    def run(self) -> List[Dict[str, Any]]:
        """Run merge sort benchmark"""
        self.log("Starting Merge Sort benchmark")
        
        input_sizes = self.get_input_sizes()
        results = []
        
        for size in input_sizes:
            self.log(f"Benchmarking merge sort with input size: {size}")
            
            try:
                result = self.run_multiple_iterations(self.benchmark_mergesort, size)
                results.append(result)
                
                self.log(f"Completed size {size}: {result['execution_time_ns']} ns")
                
            except Exception as e:
                self.log(f"Error with input size {size}: {e}")
                continue
        
        self.log(f"Merge Sort benchmark completed: {len(results)} data points")
        return results
