# 🤝 Contribuindo para o Exo Piper

Obrigado por seu interesse em contribuir para o Exo Piper! Este documento fornece diretrizes para contribuições efetivas.

## 📋 **Índice**

1. [Como Contribuir](#como-contribuir)
2. [Tipos de Contribuição](#tipos-de-contribuição)
3. [Processo de Desenvolvimento](#processo-de-desenvolvimento)
4. [Padrões de Código](#padrões-de-código)
5. [Testes](#testes)
6. [Documentação](#documentação)
7. [Comunidade](#comunidade)

## 🚀 **Como Contribuir** {#como-contribuir}

### **Primeiros Passos**

1. **Fork** o repositório no GitHub
2. **Clone** seu fork localmente
3. **Configure** o ambiente de desenvolvimento
4. **Crie** uma branch para sua contribuição
5. **Faça** suas mudanças
6. **Teste** completamente
7. **Submeta** um Pull Request

```bash
# 1. Fork no GitHub (botão Fork)

# 2. Clone seu fork
git clone https://github.com/SEU_USERNAME/exopiper.git
cd exopiper

# 3. Configure o upstream
git remote add upstream https://github.com/exopiper/exopiper.git

# 4. Configure o ambiente
./setup-permissions.sh

# 5. Crie uma branch
git checkout -b feature/minha-nova-funcionalidade

# 6. Faça suas mudanças
# ... código ...

# 7. Commit e push
git add .
git commit -m "feat: adiciona nova funcionalidade X"
git push origin feature/minha-nova-funcionalidade

# 8. Abra um Pull Request no GitHub
```

### **Mantendo seu Fork Atualizado**

```bash
# Buscar mudanças do upstream
git fetch upstream

# Merge das mudanças na main
git checkout main
git merge upstream/main

# Push para seu fork
git push origin main
```

## 🎯 **Tipos de Contribuição** {#tipos-de-contribuição}

### **🐛 Correção de Bugs**

1. **Verifique** se o bug já foi reportado
2. **Crie** uma issue se necessário
3. **Reproduza** o bug localmente
4. **Corrija** o problema
5. **Adicione** testes para prevenir regressão

### **✨ Novas Funcionalidades**

1. **Discuta** a funcionalidade em uma issue primeiro
2. **Aguarde** aprovação dos mantenedores
3. **Implemente** seguindo os padrões
4. **Documente** a nova funcionalidade
5. **Adicione** testes abrangentes

### **📚 Documentação**

- Correções de typos
- Melhorias na clareza
- Novos exemplos
- Traduções
- Tutoriais

### **🧪 Testes**

- Aumentar cobertura de testes
- Testes de edge cases
- Testes de performance
- Testes de integração

### **🎨 UI/UX**

- Melhorias na interface
- Acessibilidade
- Responsividade
- Experiência do usuário

## 🔄 **Processo de Desenvolvimento** {#processo-de-desenvolvimento}

### **Workflow Git**

```
main (produção)
├── develop (desenvolvimento)
│   ├── feature/nova-funcionalidade
│   ├── bugfix/correcao-bug
│   └── hotfix/correcao-urgente
└── release/v1.2.0
```

### **Branches**

- **`main`**: Código de produção estável
- **`develop`**: Integração de novas funcionalidades
- **`feature/*`**: Novas funcionalidades
- **`bugfix/*`**: Correções de bugs
- **`hotfix/*`**: Correções urgentes
- **`release/*`**: Preparação de releases

### **Convenções de Nomenclatura**

```bash
# Features
feature/auth-oauth2
feature/payment-bitcoin
feature/dashboard-analytics

# Bug fixes
bugfix/memory-leak-agent
bugfix/login-validation
bugfix/chart-rendering

# Hotfixes
hotfix/security-vulnerability
hotfix/payment-gateway-down

# Releases
release/v1.2.0
release/v2.0.0-beta
```

## 📝 **Padrões de Código** {#padrões-de-código}

### **Mensagens de Commit**

Seguimos o padrão [Conventional Commits](https://www.conventionalcommits.org/):

```bash
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

#### **Tipos**
- **feat**: Nova funcionalidade
- **fix**: Correção de bug
- **docs**: Documentação
- **style**: Formatação (sem mudança de lógica)
- **refactor**: Refatoração de código
- **test**: Adição ou correção de testes
- **chore**: Manutenção (build, CI, etc.)
- **perf**: Melhoria de performance
- **ci**: Mudanças no CI/CD

#### **Exemplos**
```bash
feat(auth): add OAuth2 Google integration
fix(payment): resolve USDT validation error
docs(api): update authentication examples
style(frontend): format components with prettier
refactor(backend): extract payment service
test(benchmark): add integration tests
chore(deps): update dependencies
perf(database): optimize user queries
ci(github): add automated security scanning
```

### **Código Python (Backend)**

```python
"""
Module docstring explaining purpose and usage.
"""

from typing import List, Optional, Dict, Any
from datetime import datetime

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from pydantic import BaseModel, Field

from ..core.database import get_db
from ..core.auth import get_current_user
from ..models.user import User
from ..services.user_service import UserService

router = APIRouter(prefix="/users", tags=["users"])


class UserCreate(BaseModel):
    """Schema for user creation."""
    
    username: str = Field(..., min_length=3, max_length=50)
    email: str = Field(..., regex=r'^[^@]+@[^@]+\.[^@]+$')
    password: str = Field(..., min_length=8)


class UserResponse(BaseModel):
    """Schema for user response."""
    
    id: int
    username: str
    email: str
    created_at: datetime
    is_active: bool

    class Config:
        from_attributes = True


@router.post("/", response_model=UserResponse, status_code=status.HTTP_201_CREATED)
async def create_user(
    user_data: UserCreate,
    db: AsyncSession = Depends(get_db),
    user_service: UserService = Depends()
) -> UserResponse:
    """
    Create a new user.
    
    Args:
        user_data: User creation data
        db: Database session
        user_service: User service instance
        
    Returns:
        Created user data
        
    Raises:
        HTTPException: If user already exists
    """
    try:
        user = await user_service.create_user(db, user_data)
        return UserResponse.from_orm(user)
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
```

### **Código TypeScript (Frontend)**

```typescript
'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { toast } from 'react-hot-toast'

import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { useAuth } from '@/hooks/useAuth'
import { userService } from '@/services/userService'

interface User {
  id: number
  username: string
  email: string
  createdAt: string
  isActive: boolean
}

interface UserFormData {
  username: string
  email: string
  password: string
}

export default function UserManagement() {
  const router = useRouter()
  const { user: currentUser } = useAuth()
  const [users, setUsers] = useState<User[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [formData, setFormData] = useState<UserFormData>({
    username: '',
    email: '',
    password: ''
  })

  useEffect(() => {
    fetchUsers()
  }, [])

  const fetchUsers = async (): Promise<void> => {
    try {
      setIsLoading(true)
      const response = await userService.getUsers()
      setUsers(response.data)
    } catch (error) {
      toast.error('Erro ao carregar usuários')
      console.error('Error fetching users:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleSubmit = async (e: React.FormEvent): Promise<void> => {
    e.preventDefault()
    
    try {
      await userService.createUser(formData)
      toast.success('Usuário criado com sucesso')
      setFormData({ username: '', email: '', password: '' })
      await fetchUsers()
    } catch (error) {
      toast.error('Erro ao criar usuário')
      console.error('Error creating user:', error)
    }
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>): void => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
  }

  if (isLoading) {
    return <div className="flex justify-center p-8">Carregando...</div>
  }

  return (
    <div className="container mx-auto p-6">
      <Card>
        <CardHeader>
          <CardTitle>Gerenciar Usuários</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4 mb-6">
            <Input
              name="username"
              placeholder="Nome de usuário"
              value={formData.username}
              onChange={handleInputChange}
              required
            />
            <Input
              name="email"
              type="email"
              placeholder="Email"
              value={formData.email}
              onChange={handleInputChange}
              required
            />
            <Input
              name="password"
              type="password"
              placeholder="Senha"
              value={formData.password}
              onChange={handleInputChange}
              required
            />
            <Button type="submit">Criar Usuário</Button>
          </form>

          <div className="space-y-2">
            {users.map((user) => (
              <div
                key={user.id}
                className="flex justify-between items-center p-3 border rounded"
              >
                <div>
                  <span className="font-medium">{user.username}</span>
                  <span className="text-gray-500 ml-2">{user.email}</span>
                </div>
                <span
                  className={`px-2 py-1 rounded text-sm ${
                    user.isActive
                      ? 'bg-green-100 text-green-800'
                      : 'bg-red-100 text-red-800'
                  }`}
                >
                  {user.isActive ? 'Ativo' : 'Inativo'}
                </span>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
```

## 🧪 **Testes** {#testes}

### **Requisitos de Teste**

- **Cobertura mínima**: 80%
- **Todos os testes devem passar**
- **Testes para edge cases**
- **Testes de regressão para bugs**

### **Executando Testes**

```bash
# Todos os testes
./run-all-tests.sh

# Backend apenas
cd backend
pytest --cov=app --cov-report=html

# Frontend apenas
cd frontend
npm test

# E2E
cd frontend
npm run test:e2e

# Testes específicos
pytest tests/test_auth.py::test_login_success
npm test -- --testNamePattern="user login"
```

### **Escrevendo Testes**

#### **Teste Unitário**
```python
import pytest
from unittest.mock import Mock, patch
from app.services.user_service import UserService

@pytest.fixture
def user_service():
    return UserService()

@pytest.fixture
def mock_db():
    return Mock()

@pytest.mark.asyncio
async def test_create_user_success(user_service, mock_db):
    # Arrange
    user_data = {
        "username": "testuser",
        "email": "<EMAIL>",
        "password": "password123"
    }
    
    # Act
    result = await user_service.create_user(mock_db, user_data)
    
    # Assert
    assert result.username == "testuser"
    assert result.email == "<EMAIL>"
    mock_db.add.assert_called_once()
    mock_db.commit.assert_called_once()
```

#### **Teste de Integração**
```python
import pytest
from httpx import AsyncClient
from app.main import app

@pytest.mark.asyncio
async def test_user_registration_flow():
    async with AsyncClient(app=app, base_url="http://test") as ac:
        # Register user
        response = await ac.post("/api/v1/auth/register", json={
            "username": "newuser",
            "email": "<EMAIL>",
            "password": "password123"
        })
        assert response.status_code == 201
        
        # Login
        login_response = await ac.post("/api/v1/auth/login", json={
            "email": "<EMAIL>",
            "password": "password123"
        })
        assert login_response.status_code == 200
        token = login_response.json()["access_token"]
        
        # Access protected endpoint
        profile_response = await ac.get(
            "/api/v1/auth/me",
            headers={"Authorization": f"Bearer {token}"}
        )
        assert profile_response.status_code == 200
        assert profile_response.json()["email"] == "<EMAIL>"
```

## 📚 **Documentação** {#documentação}

### **Tipos de Documentação**

1. **Código**: Docstrings e comentários
2. **API**: OpenAPI/Swagger automático
3. **Usuário**: Guias e tutoriais
4. **Desenvolvedor**: Guias técnicos

### **Padrões de Documentação**

#### **Python Docstrings**
```python
def calculate_complexity(execution_times: List[float], input_sizes: List[int]) -> Dict[str, Any]:
    """
    Calculate algorithmic complexity using the Relativity Theorem.
    
    This function analyzes execution times across different input sizes
    to determine the algorithmic complexity (Big O notation) with
    statistical confidence.
    
    Args:
        execution_times: List of execution times in seconds
        input_sizes: List of corresponding input sizes
        
    Returns:
        Dictionary containing:
            - lambda_value: Complexity exponent
            - complexity_class: Big O notation (e.g., "O(n)")
            - confidence: Statistical confidence (0-1)
            - r_squared: Correlation coefficient
            
    Raises:
        ValueError: If input lists have different lengths
        StatisticsError: If insufficient data for analysis
        
    Example:
        >>> times = [0.001, 0.004, 0.016, 0.064]
        >>> sizes = [10, 20, 40, 80]
        >>> result = calculate_complexity(times, sizes)
        >>> print(result['complexity_class'])
        'O(n^2)'
    """
```

#### **TypeScript JSDoc**
```typescript
/**
 * Executes a benchmark for a given workload
 * 
 * @param workloadId - The ID of the workload to benchmark
 * @param config - Benchmark configuration options
 * @returns Promise that resolves to benchmark results
 * 
 * @example
 * ```typescript
 * const results = await executeBenchmark(123, {
 *   inputSizes: [100, 500, 1000],
 *   iterations: 3,
 *   timeout: 60
 * })
 * console.log(results.complexity)
 * ```
 */
export async function executeBenchmark(
  workloadId: number,
  config: BenchmarkConfig
): Promise<BenchmarkResults> {
  // Implementation
}
```

## 👥 **Comunidade** {#comunidade}

### **Código de Conduta**

Seguimos o [Contributor Covenant](https://www.contributor-covenant.org/). Resumo:

- **Seja respeitoso** com todos os participantes
- **Use linguagem inclusiva** e acolhedora
- **Aceite críticas construtivas** graciosamente
- **Foque no que é melhor** para a comunidade
- **Mostre empatia** com outros membros

### **Canais de Comunicação**

- **📧 Email**: <EMAIL>
- **💬 Discord**: https://discord.gg/exopiper
- **🐛 Issues**: GitHub Issues para bugs e features
- **💡 Discussions**: GitHub Discussions para ideias
- **📱 Twitter**: @ExoPiper para atualizações

### **Reuniões da Comunidade**

- **Weekly Standup**: Segundas 10:00 UTC
- **Monthly Review**: Primeira sexta do mês
- **Quarterly Planning**: Início de cada trimestre

### **Reconhecimento**

Contribuidores são reconhecidos através de:

- **Contributors page** no site
- **Changelog** mentions
- **Social media** shoutouts
- **Swag** para contribuições significativas
- **Mentorship** opportunities

## 🎉 **Primeiras Contribuições**

### **Good First Issues**

Procure por issues marcadas com:
- `good first issue`
- `help wanted`
- `documentation`
- `beginner friendly`

### **Mentorship**

Novos contribuidores podem solicitar mentorship:
1. Comente na issue que deseja trabalhar
2. Mencione que é sua primeira contribuição
3. Um mantenedor será designado como mentor
4. Receba orientação durante todo o processo

### **Recursos para Iniciantes**

- [Git Handbook](https://guides.github.com/introduction/git-handbook/)
- [GitHub Flow](https://guides.github.com/introduction/flow/)
- [FastAPI Tutorial](https://fastapi.tiangolo.com/tutorial/)
- [Next.js Learn](https://nextjs.org/learn)
- [Python Testing](https://docs.python.org/3/library/unittest.html)

---

## 🙏 **Agradecimentos**

Obrigado por contribuir para o Exo Piper! Sua ajuda torna este projeto melhor para toda a comunidade de desenvolvedores.

**Happy contributing! 🚀**
