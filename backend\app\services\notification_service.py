"""
Notification service for user limits and system alerts
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_

from ..core.database import AsyncSessionLocal
from ..core.config import settings
from ..models.user import User, PlanType
from ..models.notification import Notification, NotificationType, NotificationStatus
from .email_service import email_service
from .slack_service import slack_service

logger = logging.getLogger(__name__)


class NotificationService:
    """Service for managing user notifications and alerts"""
    
    def __init__(self):
        self.notification_thresholds = {
            "workload_limit": [0.8, 0.9, 1.0],  # 80%, 90%, 100%
            "monthly_jobs": [0.8, 0.9, 1.0],
            "storage_usage": [0.7, 0.85, 0.95]
        }
    
    async def check_user_limits(self, user_id: int) -> Dict[str, Any]:
        """
        Check if user is approaching or has exceeded limits
        Send notifications if necessary
        """
        
        async with AsyncSessionLocal() as db:
            # Get user and their current usage
            user_query = select(User).where(User.id == user_id)
            user_result = await db.execute(user_query)
            user = user_result.scalar_one_or_none()
            
            if not user:
                return {"error": "User not found"}
            
            # Get current usage
            from .retention_service import retention_service
            usage_stats = await retention_service.get_user_usage_stats(user_id, db)
            
            # Check each limit type
            notifications_sent = []
            
            # Check workload limit
            workload_notification = await self._check_workload_limit(user, usage_stats, db)
            if workload_notification:
                notifications_sent.append(workload_notification)
            
            # Check monthly jobs limit
            jobs_notification = await self._check_monthly_jobs_limit(user, usage_stats, db)
            if jobs_notification:
                notifications_sent.append(jobs_notification)
            
            # Check storage usage
            storage_notification = await self._check_storage_limit(user, usage_stats, db)
            if storage_notification:
                notifications_sent.append(storage_notification)
            
            return {
                "user_id": user_id,
                "notifications_sent": notifications_sent,
                "current_usage": usage_stats
            }
    
    async def _check_workload_limit(
        self, 
        user: User, 
        usage_stats: Dict[str, Any], 
        db: AsyncSession
    ) -> Optional[Dict[str, Any]]:
        """Check workload limit and send notification if needed"""
        
        from .retention_service import retention_service
        limits = retention_service.get_plan_limits(user.plan_type)
        
        if limits["workloads"] == -1:  # Unlimited
            return None
        
        current_workloads = usage_stats.get("workloads", {}).get("total", 0)
        limit = limits["workloads"]
        usage_percentage = current_workloads / limit if limit > 0 else 0
        
        # Check if we need to send notification
        for threshold in self.notification_thresholds["workload_limit"]:
            if usage_percentage >= threshold:
                # Check if we already sent this notification recently
                if await self._was_notification_sent_recently(
                    user.id, 
                    NotificationType.WORKLOAD_LIMIT_WARNING,
                    threshold,
                    db
                ):
                    continue
                
                # Send notification
                notification_data = {
                    "current_count": current_workloads,
                    "limit": limit,
                    "percentage": round(usage_percentage * 100, 1),
                    "threshold": threshold
                }
                
                await self._send_limit_notification(
                    user,
                    NotificationType.WORKLOAD_LIMIT_WARNING,
                    "Limite de Workloads",
                    notification_data,
                    db
                )
                
                return {
                    "type": "workload_limit",
                    "threshold": threshold,
                    "data": notification_data
                }
        
        return None
    
    async def _check_monthly_jobs_limit(
        self, 
        user: User, 
        usage_stats: Dict[str, Any], 
        db: AsyncSession
    ) -> Optional[Dict[str, Any]]:
        """Check monthly jobs limit and send notification if needed"""
        
        from .retention_service import retention_service
        limits = retention_service.get_plan_limits(user.plan_type)
        
        if limits["monthly_jobs"] == -1:  # Unlimited
            return None
        
        current_jobs = usage_stats.get("jobs", {}).get("current_month", 0)
        limit = limits["monthly_jobs"]
        usage_percentage = current_jobs / limit if limit > 0 else 0
        
        # Check if we need to send notification
        for threshold in self.notification_thresholds["monthly_jobs"]:
            if usage_percentage >= threshold:
                # Check if we already sent this notification recently
                if await self._was_notification_sent_recently(
                    user.id, 
                    NotificationType.MONTHLY_JOBS_LIMIT_WARNING,
                    threshold,
                    db
                ):
                    continue
                
                # Send notification
                notification_data = {
                    "current_count": current_jobs,
                    "limit": limit,
                    "percentage": round(usage_percentage * 100, 1),
                    "threshold": threshold
                }
                
                await self._send_limit_notification(
                    user,
                    NotificationType.MONTHLY_JOBS_LIMIT_WARNING,
                    "Limite de Jobs Mensais",
                    notification_data,
                    db
                )
                
                return {
                    "type": "monthly_jobs_limit",
                    "threshold": threshold,
                    "data": notification_data
                }
        
        return None
    
    async def _check_storage_limit(
        self, 
        user: User, 
        usage_stats: Dict[str, Any], 
        db: AsyncSession
    ) -> Optional[Dict[str, Any]]:
        """Check storage usage and send notification if needed"""
        
        from .retention_service import retention_service
        limits = retention_service.get_plan_limits(user.plan_type)
        
        # For now, we'll use a simple storage calculation
        # In a real implementation, you'd calculate actual storage usage
        total_results = usage_stats.get("results", {}).get("total", 0)
        estimated_storage_mb = total_results * 0.5  # Estimate 0.5MB per result
        
        # Set storage limits based on plan (in MB)
        storage_limits = {
            PlanType.FREE: 100,      # 100MB
            PlanType.BASIC: 1000,    # 1GB
            PlanType.PRO: 10000,     # 10GB
            PlanType.ENTERPRISE: -1  # Unlimited
        }
        
        storage_limit = storage_limits.get(user.plan_type, 100)
        
        if storage_limit == -1:  # Unlimited
            return None
        
        usage_percentage = estimated_storage_mb / storage_limit if storage_limit > 0 else 0
        
        # Check if we need to send notification
        for threshold in self.notification_thresholds["storage_usage"]:
            if usage_percentage >= threshold:
                # Check if we already sent this notification recently
                if await self._was_notification_sent_recently(
                    user.id, 
                    NotificationType.STORAGE_LIMIT_WARNING,
                    threshold,
                    db
                ):
                    continue
                
                # Send notification
                notification_data = {
                    "current_usage_mb": round(estimated_storage_mb, 2),
                    "limit_mb": storage_limit,
                    "percentage": round(usage_percentage * 100, 1),
                    "threshold": threshold
                }
                
                await self._send_limit_notification(
                    user,
                    NotificationType.STORAGE_LIMIT_WARNING,
                    "Limite de Armazenamento",
                    notification_data,
                    db
                )
                
                return {
                    "type": "storage_limit",
                    "threshold": threshold,
                    "data": notification_data
                }
        
        return None
    
    async def _was_notification_sent_recently(
        self,
        user_id: int,
        notification_type: NotificationType,
        threshold: float,
        db: AsyncSession,
        hours: int = 24
    ) -> bool:
        """Check if a similar notification was sent recently"""
        
        cutoff_time = datetime.utcnow() - timedelta(hours=hours)
        
        notification_query = select(Notification).where(
            and_(
                Notification.user_id == user_id,
                Notification.type == notification_type,
                Notification.created_at > cutoff_time,
                Notification.metadata.contains(f'"threshold": {threshold}')
            )
        )
        
        result = await db.execute(notification_query)
        existing_notification = result.scalar_one_or_none()
        
        return existing_notification is not None
    
    async def _send_limit_notification(
        self,
        user: User,
        notification_type: NotificationType,
        title: str,
        data: Dict[str, Any],
        db: AsyncSession
    ):
        """Send limit notification via multiple channels"""
        
        # Create notification record
        notification = Notification(
            user_id=user.id,
            type=notification_type,
            title=title,
            message=self._generate_limit_message(title, data),
            metadata=data,
            status=NotificationStatus.PENDING
        )
        
        db.add(notification)
        await db.commit()
        await db.refresh(notification)
        
        # Send via email
        try:
            await self._send_email_notification(user, title, data)
            notification.email_sent = True
        except Exception as e:
            logger.error(f"Failed to send email notification: {e}")
        
        # Send via Slack if configured
        try:
            if hasattr(settings, 'SLACK_WEBHOOK_URL') and settings.SLACK_WEBHOOK_URL:
                await self._send_slack_notification(user, title, data)
                notification.slack_sent = True
        except Exception as e:
            logger.error(f"Failed to send Slack notification: {e}")
        
        # Update notification status
        notification.status = NotificationStatus.SENT
        await db.commit()
    
    def _generate_limit_message(self, title: str, data: Dict[str, Any]) -> str:
        """Generate human-readable message for limit notification"""
        
        percentage = data.get("percentage", 0)
        threshold = data.get("threshold", 0)
        
        if threshold >= 1.0:
            return f"⚠️ {title}: Você atingiu {percentage}% do seu limite!"
        elif threshold >= 0.9:
            return f"🔶 {title}: Você está próximo do limite ({percentage}% usado)."
        else:
            return f"📊 {title}: Você usou {percentage}% do seu limite."
    
    async def _send_email_notification(self, user: User, title: str, data: Dict[str, Any]):
        """Send email notification"""
        
        subject = f"Exo Piper - {title}"
        
        # Generate email content
        if "workload" in title.lower():
            content = f"""
            Olá {user.username},
            
            Você está usando {data['current_count']} de {data['limit']} workloads disponíveis 
            ({data['percentage']}% do seu limite).
            
            Para continuar criando workloads, considere fazer upgrade do seu plano.
            
            Acesse: https://exopiper.com/upgrade
            
            Atenciosamente,
            Equipe Exo Piper
            """
        elif "jobs" in title.lower():
            content = f"""
            Olá {user.username},
            
            Você executou {data['current_count']} de {data['limit']} jobs disponíveis este mês 
            ({data['percentage']}% do seu limite).
            
            Para executar mais benchmarks, considere fazer upgrade do seu plano.
            
            Acesse: https://exopiper.com/upgrade
            
            Atenciosamente,
            Equipe Exo Piper
            """
        else:
            content = f"""
            Olá {user.username},
            
            Você está usando {data.get('percentage', 0)}% do seu limite de {title.lower()}.
            
            Para mais recursos, considere fazer upgrade do seu plano.
            
            Acesse: https://exopiper.com/upgrade
            
            Atenciosamente,
            Equipe Exo Piper
            """
        
        await email_service.send_email(
            to_email=user.email,
            subject=subject,
            content=content
        )
    
    async def _send_slack_notification(self, user: User, title: str, data: Dict[str, Any]):
        """Send Slack notification"""
        
        message = f"""
        🔔 *Limite de Usuário Atingido*
        
        *Usuário:* {user.username} ({user.email})
        *Tipo:* {title}
        *Uso:* {data.get('percentage', 0)}%
        *Plano:* {user.plan_type.upper()}
        
        O usuário pode precisar fazer upgrade do plano.
        """
        
        await slack_service.send_message(message)
    
    async def send_plan_upgrade_notification(self, user_id: int, old_plan: str, new_plan: str):
        """Send notification when user upgrades plan"""
        
        async with AsyncSessionLocal() as db:
            user_query = select(User).where(User.id == user_id)
            user_result = await db.execute(user_query)
            user = user_result.scalar_one_or_none()
            
            if not user:
                return
            
            # Create notification
            notification = Notification(
                user_id=user.id,
                type=NotificationType.PLAN_UPGRADED,
                title="Plano Atualizado",
                message=f"Seu plano foi atualizado de {old_plan.upper()} para {new_plan.upper()}!",
                metadata={
                    "old_plan": old_plan,
                    "new_plan": new_plan,
                    "upgraded_at": datetime.utcnow().isoformat()
                },
                status=NotificationStatus.SENT
            )
            
            db.add(notification)
            await db.commit()
            
            # Send congratulations email
            try:
                await email_service.send_email(
                    to_email=user.email,
                    subject="Exo Piper - Plano Atualizado com Sucesso!",
                    content=f"""
                    Parabéns {user.username}!
                    
                    Seu plano foi atualizado com sucesso de {old_plan.upper()} para {new_plan.upper()}.
                    
                    Agora você tem acesso a mais recursos e funcionalidades.
                    
                    Acesse seu dashboard: https://exopiper.com/dashboard
                    
                    Obrigado por escolher o Exo Piper!
                    
                    Equipe Exo Piper
                    """
                )
            except Exception as e:
                logger.error(f"Failed to send plan upgrade email: {e}")
    
    async def check_all_users_limits(self):
        """Check limits for all active users (for scheduled task)"""
        
        async with AsyncSessionLocal() as db:
            # Get all active users
            users_query = select(User).where(User.is_active == True)
            users_result = await db.execute(users_query)
            users = users_result.scalars().all()
            
            results = []
            
            for user in users:
                try:
                    result = await self.check_user_limits(user.id)
                    results.append(result)
                except Exception as e:
                    logger.error(f"Error checking limits for user {user.id}: {e}")
                    results.append({
                        "user_id": user.id,
                        "error": str(e)
                    })
            
            logger.info(f"Checked limits for {len(users)} users")
            return results


# Global notification service instance
notification_service = NotificationService()
