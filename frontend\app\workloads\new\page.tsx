'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { 
  CpuChipIcon, 
  ArrowLeftIcon,
  PlusIcon
} from '@heroicons/react/24/outline'
import { useAuth } from '@/contexts/AuthContext'
import { apiClient } from '@/lib/api'
import toast from 'react-hot-toast'

const workloadTemplates = {
  mergesort: {
    name: 'Merge Sort Analysis',
    description: 'Análise de complexidade do algoritmo Merge Sort',
    config: {
      max_size: 100000,
      iterations: 5,
      step_factor: 2
    }
  },
  '3sat': {
    name: '3-SAT Solver',
    description: 'Benchmark do solver de problemas 3-SAT',
    config: {
      max_variables: 20,
      iterations: 3,
      clause_ratio: 4.3
    }
  },
  vec2vec: {
    name: 'Vector Operations',
    description: 'Benchmark de operações vetoriais',
    config: {
      max_dimension: 1000,
      iterations: 10,
      operations: ['dot', 'matmul', 'norm']
    }
  },
  mlmodel: {
    name: 'ML Model Inference',
    description: 'Benchmark de inferência de modelos ML',
    config: {
      model_types: ['linear', 'tree', 'neural'],
      max_features: 1000,
      iterations: 5
    }
  }
}

export default function NewWorkloadPage() {
  const [selectedType, setSelectedType] = useState<string>('')
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    workload_type: '',
    config: {},
    schedule_enabled: false,
    schedule_cron: ''
  })
  const [isLoading, setIsLoading] = useState(false)
  
  const { isAuthenticated } = useAuth()
  const router = useRouter()

  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/login')
    }
  }, [isAuthenticated, router])

  const handleTypeSelect = (type: string) => {
    setSelectedType(type)
    const template = workloadTemplates[type as keyof typeof workloadTemplates]
    setFormData({
      ...formData,
      name: template.name,
      description: template.description,
      workload_type: type,
      config: template.config
    })
  }

  const handleConfigChange = (key: string, value: any) => {
    setFormData({
      ...formData,
      config: {
        ...formData.config,
        [key]: value
      }
    })
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    try {
      const workload = await apiClient.createWorkload({
        name: formData.name,
        description: formData.description,
        workload_type: formData.workload_type,
        config: formData.config,
        is_active: true,
        schedule_enabled: formData.schedule_enabled,
        schedule_cron: formData.schedule_cron || undefined
      })

      toast.success('Workload criado com sucesso!')
      router.push(`/workloads/${workload.id}`)
      
    } catch (error: any) {
      toast.error(`Falha ao criar workload: ${error.message}`)
    } finally {
      setIsLoading(false)
    }
  }

  if (!isAuthenticated) {
    return null
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center py-6">
            <button
              onClick={() => router.push('/dashboard')}
              className="mr-4 p-2 text-gray-400 hover:text-gray-600"
            >
              <ArrowLeftIcon className="h-5 w-5" />
            </button>
            <CpuChipIcon className="h-8 w-8 text-primary-600" />
            <div className="ml-3">
              <h1 className="text-2xl font-bold text-gray-900">Novo Workload</h1>
              <p className="text-sm text-gray-600">Criar um novo workload de benchmark</p>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {!selectedType ? (
          /* Type Selection */
          <div className="card">
            <h2 className="text-lg font-medium text-gray-900 mb-6">Escolha o tipo de benchmark</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {Object.entries(workloadTemplates).map(([type, template]) => (
                <button
                  key={type}
                  onClick={() => handleTypeSelect(type)}
                  className="p-6 border border-gray-200 rounded-lg hover:border-primary-500 hover:bg-primary-50 text-left transition-colors"
                >
                  <div className="flex items-center mb-3">
                    <CpuChipIcon className="h-6 w-6 text-primary-600" />
                    <h3 className="ml-2 font-medium text-gray-900">{template.name}</h3>
                  </div>
                  <p className="text-sm text-gray-600 mb-3">{template.description}</p>
                  <div className="text-xs text-gray-500">
                    Tipo: <span className="font-medium">{type}</span>
                  </div>
                </button>
              ))}
            </div>
          </div>
        ) : (
          /* Configuration Form */
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="card">
              <h2 className="text-lg font-medium text-gray-900 mb-6">Configurar Workload</h2>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Nome
                  </label>
                  <input
                    type="text"
                    value={formData.name}
                    onChange={(e) => setFormData({...formData, name: e.target.value})}
                    className="input-field"
                    required
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Tipo
                  </label>
                  <input
                    type="text"
                    value={formData.workload_type}
                    className="input-field bg-gray-50"
                    disabled
                  />
                </div>
              </div>
              
              <div className="mt-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Descrição
                </label>
                <textarea
                  value={formData.description}
                  onChange={(e) => setFormData({...formData, description: e.target.value})}
                  className="input-field"
                  rows={3}
                />
              </div>
            </div>

            <div className="card">
              <h3 className="text-lg font-medium text-gray-900 mb-6">Parâmetros de Configuração</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {Object.entries(formData.config).map(([key, value]) => (
                  <div key={key}>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      {key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                    </label>
                    {Array.isArray(value) ? (
                      <input
                        type="text"
                        value={value.join(', ')}
                        onChange={(e) => handleConfigChange(key, e.target.value.split(', '))}
                        className="input-field"
                        placeholder="Separado por vírgulas"
                      />
                    ) : typeof value === 'number' ? (
                      <input
                        type="number"
                        value={value}
                        onChange={(e) => handleConfigChange(key, parseInt(e.target.value))}
                        className="input-field"
                      />
                    ) : (
                      <input
                        type="text"
                        value={String(value)}
                        onChange={(e) => handleConfigChange(key, e.target.value)}
                        className="input-field"
                      />
                    )}
                  </div>
                ))}
              </div>
            </div>

            <div className="card">
              <h3 className="text-lg font-medium text-gray-900 mb-6">Agendamento (Opcional)</h3>
              
              <div className="flex items-center mb-4">
                <input
                  type="checkbox"
                  id="schedule_enabled"
                  checked={formData.schedule_enabled}
                  onChange={(e) => setFormData({...formData, schedule_enabled: e.target.checked})}
                  className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                />
                <label htmlFor="schedule_enabled" className="ml-2 text-sm text-gray-700">
                  Habilitar execução agendada
                </label>
              </div>
              
              {formData.schedule_enabled && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Expressão Cron
                  </label>
                  <input
                    type="text"
                    value={formData.schedule_cron}
                    onChange={(e) => setFormData({...formData, schedule_cron: e.target.value})}
                    className="input-field"
                    placeholder="0 0 * * * (diário à meia-noite)"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Formato: minuto hora dia mês dia-da-semana
                  </p>
                </div>
              )}
            </div>

            <div className="flex justify-between">
              <button
                type="button"
                onClick={() => setSelectedType('')}
                className="btn-secondary"
              >
                Voltar
              </button>
              
              <button
                type="submit"
                disabled={isLoading}
                className="btn-primary flex items-center"
              >
                {isLoading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Criando...
                  </>
                ) : (
                  <>
                    <PlusIcon className="h-4 w-4 mr-2" />
                    Criar Workload
                  </>
                )}
              </button>
            </div>
          </form>
        )}
      </div>
    </div>
  )
}
