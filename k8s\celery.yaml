apiVersion: apps/v1
kind: Deployment
metadata:
  name: celery-worker
  namespace: exopiper
  labels:
    app: celery-worker
spec:
  replicas: 3
  selector:
    matchLabels:
      app: celery-worker
  template:
    metadata:
      labels:
        app: celery-worker
    spec:
      imagePullSecrets:
      - name: docker-registry-secret
      containers:
      - name: celery-worker
        image: exopiper/backend:latest
        command:
        - celery
        - -A
        - app.core.celery_app
        - worker
        - --loglevel=info
        - --concurrency=4
        - --queues=benchmarks,analysis,payments,notifications,cleanup
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: exopiper-secrets
              key: DATABASE_URL
        - name: REDIS_URL
          valueFrom:
            configMapKeyRef:
              name: exopiper-config
              key: REDIS_URL
        - name: SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: exopiper-secrets
              key: SECRET_KEY
        - name: ENVIRONMENT
          valueFrom:
            configMapKeyRef:
              name: exopiper-config
              key: ENVIRONMENT
        - name: CELERY_BROKER_URL
          valueFrom:
            configMapKeyRef:
              name: exopiper-config
              key: CELERY_BROKER_URL
        - name: CELERY_RESULT_BACKEND
          valueFrom:
            configMapKeyRef:
              name: exopiper-config
              key: CELERY_RESULT_BACKEND
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "2"
        volumeMounts:
        - name: logs
          mountPath: /app/logs
        - name: docker-sock
          mountPath: /var/run/docker.sock
      volumes:
      - name: logs
        emptyDir: {}
      - name: docker-sock
        hostPath:
          path: /var/run/docker.sock
          type: Socket

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: celery-beat
  namespace: exopiper
  labels:
    app: celery-beat
spec:
  replicas: 1
  selector:
    matchLabels:
      app: celery-beat
  template:
    metadata:
      labels:
        app: celery-beat
    spec:
      imagePullSecrets:
      - name: docker-registry-secret
      containers:
      - name: celery-beat
        image: exopiper/backend:latest
        command:
        - celery
        - -A
        - app.core.celery_app
        - beat
        - --loglevel=info
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: exopiper-secrets
              key: DATABASE_URL
        - name: REDIS_URL
          valueFrom:
            configMapKeyRef:
              name: exopiper-config
              key: REDIS_URL
        - name: SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: exopiper-secrets
              key: SECRET_KEY
        - name: ENVIRONMENT
          valueFrom:
            configMapKeyRef:
              name: exopiper-config
              key: ENVIRONMENT
        - name: CELERY_BROKER_URL
          valueFrom:
            configMapKeyRef:
              name: exopiper-config
              key: CELERY_BROKER_URL
        - name: CELERY_RESULT_BACKEND
          valueFrom:
            configMapKeyRef:
              name: exopiper-config
              key: CELERY_RESULT_BACKEND
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "250m"
        volumeMounts:
        - name: logs
          mountPath: /app/logs
        - name: celery-beat-schedule
          mountPath: /app/celerybeat-schedule
      volumes:
      - name: logs
        emptyDir: {}
      - name: celery-beat-schedule
        persistentVolumeClaim:
          claimName: celery-beat-pvc

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: celery-beat-pvc
  namespace: exopiper
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 1Gi
  storageClassName: fast-ssd

---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: celery-worker-hpa
  namespace: exopiper
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: celery-worker
  minReplicas: 3
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
