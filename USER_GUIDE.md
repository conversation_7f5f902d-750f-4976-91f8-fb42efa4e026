# 👥 Guia do Usuário - Exo Piper

Bem-vindo ao **Exo Piper** - o sistema mais avançado para auditoria de performance de código! Este guia irá te ajudar a começar e aproveitar ao máximo todas as funcionalidades.

## 🚀 **Primeiros Passos**

### **1. Criando sua Conta**

1. Acesse [exopiper.com](https://exopiper.com)
2. Clique em "Registrar"
3. Preencha seus dados:
   - **Nome de usuário**: Único no sistema
   - **Email**: Para notificações e recuperação
   - **Senha**: Mínimo 8 caracteres
4. Confirme seu email (verifique a caixa de spam)
5. Faça login e comece a usar!

### **2. Conhecendo o Dashboard**

Após o login, você verá o dashboard principal com:

- **📊 Visão Geral**: Estatísticas dos seus workloads
- **🔧 Workloads Ativos**: Lista dos seus projetos
- **📈 Performance Recente**: Gráficos dos últimos benchmarks
- **⚡ Jobs em Execução**: Status dos benchmarks ativos
- **💳 Plano Atual**: Informações da sua assinatura

## 🔧 **Gerenciando Workloads**

### **O que são Workloads?**

Workloads são os códigos que você quer analisar. Cada workload representa uma função, algoritmo ou trecho de código que você deseja auditar.

### **Criando um Workload**

1. Clique em "**Novo Workload**" no dashboard
2. Preencha as informações:
   - **Nome**: Identificação do seu código
   - **Descrição**: Explicação do que o código faz
   - **Linguagem**: Python, JavaScript, Java, etc.
   - **Código**: Cole ou digite seu código
   - **Complexidade Esperada**: O(n), O(n²), O(log n), etc.

3. Clique em "**Criar Workload**"

### **Exemplo de Workload**

```python
# Nome: Ordenação Bubble Sort
# Descrição: Implementação clássica do algoritmo bubble sort
# Complexidade Esperada: O(n²)

def bubble_sort(arr):
    n = len(arr)
    for i in range(n):
        for j in range(0, n-i-1):
            if arr[j] > arr[j+1]:
                arr[j], arr[j+1] = arr[j+1], arr[j]
    return arr
```

### **Editando Workloads**

1. Vá para "**Meus Workloads**"
2. Clique no workload que deseja editar
3. Clique em "**Editar**"
4. Faça as alterações necessárias
5. Salve as mudanças

## 🏃 **Executando Benchmarks**

### **Como Funciona**

O Exo Piper executa seu código com diferentes tamanhos de entrada e mede:
- **Tempo de execução**
- **Uso de memória**
- **Complexidade algorítmica**
- **Eficiência relativa**

### **Executando um Benchmark**

1. Selecione um workload
2. Clique em "**Executar Benchmark**"
3. Configure os parâmetros:
   - **Tamanhos de Entrada**: Ex: [100, 500, 1000, 5000]
   - **Iterações**: Quantas vezes executar cada teste
   - **Timeout**: Tempo máximo por execução

4. Clique em "**Iniciar**"

### **Acompanhando o Progresso**

- **Status em Tempo Real**: Veja o progresso na tela
- **Notificações**: Receba alertas quando completar
- **Logs**: Acompanhe detalhes da execução

## 📊 **Entendendo os Resultados**

### **Análise de Complexidade**

O Exo Piper usa o **Teorema da Relatividade** para determinar a complexidade:

- **λ (Lambda)**: Expoente da complexidade
  - λ ≈ 1: O(n) - Linear
  - λ ≈ 2: O(n²) - Quadrática
  - λ ≈ 0: O(log n) - Logarítmica

- **p-value**: Confiança estatística
  - p < 0.05: Resultado confiável
  - p > 0.05: Resultado incerto

### **Métricas de Performance**

- **Tempo de Execução**: Medido em milissegundos
- **Uso de Memória**: Medido em MB
- **Eficiência**: Comparação com algoritmos similares
- **Escalabilidade**: Como performa com entradas maiores

### **Gráficos e Visualizações**

- **Gráfico de Tempo**: Tempo vs Tamanho da entrada
- **Gráfico de Memória**: Uso de memória vs Entrada
- **Curva de Complexidade**: Ajuste matemático
- **Comparações**: Lado a lado com outros algoritmos

## 💳 **Planos e Pagamentos**

### **Planos Disponíveis**

| Plano | Preço | Workloads | Jobs/Mês | Retenção |
|-------|-------|-----------|----------|----------|
| **Free** | Grátis | 3 | 50 | 30 dias |
| **Basic** | $50/mês | 10 | 200 | 90 dias |
| **Pro** | $200/mês | 50 | 1000 | 1 ano |
| **Enterprise** | $1000/mês | ∞ | ∞ | ∞ |

### **Como Fazer Upgrade**

1. Clique em "**Upgrade**" no dashboard
2. Escolha o plano desejado
3. Selecione o método de pagamento:
   - **USDT BEP20** (Binance Smart Chain) - Taxas baixas
   - **USDT ERC20** (Ethereum) - Mais seguro
   - **Bitcoin via TANOS** - Swaps atômicos

4. Escaneie o QR Code ou copie o endereço
5. Envie o pagamento exato
6. Aguarde a confirmação (1-15 minutos)
7. Seu plano será atualizado automaticamente!

### **Wallet de Pagamento**
```
******************************************
```

## 🔔 **Sistema de Alertas**

### **Configurando Alertas**

1. Vá para "**Configurações**" → "**Alertas**"
2. Configure os thresholds:
   - **Tempo de execução** > X segundos
   - **Uso de memória** > X MB
   - **Degradação de performance** > X%

3. Escolha os canais de notificação:
   - **Email**: Notificações por email
   - **Slack**: Integração com workspace
   - **Dashboard**: Alertas na interface

### **Tipos de Alertas**

- **⚠️ Performance Degradada**: Código ficou mais lento
- **🚨 Limite Excedido**: Ultrapassou thresholds
- **✅ Benchmark Completo**: Execução finalizada
- **❌ Falha na Execução**: Erro durante o benchmark

## 🛠️ **CLI - Interface de Linha de Comando**

### **Instalação**

```bash
pip install exo-piper-cli
```

### **Comandos Básicos**

```bash
# Login
perf-audit login

# Listar workloads
perf-audit list

# Executar benchmark
perf-audit run --workload-id 123 --sizes 100,500,1000

# Ver resultados
perf-audit results --job-id job_456

# Agendar execução
perf-audit schedule --workload-id 123 --cron "0 2 * * *"
```

### **Configuração**

```bash
# Configurar API endpoint
perf-audit config set api_url https://api.exopiper.com

# Configurar token
perf-audit config set token your_api_token

# Ver configurações
perf-audit config list
```

## 📈 **Melhores Práticas**

### **Escrevendo Código para Benchmark**

1. **Função Principal**: Tenha uma função principal clara
2. **Parâmetros**: Use parâmetros que escalem (tamanho da entrada)
3. **Determinístico**: Evite randomização desnecessária
4. **Limpo**: Código limpo e bem documentado

### **Configurando Benchmarks**

1. **Tamanhos Progressivos**: Use sequência crescente (100, 200, 500, 1000)
2. **Iterações Suficientes**: Pelo menos 3-5 iterações
3. **Timeout Adequado**: Considere a complexidade esperada
4. **Ambiente Consistente**: Execute em condições similares

### **Interpretando Resultados**

1. **Verifique p-value**: Deve ser < 0.05 para confiabilidade
2. **Compare com Expectativa**: λ próximo da complexidade esperada?
3. **Analise Outliers**: Investigue pontos fora da curva
4. **Considere Contexto**: Tamanho dos dados, hardware, etc.

## 🔧 **Troubleshooting**

### **Problemas Comuns**

**❌ "Workload não executa"**
- Verifique sintaxe do código
- Confirme que a função principal está definida
- Teste localmente primeiro

**❌ "Benchmark muito lento"**
- Reduza tamanhos de entrada
- Aumente timeout
- Verifique complexidade do algoritmo

**❌ "Resultados inconsistentes"**
- Aumente número de iterações
- Verifique se há randomização
- Execute em horários de menor carga

**❌ "Pagamento não confirmado"**
- Verifique se enviou valor exato
- Confirme rede correta (BEP20/ERC20)
- Aguarde tempo de confirmação

### **Suporte**

- **📧 Email**: <EMAIL>
- **💬 Chat**: Disponível no dashboard
- **📚 Docs**: https://docs.exopiper.com
- **🐛 Issues**: https://github.com/exopiper/exopiper/issues

## 🎯 **Casos de Uso**

### **Para Desenvolvedores**
- Otimizar algoritmos críticos
- Comparar implementações
- Validar complexidade teórica
- Monitorar regressões de performance

### **Para Equipes**
- Code reviews baseados em performance
- Benchmarks automatizados em CI/CD
- Alertas de degradação
- Relatórios de performance

### **Para Educação**
- Ensinar complexidade algorítmica
- Demonstrar diferenças práticas
- Exercícios de otimização
- Análise comparativa

## 🏆 **Dicas Avançadas**

1. **Use o CLI para automação** em pipelines CI/CD
2. **Configure alertas proativos** para detectar regressões
3. **Compare algoritmos similares** para escolher o melhor
4. **Monitore tendências** ao longo do tempo
5. **Documente descobertas** para a equipe

---

**🎉 Parabéns!** Agora você está pronto para usar o Exo Piper como um profissional. Comece criando seu primeiro workload e descubra insights incríveis sobre a performance do seu código!
