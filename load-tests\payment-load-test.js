import http from 'k6/http';
import { check, sleep } from 'k6';
import { Rate, Counter } from 'k6/metrics';

// Custom metrics
const errorRate = new Rate('errors');
const paymentCreations = new Counter('payment_creations');
const paymentChecks = new Counter('payment_checks');

// Test configuration for payment system stress testing
export const options = {
  scenarios: {
    payment_creation: {
      executor: 'ramping-vus',
      startVUs: 0,
      stages: [
        { duration: '1m', target: 5 },
        { duration: '3m', target: 5 },
        { duration: '1m', target: 15 },
        { duration: '3m', target: 15 },
        { duration: '1m', target: 0 },
      ],
      gracefulRampDown: '30s',
    },
    payment_monitoring: {
      executor: 'constant-vus',
      vus: 10,
      duration: '8m',
      startTime: '1m',
    },
  },
  thresholds: {
    http_req_duration: ['p(95)<5000'], // 95% of requests under 5s (payments can be slower)
    http_req_failed: ['rate<0.05'],    // Error rate under 5%
    errors: ['rate<0.05'],
  },
};

const BASE_URL = __ENV.BASE_URL || 'http://localhost:8000';

// Test users for payment testing
const paymentUsers = [
  { email: '<EMAIL>', password: 'testpass123' },
  { email: '<EMAIL>', password: 'testpass123' },
  { email: '<EMAIL>', password: 'testpass123' },
];

export function setup() {
  console.log('Setting up payment load test...');
  
  // Create test users
  paymentUsers.forEach((user, index) => {
    const registerPayload = {
      username: `paymentuser${index + 1}`,
      email: user.email,
      password: user.password,
    };
    
    const response = http.post(
      `${BASE_URL}/api/v1/auth/register`,
      JSON.stringify(registerPayload),
      { headers: { 'Content-Type': 'application/json' } }
    );
    
    console.log(`Payment user ${user.email} setup: ${response.status}`);
  });
  
  return { users: paymentUsers };
}

export default function (data) {
  const scenario = __ENV.K6_SCENARIO_NAME || 'payment_creation';
  
  if (scenario === 'payment_creation') {
    testPaymentCreation(data);
  } else if (scenario === 'payment_monitoring') {
    testPaymentMonitoring(data);
  }
}

function testPaymentCreation(data) {
  // Select random user
  const user = data.users[Math.floor(Math.random() * data.users.length)];
  
  // Login
  const loginResponse = http.post(
    `${BASE_URL}/api/v1/auth/login`,
    JSON.stringify({ email: user.email, password: user.password }),
    { headers: { 'Content-Type': 'application/json' } }
  );
  
  if (!check(loginResponse, { 'login successful': (r) => r.status === 200 })) {
    errorRate.add(1);
    return;
  }
  
  const token = JSON.parse(loginResponse.body).access_token;
  const authHeaders = {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`,
  };
  
  sleep(0.5);
  
  // Test 1: Get available plans
  const plansResponse = http.get(`${BASE_URL}/api/v1/payments/plans`, {
    headers: authHeaders,
  });
  
  check(plansResponse, {
    'plans status is 200': (r) => r.status === 200,
    'plans response time < 2s': (r) => r.timings.duration < 2000,
    'plans contain basic plan': (r) => {
      try {
        const body = JSON.parse(r.body);
        return body.plans.some(plan => plan.name === 'basic');
      } catch (e) {
        return false;
      }
    },
  }) || errorRate.add(1);
  
  sleep(1);
  
  // Test 2: Create USDT payment
  const paymentPayload = {
    plan_type: 'basic',
    network: Math.random() > 0.5 ? 'bep20' : 'erc20', // Random network
  };
  
  const paymentResponse = http.post(
    `${BASE_URL}/api/v1/payments/create`,
    JSON.stringify(paymentPayload),
    { headers: authHeaders }
  );
  
  const paymentSuccess = check(paymentResponse, {
    'payment creation status is 200': (r) => r.status === 200,
    'payment creation response time < 5s': (r) => r.timings.duration < 5000,
    'payment contains wallet address': (r) => {
      try {
        const body = JSON.parse(r.body);
        return body.payment && body.payment.wallet_address;
      } catch (e) {
        return false;
      }
    },
    'payment contains QR code': (r) => {
      try {
        const body = JSON.parse(r.body);
        return body.payment && body.payment.qr_code;
      } catch (e) {
        return false;
      }
    },
  });
  
  if (paymentSuccess) {
    paymentCreations.add(1);
    
    const paymentData = JSON.parse(paymentResponse.body).payment;
    
    sleep(2);
    
    // Test 3: Check payment status
    const statusResponse = http.get(
      `${BASE_URL}/api/v1/payments/status/${paymentData.payment_id}`,
      { headers: authHeaders }
    );
    
    check(statusResponse, {
      'payment status check is 200': (r) => r.status === 200,
      'payment status response time < 3s': (r) => r.timings.duration < 3000,
      'payment status is pending': (r) => {
        try {
          const body = JSON.parse(r.body);
          return body.status === 'pending';
        } catch (e) {
          return false;
        }
      },
    }) || errorRate.add(1);
    
  } else {
    errorRate.add(1);
  }
  
  sleep(1);
  
  // Test 4: Create Bitcoin payment (10% chance)
  if (Math.random() < 0.1) {
    const bitcoinPayload = {
      plan_type: 'basic',
    };
    
    const bitcoinResponse = http.post(
      `${BASE_URL}/api/v1/payments/tanos/create`,
      JSON.stringify(bitcoinPayload),
      { headers: authHeaders }
    );
    
    check(bitcoinResponse, {
      'bitcoin payment creation status is 200': (r) => r.status === 200,
      'bitcoin payment response time < 10s': (r) => r.timings.duration < 10000,
    }) || errorRate.add(1);
    
    sleep(2);
  }
}

function testPaymentMonitoring(data) {
  // This scenario continuously monitors payment endpoints
  const user = data.users[Math.floor(Math.random() * data.users.length)];
  
  // Login
  const loginResponse = http.post(
    `${BASE_URL}/api/v1/auth/login`,
    JSON.stringify({ email: user.email, password: user.password }),
    { headers: { 'Content-Type': 'application/json' } }
  );
  
  if (!check(loginResponse, { 'login successful': (r) => r.status === 200 })) {
    errorRate.add(1);
    return;
  }
  
  const token = JSON.parse(loginResponse.body).access_token;
  const authHeaders = {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`,
  };
  
  // Test payment history endpoint
  const historyResponse = http.get(`${BASE_URL}/api/v1/payments/history`, {
    headers: authHeaders,
  });
  
  check(historyResponse, {
    'payment history status is 200': (r) => r.status === 200,
    'payment history response time < 2s': (r) => r.timings.duration < 2000,
  }) || errorRate.add(1);
  
  paymentChecks.add(1);
  
  sleep(5);
  
  // Test plans endpoint
  const plansResponse = http.get(`${BASE_URL}/api/v1/payments/plans`, {
    headers: authHeaders,
  });
  
  check(plansResponse, {
    'plans monitoring status is 200': (r) => r.status === 200,
    'plans monitoring response time < 1s': (r) => r.timings.duration < 1000,
  }) || errorRate.add(1);
  
  sleep(10);
}

export function teardown(data) {
  console.log('Payment load test completed');
  console.log(`Total payment creations: ${paymentCreations.count}`);
  console.log(`Total payment checks: ${paymentChecks.count}`);
  
  // Cleanup test payments
  const adminLogin = http.post(
    `${BASE_URL}/api/v1/auth/login`,
    JSON.stringify({
      email: '<EMAIL>',
      password: 'admin123',
    }),
    { headers: { 'Content-Type': 'application/json' } }
  );
  
  if (adminLogin.status === 200) {
    const adminToken = JSON.parse(adminLogin.body).access_token;
    
    // Clean up test payments
    http.post(
      `${BASE_URL}/api/v1/admin/cleanup/test-payments`,
      '{}',
      {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${adminToken}`,
        },
      }
    );
    
    console.log('Test payments cleaned up');
  }
}
