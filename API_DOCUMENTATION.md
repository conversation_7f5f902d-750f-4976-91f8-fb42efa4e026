# 📚 Exo Piper API Documentation

Documentação completa da API REST do Exo Piper - Sistema de Auditoria de Performance.

## 🔗 **Base URL**

- **Desenvolvimento**: `http://localhost:8000`
- **Produção**: `https://api.exopiper.com`

## 🔐 **Autenticação**

O Exo Piper usa autenticação JWT (JSON Web Tokens) para proteger os endpoints.

### **Obter Token**
```http
POST /api/v1/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Resposta:**
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "bearer",
  "expires_in": 3600
}
```

### **Usar Token**
```http
Authorization: Bearer ey<PERSON>hbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

## 👤 **Autenticação e Usuários**

### **Registrar Usuário**
```http
POST /api/v1/auth/register
Content-Type: application/json

{
  "username": "johndoe",
  "email": "<EMAIL>",
  "password": "securepassword123"
}
```

### **Login**
```http
POST /api/v1/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "securepassword123"
}
```

### **Obter Perfil do Usuário**
```http
GET /api/v1/auth/me
Authorization: Bearer {token}
```

### **Verificar Limites do Usuário**
```http
GET /api/v1/auth/limits
Authorization: Bearer {token}
```

**Resposta:**
```json
{
  "workloads": {
    "current": 5,
    "limit": 10,
    "exceeded": false
  },
  "monthly_jobs": {
    "current": 150,
    "limit": 200,
    "exceeded": false
  },
  "plan_type": "basic"
}
```

## 🔧 **Workloads**

### **Listar Workloads**
```http
GET /api/v1/workloads/
Authorization: Bearer {token}
```

### **Criar Workload**
```http
POST /api/v1/workloads/
Authorization: Bearer {token}
Content-Type: application/json

{
  "name": "Fibonacci Calculator",
  "description": "Recursive fibonacci implementation",
  "code": "def fibonacci(n):\n    if n <= 1:\n        return n\n    return fibonacci(n-1) + fibonacci(n-2)",
  "language": "python",
  "complexity_target": "O(2^n)"
}
```

### **Obter Workload**
```http
GET /api/v1/workloads/{workload_id}
Authorization: Bearer {token}
```

### **Atualizar Workload**
```http
PUT /api/v1/workloads/{workload_id}
Authorization: Bearer {token}
Content-Type: application/json

{
  "name": "Updated Fibonacci Calculator",
  "description": "Optimized fibonacci implementation"
}
```

### **Deletar Workload**
```http
DELETE /api/v1/workloads/{workload_id}
Authorization: Bearer {token}
```

## 🏃 **Benchmarks**

### **Executar Benchmark**
```http
POST /api/v1/benchmarks/run
Authorization: Bearer {token}
Content-Type: application/json

{
  "workload_id": 123,
  "input_sizes": [10, 20, 30, 40, 50],
  "iterations": 5,
  "timeout": 300
}
```

**Resposta:**
```json
{
  "job_id": "job_456",
  "status": "pending",
  "estimated_duration": 120
}
```

### **Verificar Status do Benchmark**
```http
GET /api/v1/benchmarks/status/{job_id}
Authorization: Bearer {token}
```

**Resposta:**
```json
{
  "job_id": "job_456",
  "status": "completed",
  "progress": 100,
  "results": {
    "complexity_analysis": {
      "lambda": 1.85,
      "p_value": 0.001,
      "complexity_class": "O(2^n)",
      "confidence": 0.95
    },
    "performance_metrics": [
      {
        "input_size": 10,
        "execution_time": 0.001,
        "memory_usage": 1024
      }
    ]
  }
}
```

### **Listar Jobs de Benchmark**
```http
GET /api/v1/benchmarks/jobs
Authorization: Bearer {token}
```

## 💳 **Pagamentos**

### **Obter Planos Disponíveis**
```http
GET /api/v1/payments/plans
Authorization: Bearer {token}
```

**Resposta:**
```json
{
  "current_plan": "free",
  "plans": [
    {
      "name": "basic",
      "display_name": "Basic",
      "price_usdt": "50.000000",
      "price_usd": "50.00",
      "available": true,
      "features": [
        "10 workloads ativos",
        "200 jobs por mês",
        "90 dias de retenção"
      ]
    }
  ],
  "payment_networks": [
    {
      "id": "bep20",
      "name": "Binance Smart Chain (BEP20)",
      "currency": "USDT",
      "fees": "Low (~$0.20)",
      "confirmation_time": "1-3 minutes"
    }
  ]
}
```

### **Criar Pagamento USDT**
```http
POST /api/v1/payments/create
Authorization: Bearer {token}
Content-Type: application/json

{
  "plan_type": "basic",
  "network": "bep20"
}
```

**Resposta:**
```json
{
  "success": true,
  "payment": {
    "payment_id": 789,
    "amount": "50.000000",
    "currency": "USDT",
    "network": "BEP20",
    "wallet_address": "******************************************",
    "contract_address": "******************************************",
    "expires_at": "2024-01-15T12:00:00Z",
    "qr_code": {
      "qr_code_png_base64": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...",
      "payment_url": "bnb:******************************************?amount=50&token=******************************************"
    },
    "instructions": [
      "1. Abra sua wallet (MetaMask, Trust Wallet, etc.)",
      "2. Conecte à rede Binance Smart Chain (BSC)",
      "3. Envie o valor exato em USDT para o endereço fornecido"
    ]
  }
}
```

### **Verificar Status do Pagamento**
```http
GET /api/v1/payments/status/{payment_id}
Authorization: Bearer {token}
```

### **Criar Swap TANOS (Bitcoin)**
```http
POST /api/v1/payments/tanos/create
Authorization: Bearer {token}
Content-Type: application/json

{
  "plan_type": "basic"
}
```

### **Histórico de Pagamentos**
```http
GET /api/v1/payments/history
Authorization: Bearer {token}
```

## 📊 **Relatórios e Analytics**

### **Obter Relatório de Performance**
```http
GET /api/v1/reports/performance/{workload_id}
Authorization: Bearer {token}
```

### **Comparar Workloads**
```http
POST /api/v1/reports/compare
Authorization: Bearer {token}
Content-Type: application/json

{
  "workload_ids": [123, 456, 789],
  "metric": "execution_time"
}
```

## 🔧 **Administração** (Apenas Admins)

### **Estatísticas de Uso**
```http
GET /api/v1/admin/usage/all
Authorization: Bearer {admin_token}
```

### **Analytics de Pagamentos**
```http
GET /api/v1/admin/analytics/payments
Authorization: Bearer {admin_token}
```

### **Dashboard de Analytics**
```http
GET /api/v1/admin/analytics/dashboard
Authorization: Bearer {admin_token}
```

### **Limpeza de Dados**
```http
POST /api/v1/admin/cleanup/all
Authorization: Bearer {admin_token}
```

## 📋 **Códigos de Status HTTP**

| Código | Significado |
|--------|-------------|
| 200 | OK - Requisição bem-sucedida |
| 201 | Created - Recurso criado com sucesso |
| 202 | Accepted - Requisição aceita para processamento |
| 204 | No Content - Operação bem-sucedida sem conteúdo |
| 400 | Bad Request - Dados inválidos |
| 401 | Unauthorized - Token inválido ou ausente |
| 403 | Forbidden - Sem permissão para o recurso |
| 404 | Not Found - Recurso não encontrado |
| 422 | Unprocessable Entity - Erro de validação |
| 429 | Too Many Requests - Rate limit excedido |
| 500 | Internal Server Error - Erro interno do servidor |

## 🔄 **Rate Limiting**

A API implementa rate limiting para prevenir abuso:

- **Usuários Free**: 100 requisições/hora
- **Usuários Basic**: 500 requisições/hora  
- **Usuários Pro**: 2000 requisições/hora
- **Usuários Enterprise**: 10000 requisições/hora

## 📝 **Exemplos de Uso**

### **Fluxo Completo: Criar e Executar Benchmark**

```bash
# 1. Login
curl -X POST http://localhost:8000/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123"}'

# 2. Criar workload
curl -X POST http://localhost:8000/api/v1/workloads/ \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Bubble Sort",
    "code": "def bubble_sort(arr):\n    n = len(arr)\n    for i in range(n):\n        for j in range(0, n-i-1):\n            if arr[j] > arr[j+1]:\n                arr[j], arr[j+1] = arr[j+1], arr[j]\n    return arr",
    "language": "python",
    "complexity_target": "O(n^2)"
  }'

# 3. Executar benchmark
curl -X POST http://localhost:8000/api/v1/benchmarks/run \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "workload_id": 123,
    "input_sizes": [100, 200, 500, 1000],
    "iterations": 3
  }'

# 4. Verificar status
curl -X GET http://localhost:8000/api/v1/benchmarks/status/job_456 \
  -H "Authorization: Bearer $TOKEN"
```

## 🔍 **Documentação Interativa**

A documentação interativa da API está disponível em:

- **Swagger UI**: `http://localhost:8000/docs`
- **ReDoc**: `http://localhost:8000/redoc`
- **OpenAPI Spec**: `http://localhost:8000/openapi.json`

## 🐛 **Tratamento de Erros**

Todos os erros seguem o formato padrão:

```json
{
  "detail": "Descrição do erro",
  "error_code": "WORKLOAD_NOT_FOUND",
  "timestamp": "2024-01-15T10:30:00Z"
}
```

## 🔒 **Segurança**

- **HTTPS**: Todas as comunicações em produção usam TLS 1.3
- **JWT**: Tokens com expiração configurável
- **CORS**: Configurado para domínios autorizados
- **Rate Limiting**: Proteção contra abuso
- **Validação**: Todos os inputs são validados
- **Sanitização**: Código executado em containers isolados

## 📞 **Suporte**

- **Email**: <EMAIL>
- **Documentação**: https://docs.exopiper.com
- **Status**: https://status.exopiper.com
- **GitHub**: https://github.com/exopiper/exopiper
