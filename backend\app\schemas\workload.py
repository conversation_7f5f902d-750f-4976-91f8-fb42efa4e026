"""
Workload schemas for API validation
"""

from pydantic import BaseModel, Field
from typing import Optional, Dict, Any
from datetime import datetime


class WorkloadBase(BaseModel):
    name: str = Field(..., min_length=1, max_length=100)
    description: Optional[str] = None
    workload_type: str = Field(..., regex="^(mergesort|3sat|mlmodel|vec2vec|custom)$")
    config: Dict[str, Any] = Field(..., description="Workload-specific configuration")


class WorkloadCreate(WorkloadBase):
    schedule_enabled: bool = False
    schedule_cron: Optional[str] = None


class WorkloadUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=100)
    description: Optional[str] = None
    config: Optional[Dict[str, Any]] = None
    is_active: Optional[bool] = None
    schedule_enabled: Optional[bool] = None
    schedule_cron: Optional[str] = None


class WorkloadResponse(WorkloadBase):
    id: int
    user_id: int
    is_active: bool
    schedule_enabled: bool
    schedule_cron: Optional[str]
    created_at: datetime
    updated_at: Optional[datetime]
    
    class Config:
        from_attributes = True


class WorkloadListResponse(BaseModel):
    workloads: list[WorkloadResponse]
    total: int
    page: int
    per_page: int
