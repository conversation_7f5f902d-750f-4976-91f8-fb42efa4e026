"""
Reports API endpoints
"""

from fastapi import API<PERSON>outer, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_
from typing import Optional
from datetime import datetime, timedelta

from ..core.database import get_db
from ..core.security import get_current_user
from ..models.user import User
from ..models.workload import Workload
from ..models.benchmark import Ben<PERSON><PERSON><PERSON><PERSON>, BenchmarkResult, JobStatus
from ..services.analysis_service import ComplexityAnalysisService

router = APIRouter()


@router.get("/performance-summary")
async def get_performance_summary(
    workload_id: Optional[int] = None,
    days: int = Query(30, ge=1, le=365),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get performance summary for user's workloads"""

    # Date range
    end_date = datetime.now(datetime.UTC)
    start_date = end_date - timedelta(days=days)

    # Base query for user's jobs
    base_query = select(BenchmarkJob).where(
        and_(
            BenchmarkJob.user_id == current_user.id,
            BenchmarkJob.status == JobStatus.COMPLETED,
            BenchmarkJob.completed_at >= start_date,
            BenchmarkJob.completed_at <= end_date
        )
    )

    # Filter by workload if specified
    if workload_id:
        base_query = base_query.where(BenchmarkJob.workload_id == workload_id)

    # Get jobs
    result = await db.execute(base_query.order_by(BenchmarkJob.completed_at.desc()))
    jobs = result.scalars().all()

    # Get workload info
    workloads_info = {}
    if workload_id:
        workload = await db.get(Workload, workload_id)
        if workload and workload.user_id == current_user.id:
            workloads_info[workload_id] = {
                "name": workload.name,
                "type": workload.workload_type,
                "description": workload.description
            }
    else:
        # Get all user workloads
        workloads_result = await db.execute(
            select(Workload).where(Workload.user_id == current_user.id)
        )
        for workload in workloads_result.scalars().all():
            workloads_info[workload.id] = {
                "name": workload.name,
                "type": workload.workload_type,
                "description": workload.description
            }

    # Calculate summary statistics
    summary = {
        "period_days": days,
        "total_jobs": len(jobs),
        "total_workloads": len(workloads_info),
        "workloads": workloads_info,
        "job_statistics": {
            "completed": len([j for j in jobs if j.status == JobStatus.COMPLETED]),
            "avg_duration_seconds": sum(j.duration_seconds or 0 for j in jobs) / len(jobs) if jobs else 0,
            "total_execution_time_hours": sum(j.duration_seconds or 0 for j in jobs) / 3600
        },
        "recent_jobs": [
            {
                "job_id": job.job_id,
                "workload_id": job.workload_id,
                "workload_name": workloads_info.get(job.workload_id, {}).get("name", "Unknown"),
                "completed_at": job.completed_at.isoformat() if job.completed_at else None,
                "duration_seconds": job.duration_seconds
            }
            for job in jobs[:10]  # Last 10 jobs
        ]
    }

    return summary


@router.get("/complexity-analysis/{workload_id}")
async def get_complexity_analysis(
    workload_id: int,
    job_limit: int = Query(5, ge=1, le=20),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get complexity analysis for a specific workload"""

    # Verify workload ownership
    workload = await db.get(Workload, workload_id)
    if not workload or workload.user_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Workload not found"
        )

    # Get recent completed jobs
    jobs_result = await db.execute(
        select(BenchmarkJob).where(
            and_(
                BenchmarkJob.workload_id == workload_id,
                BenchmarkJob.status == JobStatus.COMPLETED
            )
        ).order_by(BenchmarkJob.completed_at.desc()).limit(job_limit)
    )
    jobs = jobs_result.scalars().all()

    if not jobs:
        return {
            "workload_id": workload_id,
            "workload_name": workload.name,
            "status": "no_data",
            "message": "No completed benchmark jobs found"
        }

    # Get results for the most recent job
    latest_job = jobs[0]
    results_query = await db.execute(
        select(BenchmarkResult).where(BenchmarkResult.job_id == latest_job.id)
    )
    results = results_query.scalars().all()

    if not results:
        return {
            "workload_id": workload_id,
            "workload_name": workload.name,
            "status": "no_results",
            "message": "No benchmark results found for latest job"
        }

    # Perform complexity analysis
    analysis_service = ComplexityAnalysisService()
    analysis = analysis_service.analyze_complexity(results)

    # Add workload context
    analysis["workload_id"] = workload_id
    analysis["workload_name"] = workload.name
    analysis["workload_type"] = workload.workload_type
    analysis["latest_job_id"] = latest_job.job_id
    analysis["analysis_date"] = datetime.utcnow().isoformat()

    # Add raw data for charts
    analysis["raw_data"] = [
        {
            "input_size": result.input_size,
            "execution_time_ns": result.execution_time_ns,
            "clean_time_ns": result.clean_time_ns,
            "hardware_type": result.hardware_type,
            "memory_usage_mb": result.memory_usage_mb
        }
        for result in results
    ]

    # Historical comparison if we have multiple jobs
    if len(jobs) > 1:
        prev_job = jobs[1]
        prev_results_query = await db.execute(
            select(BenchmarkResult).where(BenchmarkResult.job_id == prev_job.id)
        )
        prev_results = prev_results_query.scalars().all()

        if prev_results:
            prev_analysis = analysis_service.analyze_complexity(prev_results)
            if prev_analysis.get("status") == "success":
                comparison = analysis_service.compare_analyses(analysis, prev_analysis)
                analysis["historical_comparison"] = comparison

    return analysis


@router.get("/alerts")
async def get_alerts(
    days: int = Query(7, ge=1, le=90),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get performance alerts for user"""

    # For now, we'll generate alerts based on recent analysis
    # In a full implementation, these would be stored in a dedicated alerts table

    end_date = datetime.utcnow()
    start_date = end_date - timedelta(days=days)

    # Get recent jobs with potential issues
    jobs_result = await db.execute(
        select(BenchmarkJob).where(
            and_(
                BenchmarkJob.user_id == current_user.id,
                BenchmarkJob.status == JobStatus.COMPLETED,
                BenchmarkJob.completed_at >= start_date
            )
        ).order_by(BenchmarkJob.completed_at.desc())
    )
    jobs = jobs_result.scalars().all()

    alerts = []
    analysis_service = ComplexityAnalysisService()

    # Group jobs by workload
    workload_jobs = {}
    for job in jobs:
        if job.workload_id not in workload_jobs:
            workload_jobs[job.workload_id] = []
        workload_jobs[job.workload_id].append(job)

    # Check each workload for regressions
    for workload_id, workload_job_list in workload_jobs.items():
        if len(workload_job_list) < 2:
            continue

        # Get workload info
        workload = await db.get(Workload, workload_id)
        if not workload:
            continue

        # Compare latest two jobs
        latest_job = workload_job_list[0]
        prev_job = workload_job_list[1]

        # Get results for both jobs
        latest_results = await db.execute(
            select(BenchmarkResult).where(BenchmarkResult.job_id == latest_job.id)
        )
        latest_data = latest_results.scalars().all()

        prev_results = await db.execute(
            select(BenchmarkResult).where(BenchmarkResult.job_id == prev_job.id)
        )
        prev_data = prev_results.scalars().all()

        if latest_data and prev_data:
            latest_analysis = analysis_service.analyze_complexity(latest_data)
            prev_analysis = analysis_service.analyze_complexity(prev_data)

            if (latest_analysis.get("status") == "success" and
                prev_analysis.get("status") == "success"):

                comparison = analysis_service.compare_analyses(latest_analysis, prev_analysis)

                if comparison.get("regression_detected"):
                    alerts.append({
                        "id": f"regression_{workload_id}_{latest_job.id}",
                        "type": "performance_regression",
                        "severity": "high" if comparison.get("delta_p", 0) > 0.1 else "medium",
                        "workload_id": workload_id,
                        "workload_name": workload.name,
                        "message": f"Performance regression detected in {workload.name}",
                        "details": {
                            "current_p": latest_analysis.get("p_exponent"),
                            "previous_p": prev_analysis.get("p_exponent"),
                            "delta_p": comparison.get("delta_p"),
                            "performance_change": comparison.get("performance_change")
                        },
                        "created_at": latest_job.completed_at.isoformat(),
                        "job_id": latest_job.job_id
                    })

        # Check for failed jobs
        failed_jobs = [j for j in workload_job_list if j.status == JobStatus.FAILED]
        if failed_jobs:
            alerts.append({
                "id": f"failed_jobs_{workload_id}",
                "type": "job_failures",
                "severity": "medium",
                "workload_id": workload_id,
                "workload_name": workload.name,
                "message": f"{len(failed_jobs)} failed jobs in {workload.name}",
                "details": {
                    "failed_count": len(failed_jobs),
                    "recent_failures": [
                        {
                            "job_id": job.job_id,
                            "error": job.error_message,
                            "failed_at": job.completed_at.isoformat() if job.completed_at else None
                        }
                        for job in failed_jobs[:3]  # Show last 3 failures
                    ]
                },
                "created_at": failed_jobs[0].completed_at.isoformat() if failed_jobs[0].completed_at else None
            })

    return {
        "alerts": alerts,
        "total_alerts": len(alerts),
        "period_days": days,
        "generated_at": datetime.utcnow().isoformat()
    }
