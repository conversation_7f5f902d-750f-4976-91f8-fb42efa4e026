"""
Benchmark job and result models
"""

from sqlalchemy import <PERSON>umn, Integer, String, Text, Boolean, DateTime, ForeignKey, JSON, Float, Enum as SQLEnum
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import enum

from ..core.database import Base


class JobStatus(enum.Enum):
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class BenchmarkJob(Base):
    __tablename__ = "benchmark_jobs"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    workload_id = Column(Integer, ForeignKey("workloads.id"), nullable=False)

    # Job identification
    job_id = Column(String, unique=True, index=True, nullable=False)  # UUID
    status = Column(SQLEnum(JobStatus), default=JobStatus.PENDING)

    # Execution details
    agent_info = Column(JSON, nullable=True)  # Hardware info from agent
    docker_image = Column(String, nullable=True)
    execution_log = Column(Text, nullable=True)
    error_message = Column(Text, nullable=True)

    # Storage paths
    log_file_path = Column(String, nullable=True)  # Path to logs in MinIO
    artifact_file_path = Column(String, nullable=True)  # Path to artifacts in MinIO

    # Timing
    started_at = Column(DateTime, nullable=True)
    completed_at = Column(DateTime, nullable=True)
    duration_seconds = Column(Float, nullable=True)

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    user = relationship("User", back_populates="benchmark_jobs")
    workload = relationship("Workload", back_populates="benchmark_jobs")
    results = relationship("BenchmarkResult", back_populates="job")

    def __repr__(self):
        return f"<BenchmarkJob(id={self.id}, job_id='{self.job_id}', status='{self.status.value}')>"


class BenchmarkResult(Base):
    __tablename__ = "benchmark_results"

    id = Column(Integer, primary_key=True, index=True)
    job_id = Column(Integer, ForeignKey("benchmark_jobs.id"), nullable=False)

    # Core metrics from Teorema da Relatividade da Complexidade
    input_size = Column(Integer, nullable=False)  # n
    execution_time_ns = Column(Integer, nullable=False)  # Raw execution time in nanoseconds
    overhead_time_ns = Column(Integer, nullable=True)  # Overhead time
    clean_time_ns = Column(Integer, nullable=False)  # execution_time - overhead

    # Hardware context
    hardware_type = Column(String, nullable=False)  # CPU, GPU, FPGA
    hardware_info = Column(JSON, nullable=True)  # Detailed hardware specs

    # Derived metrics
    lambda_value = Column(Float, nullable=True)  # λ (hardware constant)
    p_exponent = Column(Float, nullable=True)  # p (algorithmic exponent)

    # Additional metrics
    memory_usage_mb = Column(Float, nullable=True)
    cpu_usage_percent = Column(Float, nullable=True)
    gpu_usage_percent = Column(Float, nullable=True)

    # Metadata
    metadata = Column(JSON, nullable=True)  # Additional benchmark-specific data

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    job = relationship("BenchmarkJob", back_populates="results")

    def __repr__(self):
        return f"<BenchmarkResult(id={self.id}, n={self.input_size}, time={self.clean_time_ns}ns)>"
