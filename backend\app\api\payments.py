"""
Payment endpoints for USDT payments via TANOS
"""

from fastapi import APIRouter, Depends, HTTPException, status, BackgroundTasks
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Dict, Any, List
from pydantic import BaseModel

from ..core.database import get_db
from ..core.auth import get_current_user
from ..models.user import User, PlanType
from ..services.payment_service import payment_service
from ..services.tanos_service import tanos_service
from ..services.webhook_service import webhook_service

router = APIRouter()


class PaymentRequest(BaseModel):
    """Payment request model"""
    plan_type: str
    network: str = "bep20"  # Default to BEP20 (BSC)


class TANOSSwapRequest(BaseModel):
    """TANOS swap request model"""
    plan_type: str


class PaymentStatusResponse(BaseModel):
    """Payment status response model"""
    status: str
    transaction_hash: str = None
    confirmed_at: str = None
    message: str = None


@router.post("/create")
async def create_payment(
    request: PaymentRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    Create a new payment request for plan upgrade
    """

    # Validate plan type
    try:
        plan_type = PlanType(request.plan_type.lower())
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid plan type. Must be 'basic', 'pro', or 'enterprise'"
        )

    # Validate network
    if request.network not in ["bep20", "erc20"]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid network. Must be 'bep20' or 'erc20'"
        )

    # Check if user already has this plan or higher
    current_plan_priority = {
        PlanType.FREE: 0,
        PlanType.BASIC: 1,
        PlanType.PRO: 2,
        PlanType.ENTERPRISE: 3
    }

    if current_plan_priority.get(PlanType(current_user.plan_type), 0) >= current_plan_priority[plan_type]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="You already have this plan or a higher plan"
        )

    try:
        payment_request = await payment_service.create_payment_request(
            user_id=current_user.id,
            plan_type=plan_type,
            network=request.network
        )

        return {
            "success": True,
            "payment": payment_request
        }

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create payment request: {str(e)}"
        )


@router.get("/status/{payment_id}")
async def check_payment_status(
    payment_id: int,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> PaymentStatusResponse:
    """
    Check the status of a payment
    """

    try:
        status_info = await payment_service.check_payment_status(payment_id)

        return PaymentStatusResponse(
            status=status_info["status"],
            transaction_hash=status_info.get("transaction_hash"),
            confirmed_at=status_info.get("confirmed_at"),
            message=status_info.get("message")
        )

    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to check payment status: {str(e)}"
        )


@router.get("/history")
async def get_payment_history(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    Get payment history for the current user
    """

    try:
        payments = await payment_service.get_payment_history(current_user.id)

        return {
            "success": True,
            "payments": payments
        }

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get payment history: {str(e)}"
        )


@router.get("/plans")
async def get_available_plans(
    current_user: User = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    Get available plans and their prices
    """

    current_plan_priority = {
        PlanType.FREE: 0,
        PlanType.BASIC: 1,
        PlanType.PRO: 2,
        PlanType.ENTERPRISE: 3
    }

    user_priority = current_plan_priority.get(PlanType(current_user.plan_type), 0)

    plans = []
    for plan_type, price in payment_service.PLAN_PRICES.items():
        plan_priority = current_plan_priority[plan_type]

        plans.append({
            "name": plan_type.value,
            "display_name": plan_type.value.title(),
            "price_usdt": str(price),
            "price_usd": str(price),  # USDT is pegged to USD
            "available": plan_priority > user_priority,
            "current": plan_type.value == current_user.plan_type,
            "features": _get_plan_features(plan_type)
        })

    return {
        "success": True,
        "current_plan": current_user.plan_type,
        "plans": plans,
        "payment_networks": [
            {
                "id": "bep20",
                "name": "Binance Smart Chain (BEP20)",
                "currency": "USDT",
                "fees": "Low (~$0.20)",
                "confirmation_time": "1-3 minutes"
            },
            {
                "id": "erc20",
                "name": "Ethereum (ERC20)",
                "currency": "USDT",
                "fees": "High (~$5-20)",
                "confirmation_time": "5-15 minutes"
            },
            {
                "id": "bitcoin",
                "name": "Bitcoin (TANOS Atomic Swap)",
                "currency": "BTC",
                "fees": "Variable (~$1-10)",
                "confirmation_time": "10-60 minutes"
            }
        ]
    }


@router.post("/webhook/payment-confirmed")
async def payment_webhook(
    payload: Dict[str, Any],
    background_tasks: BackgroundTasks
) -> Dict[str, Any]:
    """
    Webhook endpoint for payment confirmations
    This can be called by external services or blockchain monitors
    """

    # Add background task to process webhook
    background_tasks.add_task(_process_payment_webhook, payload)

    return {"success": True, "message": "Webhook received"}


async def _process_payment_webhook(payload: Dict[str, Any]):
    """Process payment webhook in background"""
    try:
        # Extract payment information from webhook
        payment_id = payload.get("payment_id")
        transaction_hash = payload.get("transaction_hash")

        if payment_id:
            # Check and update payment status
            await payment_service.check_payment_status(payment_id)

    except Exception as e:
        # Log error but don't fail the webhook
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"Error processing payment webhook: {e}")


def _get_plan_features(plan_type: PlanType) -> List[str]:
    """Get features for a plan type"""

    features = {
        PlanType.BASIC: [
            "10 workloads ativos",
            "200 jobs por mês",
            "90 dias de retenção",
            "Suporte por email",
            "Relatórios básicos"
        ],
        PlanType.PRO: [
            "50 workloads ativos",
            "1000 jobs por mês",
            "365 dias de retenção",
            "Webhooks",
            "Relatórios avançados",
            "Suporte prioritário"
        ],
        PlanType.ENTERPRISE: [
            "Workloads ilimitados",
            "Jobs ilimitados",
            "Retenção ilimitada",
            "SLA 24x7",
            "SSO + VPC",
            "White-label",
            "Suporte dedicado",
            "Integrações customizadas"
        ]
    }

    return features.get(plan_type, [])


# TANOS Atomic Swap Endpoints

@router.post("/tanos/create")
async def create_tanos_swap(
    request: TANOSSwapRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    Create a new TANOS atomic swap for plan upgrade
    """

    # Validate plan type
    try:
        plan_type = PlanType(request.plan_type.lower())
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid plan type. Must be 'basic', 'pro', or 'enterprise'"
        )

    # Check if user already has this plan or higher
    current_plan_priority = {
        PlanType.FREE: 0,
        PlanType.BASIC: 1,
        PlanType.PRO: 2,
        PlanType.ENTERPRISE: 3
    }

    if current_plan_priority.get(PlanType(current_user.plan_type), 0) >= current_plan_priority[plan_type]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="You already have this plan or a higher plan"
        )

    try:
        swap_data = await tanos_service.create_tanos_swap(
            user_id=current_user.id,
            plan_type=plan_type
        )

        return {
            "success": True,
            "swap": swap_data
        }

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create TANOS swap: {str(e)}"
        )


@router.get("/tanos/status/{swap_id}")
async def check_tanos_swap_status(
    swap_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    Check the status of a TANOS atomic swap
    """

    try:
        status_info = await tanos_service.check_tanos_swap_status(swap_id)

        return {
            "success": True,
            "status": status_info
        }

    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to check TANOS swap status: {str(e)}"
        )


@router.get("/webhooks/{payment_id}")
async def get_payment_webhooks(
    payment_id: int,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    Get webhook history for a payment
    """

    try:
        webhooks = await webhook_service.get_webhook_history(payment_id)

        return {
            "success": True,
            "webhooks": webhooks
        }

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get webhook history: {str(e)}"
        )
