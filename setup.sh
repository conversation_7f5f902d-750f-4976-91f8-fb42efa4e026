#!/bin/bash

# Exo Piper Setup Script
# This script sets up the development environment

set -e

echo "🚀 Setting up Exo Piper Development Environment"
echo "================================================"

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed. Please install Docker first."
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js first."
    exit 1
fi

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 is not installed. Please install Python 3 first."
    exit 1
fi

echo "✅ Prerequisites check passed"

# Create .env file if it doesn't exist
if [ ! -f .env ]; then
    echo "📝 Creating .env file from template..."
    cp .env.example .env
    echo "⚠️  Please edit .env file with your configuration"
else
    echo "✅ .env file already exists"
fi

# Setup backend
echo "🔧 Setting up backend..."
cd backend

# Create virtual environment if it doesn't exist
if [ ! -d "venv" ]; then
    echo "📦 Creating Python virtual environment..."
    python3 -m venv venv
fi

# Activate virtual environment
source venv/bin/activate

# Install Python dependencies
echo "📦 Installing Python dependencies..."
pip install -r requirements.txt

cd ..

# Setup frontend
echo "🌐 Setting up frontend..."
cd frontend

# Install Node.js dependencies
echo "📦 Installing Node.js dependencies..."
npm install

cd ..

# Setup CLI
echo "💻 Setting up CLI..."
cd cli

# Install CLI in development mode
echo "📦 Installing CLI in development mode..."
pip install -e .

cd ..

echo "🐳 Starting Docker services..."
docker-compose up -d postgres redis minio

# Wait for services to be ready
echo "⏳ Waiting for services to be ready..."
sleep 10

# Run database migrations
echo "🗄️  Setting up database..."
cd backend
source venv/bin/activate
# Note: In a real setup, you'd run Alembic migrations here
# alembic upgrade head
cd ..

echo "✅ Setup completed successfully!"
echo ""
echo "🚀 To start the development environment:"
echo "   1. Start all services: docker-compose up -d"
echo "   2. Start backend: cd backend && source venv/bin/activate && uvicorn main:app --reload"
echo "   3. Start frontend: cd frontend && npm run dev"
echo "   4. Start Celery worker: cd backend && source venv/bin/activate && celery -A app.core.celery_app worker --loglevel=info"
echo ""
echo "🌐 Access points:"
echo "   - Frontend: http://localhost:3000"
echo "   - Backend API: http://localhost:8000"
echo "   - API Docs: http://localhost:8000/docs"
echo "   - MinIO Console: http://localhost:9001"
echo ""
echo "📚 Next steps:"
echo "   1. Edit .env file with your configuration"
echo "   2. Test CLI: perf-audit --help"
echo "   3. Create a user account via API or frontend"
echo "   4. Start benchmarking!"
