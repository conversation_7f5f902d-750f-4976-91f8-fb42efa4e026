"""
Configuration management for CLI
"""

import os
import json
from pathlib import Path
from dataclasses import dataclass
from typing import Optional


@dataclass
class Config:
    api_url: str
    api_key: str


def get_config_path() -> Path:
    """Get path to config file"""
    home = Path.home()
    config_dir = home / ".perf-audit"
    config_dir.mkdir(exist_ok=True)
    return config_dir / "config.json"


def load_config() -> Optional[Config]:
    """Load configuration from file"""
    config_path = get_config_path()
    
    if not config_path.exists():
        return None
    
    try:
        with open(config_path, 'r') as f:
            data = json.load(f)
        
        return Config(
            api_url=data['api_url'],
            api_key=data['api_key']
        )
    except (json.JSONDecodeError, KeyError):
        return None


def save_config(config: Config) -> None:
    """Save configuration to file"""
    config_path = get_config_path()
    
    data = {
        'api_url': config.api_url,
        'api_key': config.api_key
    }
    
    with open(config_path, 'w') as f:
        json.dump(data, f, indent=2)


def clear_config() -> None:
    """Clear saved configuration"""
    config_path = get_config_path()
    if config_path.exists():
        config_path.unlink()
