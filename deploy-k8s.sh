#!/bin/bash

# Deploy Exo Piper to Kubernetes
# Supports multiple environments and cloud providers

set -e

echo "☸️ Exo Piper Kubernetes Deployment"
echo "=================================="

# Colors
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Configuration
ENVIRONMENT=${ENVIRONMENT:-"production"}
NAMESPACE=${NAMESPACE:-"exopiper"}
DOMAIN=${DOMAIN:-"exopiper.com"}
REGISTRY=${REGISTRY:-"ghcr.io/exopiper"}

# Check dependencies
check_dependencies() {
    log_info "Checking dependencies..."
    
    # Check kubectl
    if ! command -v kubectl &> /dev/null; then
        log_error "kubectl is not installed"
        exit 1
    fi
    
    # Check helm (optional)
    if ! command -v helm &> /dev/null; then
        log_warning "Helm is not installed (optional)"
    fi
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed"
        exit 1
    fi
    
    # Check cluster connection
    if ! kubectl cluster-info &> /dev/null; then
        log_error "Cannot connect to Kubernetes cluster"
        exit 1
    fi
    
    log_success "Dependencies check completed"
}

# Build and push Docker images
build_and_push_images() {
    log_info "Building and pushing Docker images..."
    
    # Build backend image
    log_info "Building backend image..."
    docker build -t ${REGISTRY}/backend:latest -f backend/Dockerfile.production backend/
    docker push ${REGISTRY}/backend:latest
    
    # Build frontend image
    log_info "Building frontend image..."
    docker build -t ${REGISTRY}/frontend:latest -f frontend/Dockerfile.production frontend/
    docker push ${REGISTRY}/frontend:latest
    
    # Build agent image
    log_info "Building agent image..."
    docker build -t ${REGISTRY}/agent:latest agent/
    docker push ${REGISTRY}/agent:latest
    
    log_success "Docker images built and pushed"
}

# Create namespace and basic resources
setup_namespace() {
    log_info "Setting up namespace and basic resources..."
    
    # Apply namespace
    kubectl apply -f k8s/namespace.yaml
    
    # Wait for namespace to be ready
    kubectl wait --for=condition=Ready namespace/${NAMESPACE} --timeout=60s
    
    log_success "Namespace setup completed"
}

# Deploy secrets and config maps
deploy_configs() {
    log_info "Deploying configurations and secrets..."
    
    # Apply config maps
    kubectl apply -f k8s/configmap.yaml
    
    # Apply secrets (make sure to update with real values)
    kubectl apply -f k8s/secrets.yaml
    
    log_success "Configurations deployed"
}

# Deploy database and storage
deploy_storage() {
    log_info "Deploying storage components..."
    
    # Deploy PostgreSQL
    kubectl apply -f k8s/postgres.yaml
    
    # Deploy Redis
    kubectl apply -f k8s/redis.yaml
    
    # Wait for storage to be ready
    log_info "Waiting for storage components to be ready..."
    kubectl wait --for=condition=Ready pod -l app=postgres -n ${NAMESPACE} --timeout=300s
    kubectl wait --for=condition=Ready pod -l app=redis -n ${NAMESPACE} --timeout=300s
    
    log_success "Storage components deployed"
}

# Deploy application components
deploy_applications() {
    log_info "Deploying application components..."
    
    # Deploy backend
    kubectl apply -f k8s/backend.yaml
    
    # Deploy frontend
    kubectl apply -f k8s/frontend.yaml
    
    # Deploy Celery workers
    kubectl apply -f k8s/celery.yaml
    
    # Wait for applications to be ready
    log_info "Waiting for applications to be ready..."
    kubectl wait --for=condition=Ready pod -l app=backend -n ${NAMESPACE} --timeout=300s
    kubectl wait --for=condition=Ready pod -l app=frontend -n ${NAMESPACE} --timeout=300s
    kubectl wait --for=condition=Ready pod -l app=celery-worker -n ${NAMESPACE} --timeout=300s
    
    log_success "Applications deployed"
}

# Deploy ingress and networking
deploy_networking() {
    log_info "Deploying networking components..."
    
    # Check if cert-manager is installed
    if ! kubectl get crd certificates.cert-manager.io &> /dev/null; then
        log_warning "cert-manager not found, installing..."
        kubectl apply -f https://github.com/cert-manager/cert-manager/releases/download/v1.13.0/cert-manager.yaml
        kubectl wait --for=condition=Ready pod -l app=cert-manager -n cert-manager --timeout=300s
    fi
    
    # Check if nginx-ingress is installed
    if ! kubectl get pods -n ingress-nginx &> /dev/null; then
        log_warning "nginx-ingress not found, installing..."
        kubectl apply -f https://raw.githubusercontent.com/kubernetes/ingress-nginx/controller-v1.8.1/deploy/static/provider/cloud/deploy.yaml
        kubectl wait --for=condition=Ready pod -l app.kubernetes.io/component=controller -n ingress-nginx --timeout=300s
    fi
    
    # Deploy ingress
    kubectl apply -f k8s/ingress.yaml
    
    log_success "Networking deployed"
}

# Run database migrations
run_migrations() {
    log_info "Running database migrations..."
    
    # Get a backend pod
    BACKEND_POD=$(kubectl get pods -n ${NAMESPACE} -l app=backend -o jsonpath='{.items[0].metadata.name}')
    
    if [ -n "$BACKEND_POD" ]; then
        # Run Alembic migrations
        kubectl exec -n ${NAMESPACE} $BACKEND_POD -- alembic upgrade head
        log_success "Database migrations completed"
    else
        log_error "No backend pod found for migrations"
        exit 1
    fi
}

# Verify deployment
verify_deployment() {
    log_info "Verifying deployment..."
    
    # Check all pods are running
    log_info "Checking pod status..."
    kubectl get pods -n ${NAMESPACE}
    
    # Check services
    log_info "Checking services..."
    kubectl get services -n ${NAMESPACE}
    
    # Check ingress
    log_info "Checking ingress..."
    kubectl get ingress -n ${NAMESPACE}
    
    # Test health endpoints
    log_info "Testing health endpoints..."
    
    # Port forward to test locally
    kubectl port-forward -n ${NAMESPACE} service/backend-service 8080:8000 &
    PORT_FORWARD_PID=$!
    
    sleep 5
    
    if curl -f http://localhost:8080/health &> /dev/null; then
        log_success "Backend health check passed"
    else
        log_error "Backend health check failed"
    fi
    
    # Kill port forward
    kill $PORT_FORWARD_PID 2>/dev/null || true
    
    log_success "Deployment verification completed"
}

# Setup monitoring (optional)
setup_monitoring() {
    log_info "Setting up monitoring..."
    
    # Install Prometheus Operator if not exists
    if ! kubectl get crd prometheuses.monitoring.coreos.com &> /dev/null; then
        log_info "Installing Prometheus Operator..."
        kubectl apply -f https://raw.githubusercontent.com/prometheus-operator/prometheus-operator/main/bundle.yaml
    fi
    
    # Create monitoring namespace
    kubectl create namespace monitoring --dry-run=client -o yaml | kubectl apply -f -
    
    # Deploy Prometheus
    cat <<EOF | kubectl apply -f -
apiVersion: monitoring.coreos.com/v1
kind: Prometheus
metadata:
  name: exopiper-prometheus
  namespace: monitoring
spec:
  serviceAccountName: prometheus
  serviceMonitorSelector:
    matchLabels:
      app: exopiper
  resources:
    requests:
      memory: 400Mi
  retention: 30d
EOF
    
    # Deploy ServiceMonitor for backend
    cat <<EOF | kubectl apply -f -
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: exopiper-backend
  namespace: ${NAMESPACE}
  labels:
    app: exopiper
spec:
  selector:
    matchLabels:
      app: backend
  endpoints:
  - port: http
    path: /metrics
EOF
    
    log_success "Monitoring setup completed"
}

# Cleanup function
cleanup() {
    log_info "Cleaning up resources..."
    
    # Delete all resources in namespace
    kubectl delete all --all -n ${NAMESPACE}
    
    # Delete PVCs
    kubectl delete pvc --all -n ${NAMESPACE}
    
    # Delete namespace
    kubectl delete namespace ${NAMESPACE}
    
    log_success "Cleanup completed"
}

# Rollback function
rollback() {
    local REVISION=${1:-1}
    log_info "Rolling back to revision $REVISION..."
    
    # Rollback deployments
    kubectl rollout undo deployment/backend -n ${NAMESPACE} --to-revision=$REVISION
    kubectl rollout undo deployment/frontend -n ${NAMESPACE} --to-revision=$REVISION
    kubectl rollout undo deployment/celery-worker -n ${NAMESPACE} --to-revision=$REVISION
    
    # Wait for rollback to complete
    kubectl rollout status deployment/backend -n ${NAMESPACE}
    kubectl rollout status deployment/frontend -n ${NAMESPACE}
    kubectl rollout status deployment/celery-worker -n ${NAMESPACE}
    
    log_success "Rollback completed"
}

# Scale function
scale() {
    local COMPONENT=$1
    local REPLICAS=$2
    
    if [ -z "$COMPONENT" ] || [ -z "$REPLICAS" ]; then
        log_error "Usage: $0 scale <component> <replicas>"
        exit 1
    fi
    
    log_info "Scaling $COMPONENT to $REPLICAS replicas..."
    kubectl scale deployment/$COMPONENT -n ${NAMESPACE} --replicas=$REPLICAS
    kubectl rollout status deployment/$COMPONENT -n ${NAMESPACE}
    log_success "Scaling completed"
}

# Main deployment function
main() {
    log_info "Starting Kubernetes deployment for environment: $ENVIRONMENT"
    
    check_dependencies
    
    if [ "$1" != "--skip-build" ]; then
        build_and_push_images
    fi
    
    setup_namespace
    deploy_configs
    deploy_storage
    deploy_applications
    deploy_networking
    run_migrations
    verify_deployment
    
    if [ "$1" = "--monitoring" ]; then
        setup_monitoring
    fi
    
    log_success "Kubernetes deployment completed successfully!"
    
    echo
    echo "🎉 Deployment Summary:"
    echo "✅ Namespace: $NAMESPACE"
    echo "✅ Environment: $ENVIRONMENT"
    echo "✅ Domain: $DOMAIN"
    echo
    echo "🌐 URLs:"
    echo "Main site: https://$DOMAIN"
    echo "API: https://api.$DOMAIN"
    echo
    echo "📊 Useful commands:"
    echo "kubectl get pods -n $NAMESPACE"
    echo "kubectl logs -f deployment/backend -n $NAMESPACE"
    echo "kubectl port-forward service/backend-service 8080:8000 -n $NAMESPACE"
}

# Handle command line arguments
case "$1" in
    "deploy")
        main "${@:2}"
        ;;
    "cleanup")
        cleanup
        ;;
    "rollback")
        rollback "$2"
        ;;
    "scale")
        scale "$2" "$3"
        ;;
    "verify")
        verify_deployment
        ;;
    "monitoring")
        setup_monitoring
        ;;
    *)
        main "$@"
        ;;
esac
