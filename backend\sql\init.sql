-- Exo Piper Database Initialization
-- This script creates the initial database structure

-- Create extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create enum types
DO $$ BEGIN
    CREATE TYPE plan_type AS ENUM ('free', 'pro', 'team');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE job_status AS ENUM ('pending', 'running', 'completed', 'failed', 'cancelled');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Create indexes for better performance
-- These will be created automatically by SQLAlchemy, but we can add custom ones here

-- Example: Index for faster API key lookups
-- CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_api_key ON users(api_key) WHERE api_key IS NOT NULL;

-- Example: Index for benchmark results analysis
-- CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_benchmark_results_analysis ON benchmark_results(job_id, input_size, hardware_type);

-- Insert default data if needed
-- This could include default workload templates, etc.

COMMENT ON DATABASE exoPiper IS 'Exo Piper - Performance Auditor SaaS Database';
