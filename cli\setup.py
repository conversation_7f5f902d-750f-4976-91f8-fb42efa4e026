"""
Setup script for perf-audit CLI
"""

from setuptools import setup, find_packages

with open("README.md", "r", encoding="utf-8") as fh:
    long_description = fh.read()

setup(
    name="perf-audit",
    version="1.0.0",
    author="Exo Piper Team",
    author_email="<EMAIL>",
    description="CLI for Exo Piper Performance Auditor SaaS",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/exoPiper/cli",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
    ],
    python_requires=">=3.8",
    install_requires=[
        "click>=8.0.0",
        "requests>=2.28.0",
        "pyyaml>=6.0",
        "rich>=13.0.0",
        "docker>=6.0.0",
        "tabulate>=0.9.0",
    ],
    entry_points={
        "console_scripts": [
            "perf-audit=perf_audit.cli:main",
        ],
    },
    include_package_data=True,
    package_data={
        "perf_audit": ["templates/*.yaml"],
    },
)
