#!/bin/bash

# Exo Piper Production Deployment Script
# This script deploys the complete Exo Piper system to production

set -e  # Exit on any error

echo "🚀 Exo Piper Production Deployment"
echo "=================================="

# Configuration
DOMAIN=${DOMAIN:-"exopiper.com"}
EMAIL=${EMAIL:-"<EMAIL>"}
ENVIRONMENT="production"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as root
if [[ $EUID -eq 0 ]]; then
   log_error "This script should not be run as root for security reasons"
   exit 1
fi

# Check required tools
check_dependencies() {
    log_info "Checking dependencies..."
    
    local deps=("docker" "docker-compose" "git" "openssl")
    for dep in "${deps[@]}"; do
        if ! command -v $dep &> /dev/null; then
            log_error "$dep is not installed"
            exit 1
        fi
    done
    
    log_success "All dependencies are installed"
}

# Generate secure secrets
generate_secrets() {
    log_info "Generating secure secrets..."
    
    # Generate random secrets
    export SECRET_KEY=$(openssl rand -hex 32)
    export JWT_SECRET=$(openssl rand -hex 32)
    export WEBHOOK_SECRET=$(openssl rand -hex 32)
    export POSTGRES_PASSWORD=$(openssl rand -hex 16)
    export MINIO_SECRET_KEY=$(openssl rand -hex 16)
    
    log_success "Secrets generated"
}

# Create production environment file
create_env_file() {
    log_info "Creating production environment file..."
    
    cat > .env.production << EOF
# Exo Piper Production Environment
ENVIRONMENT=production
DOMAIN=$DOMAIN

# Security
SECRET_KEY=$SECRET_KEY
JWT_SECRET=$JWT_SECRET
WEBHOOK_SECRET=$WEBHOOK_SECRET

# Database
DATABASE_URL=******************************************************/exoPiper
POSTGRES_PASSWORD=$POSTGRES_PASSWORD

# Redis
REDIS_URL=redis://redis:6379/0

# Celery
CELERY_BROKER_URL=redis://redis:6379/0
CELERY_RESULT_BACKEND=redis://redis:6379/0

# MinIO
MINIO_ENDPOINT=minio:9000
MINIO_ACCESS_KEY=admin
MINIO_SECRET_KEY=$MINIO_SECRET_KEY
BUCKET_NAME=exopiper-artifacts

# Payment APIs (replace with your actual keys)
BSCSCAN_API_KEY=your_bscscan_api_key_here
ETHERSCAN_API_KEY=your_etherscan_api_key_here

# Email (configure with your SMTP provider)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_app_password

# Slack (optional)
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK

# SSL
LETSENCRYPT_EMAIL=$EMAIL

# Monitoring
SENTRY_DSN=your_sentry_dsn_here
EOF

    log_success "Environment file created"
}

# Create production docker-compose file
create_production_compose() {
    log_info "Creating production docker-compose file..."
    
    cat > docker-compose.production.yml << 'EOF'
version: '3.8'

services:
  # Nginx Reverse Proxy with SSL
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
      - certbot-etc:/etc/letsencrypt
      - certbot-var:/var/lib/letsencrypt
    depends_on:
      - backend
      - frontend
    restart: unless-stopped

  # Certbot for SSL certificates
  certbot:
    image: certbot/certbot
    volumes:
      - certbot-etc:/etc/letsencrypt
      - certbot-var:/var/lib/letsencrypt
      - ./nginx/ssl:/var/www/html
    command: certonly --webroot --webroot-path=/var/www/html --email ${LETSENCRYPT_EMAIL} --agree-tos --no-eff-email -d ${DOMAIN} -d api.${DOMAIN}

  # PostgreSQL Database
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: exoPiper
      POSTGRES_USER: exoPiper
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/sql:/docker-entrypoint-initdb.d
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U exoPiper"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis Cache
  redis:
    image: redis:7-alpine
    volumes:
      - redis_data:/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # MinIO Object Storage
  minio:
    image: minio/minio:latest
    environment:
      MINIO_ROOT_USER: admin
      MINIO_ROOT_PASSWORD: ${MINIO_SECRET_KEY}
    volumes:
      - minio_data:/data
    command: server /data --console-address ":9001"
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 10s
      retries: 3

  # FastAPI Backend
  backend:
    build: 
      context: ./backend
      dockerfile: Dockerfile.production
    environment:
      DATABASE_URL: ${DATABASE_URL}
      REDIS_URL: ${REDIS_URL}
      SECRET_KEY: ${SECRET_KEY}
      JWT_SECRET: ${JWT_SECRET}
      ENVIRONMENT: production
      MINIO_ENDPOINT: ${MINIO_ENDPOINT}
      MINIO_ACCESS_KEY: ${MINIO_ACCESS_KEY}
      MINIO_SECRET_KEY: ${MINIO_SECRET_KEY}
      BUCKET_NAME: ${BUCKET_NAME}
      BSCSCAN_API_KEY: ${BSCSCAN_API_KEY}
      ETHERSCAN_API_KEY: ${ETHERSCAN_API_KEY}
      SMTP_HOST: ${SMTP_HOST}
      SMTP_PORT: ${SMTP_PORT}
      SMTP_USER: ${SMTP_USER}
      SMTP_PASSWORD: ${SMTP_PASSWORD}
      WEBHOOK_SECRET: ${WEBHOOK_SECRET}
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      minio:
        condition: service_healthy
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
    restart: unless-stopped

  # Next.js Frontend
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.production
    environment:
      NODE_ENV: production
      NEXT_PUBLIC_API_URL: https://api.${DOMAIN}
    depends_on:
      - backend
    restart: unless-stopped

  # Celery Worker
  celery:
    build:
      context: ./backend
      dockerfile: Dockerfile.production
    environment:
      DATABASE_URL: ${DATABASE_URL}
      REDIS_URL: ${REDIS_URL}
      CELERY_BROKER_URL: ${CELERY_BROKER_URL}
      CELERY_RESULT_BACKEND: ${CELERY_RESULT_BACKEND}
      SECRET_KEY: ${SECRET_KEY}
      ENVIRONMENT: production
      MINIO_ENDPOINT: ${MINIO_ENDPOINT}
      MINIO_ACCESS_KEY: ${MINIO_ACCESS_KEY}
      MINIO_SECRET_KEY: ${MINIO_SECRET_KEY}
      BUCKET_NAME: ${BUCKET_NAME}
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
    command: celery -A app.core.celery_app worker --loglevel=info --queues=benchmarks,analysis,cleanup,notifications
    restart: unless-stopped

  # Celery Beat Scheduler
  celery-beat:
    build:
      context: ./backend
      dockerfile: Dockerfile.production
    environment:
      DATABASE_URL: ${DATABASE_URL}
      REDIS_URL: ${REDIS_URL}
      CELERY_BROKER_URL: ${CELERY_BROKER_URL}
      CELERY_RESULT_BACKEND: ${CELERY_RESULT_BACKEND}
      SECRET_KEY: ${SECRET_KEY}
      ENVIRONMENT: production
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    command: celery -A app.core.celery_app beat --loglevel=info
    restart: unless-stopped

  # Celery Payment Worker
  celery-payments:
    build:
      context: ./backend
      dockerfile: Dockerfile.production
    environment:
      DATABASE_URL: ${DATABASE_URL}
      REDIS_URL: ${REDIS_URL}
      CELERY_BROKER_URL: ${CELERY_BROKER_URL}
      CELERY_RESULT_BACKEND: ${CELERY_RESULT_BACKEND}
      SECRET_KEY: ${SECRET_KEY}
      ENVIRONMENT: production
      BSCSCAN_API_KEY: ${BSCSCAN_API_KEY}
      ETHERSCAN_API_KEY: ${ETHERSCAN_API_KEY}
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    command: celery -A app.core.celery_app worker --loglevel=info --queues=payments --concurrency=2
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
  minio_data:
  certbot-etc:
  certbot-var:

networks:
  default:
    driver: bridge
EOF

    log_success "Production docker-compose file created"
}

# Create Nginx configuration
create_nginx_config() {
    log_info "Creating Nginx configuration..."
    
    mkdir -p nginx
    
    cat > nginx/nginx.conf << EOF
events {
    worker_connections 1024;
}

http {
    upstream backend {
        server backend:8000;
    }
    
    upstream frontend {
        server frontend:3000;
    }

    # Redirect HTTP to HTTPS
    server {
        listen 80;
        server_name $DOMAIN api.$DOMAIN;
        
        location /.well-known/acme-challenge/ {
            root /var/www/html;
        }
        
        location / {
            return 301 https://\$server_name\$request_uri;
        }
    }

    # Main frontend
    server {
        listen 443 ssl http2;
        server_name $DOMAIN;

        ssl_certificate /etc/letsencrypt/live/$DOMAIN/fullchain.pem;
        ssl_certificate_key /etc/letsencrypt/live/$DOMAIN/privkey.pem;
        
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
        ssl_prefer_server_ciphers off;

        location / {
            proxy_pass http://frontend;
            proxy_set_header Host \$host;
            proxy_set_header X-Real-IP \$remote_addr;
            proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto \$scheme;
        }
    }

    # API backend
    server {
        listen 443 ssl http2;
        server_name api.$DOMAIN;

        ssl_certificate /etc/letsencrypt/live/$DOMAIN/fullchain.pem;
        ssl_certificate_key /etc/letsencrypt/live/$DOMAIN/privkey.pem;
        
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
        ssl_prefer_server_ciphers off;

        client_max_body_size 100M;

        location / {
            proxy_pass http://backend;
            proxy_set_header Host \$host;
            proxy_set_header X-Real-IP \$remote_addr;
            proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto \$scheme;
            proxy_read_timeout 300s;
            proxy_connect_timeout 75s;
        }
    }
}
EOF

    log_success "Nginx configuration created"
}

# Create production Dockerfiles
create_production_dockerfiles() {
    log_info "Creating production Dockerfiles..."
    
    # Backend production Dockerfile
    cat > backend/Dockerfile.production << 'EOF'
FROM python:3.11-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Install Docker CLI for agent execution
RUN curl -fsSL https://get.docker.com | sh

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Create non-root user
RUN useradd -m -u 1000 appuser && chown -R appuser:appuser /app
USER appuser

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000", "--workers", "4"]
EOF

    # Frontend production Dockerfile
    cat > frontend/Dockerfile.production << 'EOF'
FROM node:18-alpine AS builder

WORKDIR /app

# Copy package files
COPY package*.json ./
RUN npm ci --only=production

# Copy source code
COPY . .

# Build the application
RUN npm run build

# Production image
FROM node:18-alpine AS runner

WORKDIR /app

# Create non-root user
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

# Copy built application
COPY --from=builder --chown=nextjs:nodejs /app/.next ./.next
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/package.json ./package.json

USER nextjs

EXPOSE 3000

ENV NODE_ENV production
ENV PORT 3000

CMD ["npm", "start"]
EOF

    log_success "Production Dockerfiles created"
}

# Deploy the application
deploy_application() {
    log_info "Deploying application..."
    
    # Build and start services
    docker-compose -f docker-compose.production.yml --env-file .env.production up -d --build
    
    log_success "Application deployed"
}

# Setup SSL certificates
setup_ssl() {
    log_info "Setting up SSL certificates..."
    
    # Initial certificate request
    docker-compose -f docker-compose.production.yml --env-file .env.production run --rm certbot
    
    # Reload Nginx
    docker-compose -f docker-compose.production.yml --env-file .env.production restart nginx
    
    log_success "SSL certificates configured"
}

# Setup monitoring
setup_monitoring() {
    log_info "Setting up monitoring..."
    
    # Create monitoring script
    cat > monitor.sh << 'EOF'
#!/bin/bash
# Simple monitoring script for Exo Piper

echo "=== Exo Piper System Status ==="
echo "Date: $(date)"
echo

echo "=== Docker Services ==="
docker-compose -f docker-compose.production.yml ps

echo
echo "=== System Resources ==="
echo "Memory Usage:"
free -h

echo
echo "Disk Usage:"
df -h

echo
echo "=== Service Health ==="
curl -s http://localhost/health || echo "Frontend: DOWN"
curl -s http://localhost:8000/health || echo "Backend: DOWN"

echo
echo "=== Recent Logs ==="
docker-compose -f docker-compose.production.yml logs --tail=10 backend
EOF

    chmod +x monitor.sh
    
    log_success "Monitoring setup complete"
}

# Main deployment function
main() {
    log_info "Starting Exo Piper production deployment..."
    
    check_dependencies
    generate_secrets
    create_env_file
    create_production_compose
    create_nginx_config
    create_production_dockerfiles
    deploy_application
    
    log_success "Deployment completed successfully!"
    
    echo
    log_info "Next steps:"
    echo "1. Configure your DNS to point $DOMAIN and api.$DOMAIN to this server"
    echo "2. Run: ./deploy-production.sh ssl  # to setup SSL certificates"
    echo "3. Update API keys in .env.production file"
    echo "4. Monitor with: ./monitor.sh"
    echo
    log_info "Your Exo Piper instance will be available at:"
    echo "  Frontend: https://$DOMAIN"
    echo "  API: https://api.$DOMAIN"
    echo "  Wallet: ******************************************"
}

# Handle SSL setup separately
if [[ "$1" == "ssl" ]]; then
    setup_ssl
    exit 0
fi

# Handle monitoring setup
if [[ "$1" == "monitor" ]]; then
    setup_monitoring
    exit 0
fi

# Run main deployment
main
