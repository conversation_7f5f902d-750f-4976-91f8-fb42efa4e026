import { chromium, FullConfig } from '@playwright/test'

async function globalTeardown(config: FullConfig) {
  console.log('🧹 Starting global test teardown...')
  
  const browser = await chromium.launch()
  const page = await browser.newPage()
  
  try {
    // Clean up test data
    console.log('🗑️ Cleaning up test data...')
    await cleanupTestData(page)
    
    console.log('✅ Global teardown completed successfully')
    
  } catch (error) {
    console.error('❌ Global teardown failed:', error)
    // Don't throw error in teardown to avoid masking test failures
  } finally {
    await browser.close()
  }
}

async function cleanupTestData(page: any) {
  try {
    // Login as test user
    const loginResponse = await page.request.post('http://localhost:8000/api/v1/auth/login', {
      data: {
        email: '<EMAIL>',
        password: 'testpassword'
      }
    })
    
    if (!loginResponse.ok()) {
      console.warn('⚠️ Could not login for cleanup')
      return
    }
    
    const loginData = await loginResponse.json()
    const token = loginData.access_token
    
    // Get all workloads
    const workloadsResponse = await page.request.get('http://localhost:8000/api/v1/workloads/', {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    })
    
    if (workloadsResponse.ok()) {
      const workloads = await workloadsResponse.json()
      
      // Delete test workloads
      for (const workload of workloads) {
        if (workload.name.includes('Test') || workload.name.includes('test')) {
          try {
            const deleteResponse = await page.request.delete(`http://localhost:8000/api/v1/workloads/${workload.id}`, {
              headers: {
                'Authorization': `Bearer ${token}`
              }
            })
            
            if (deleteResponse.ok()) {
              console.log(`🗑️ Deleted test workload: ${workload.name}`)
            }
          } catch (error) {
            console.warn(`⚠️ Could not delete workload ${workload.name}:`, error)
          }
        }
      }
    }
    
    // Clean up test benchmark jobs
    await cleanupTestBenchmarks(page, token)
    
    // Clean up test payments
    await cleanupTestPayments(page, token)
    
  } catch (error) {
    console.warn('⚠️ Test data cleanup failed:', error)
  }
}

async function cleanupTestBenchmarks(page: any, token: string) {
  try {
    const benchmarksResponse = await page.request.get('http://localhost:8000/api/v1/benchmarks/jobs', {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    })
    
    if (benchmarksResponse.ok()) {
      const benchmarks = await benchmarksResponse.json()
      
      // Delete test benchmarks (created in the last hour)
      const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000)
      
      for (const benchmark of benchmarks) {
        const createdAt = new Date(benchmark.created_at)
        if (createdAt > oneHourAgo) {
          try {
            const deleteResponse = await page.request.delete(`http://localhost:8000/api/v1/benchmarks/jobs/${benchmark.id}`, {
              headers: {
                'Authorization': `Bearer ${token}`
              }
            })
            
            if (deleteResponse.ok()) {
              console.log(`🗑️ Deleted test benchmark: ${benchmark.id}`)
            }
          } catch (error) {
            console.warn(`⚠️ Could not delete benchmark ${benchmark.id}:`, error)
          }
        }
      }
    }
    
  } catch (error) {
    console.warn('⚠️ Benchmark cleanup failed:', error)
  }
}

async function cleanupTestPayments(page: any, token: string) {
  try {
    const paymentsResponse = await page.request.get('http://localhost:8000/api/v1/payments/history', {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    })
    
    if (paymentsResponse.ok()) {
      const payments = await paymentsResponse.json()
      
      // Delete test payments (pending ones created in the last hour)
      const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000)
      
      for (const payment of payments) {
        const createdAt = new Date(payment.created_at)
        if (createdAt > oneHourAgo && payment.status === 'pending') {
          try {
            const deleteResponse = await page.request.delete(`http://localhost:8000/api/v1/payments/${payment.id}`, {
              headers: {
                'Authorization': `Bearer ${token}`
              }
            })
            
            if (deleteResponse.ok()) {
              console.log(`🗑️ Deleted test payment: ${payment.id}`)
            }
          } catch (error) {
            console.warn(`⚠️ Could not delete payment ${payment.id}:`, error)
          }
        }
      }
    }
    
  } catch (error) {
    console.warn('⚠️ Payment cleanup failed:', error)
  }
}

export default globalTeardown
