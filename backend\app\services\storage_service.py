"""
MinIO/S3 storage service for benchmark artifacts
"""

import io
import json
import logging
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, List
from pathlib import Path

from minio import Minio
from minio.error import S3Error

from ..core.config import settings

logger = logging.getLogger(__name__)


class StorageService:
    """Service for managing benchmark artifacts in MinIO/S3"""
    
    def __init__(self):
        self.client = Minio(
            settings.MINIO_ENDPOINT,
            access_key=settings.MINIO_ACCESS_KEY,
            secret_key=settings.MINIO_SECRET_KEY,
            secure=settings.MINIO_SECURE
        )
        self.bucket_name = settings.BUCKET_NAME
        self._ensure_bucket_exists()
    
    def _ensure_bucket_exists(self):
        """Ensure the bucket exists, create if not"""
        try:
            if not self.client.bucket_exists(self.bucket_name):
                self.client.make_bucket(self.bucket_name)
                logger.info(f"Created bucket: {self.bucket_name}")
        except S3Error as e:
            logger.error(f"Error ensuring bucket exists: {e}")
            raise
    
    def upload_benchmark_logs(
        self, 
        job_id: str, 
        logs: str, 
        log_type: str = "execution"
    ) -> str:
        """
        Upload benchmark execution logs
        
        Args:
            job_id: Benchmark job ID
            logs: Log content
            log_type: Type of log (execution, error, debug)
            
        Returns:
            Object key/path in storage
        """
        try:
            timestamp = datetime.utcnow().strftime("%Y%m%d_%H%M%S")
            object_key = f"logs/{job_id}/{log_type}_{timestamp}.log"
            
            # Convert logs to bytes
            logs_bytes = logs.encode('utf-8')
            logs_stream = io.BytesIO(logs_bytes)
            
            # Upload to MinIO
            self.client.put_object(
                bucket_name=self.bucket_name,
                object_name=object_key,
                data=logs_stream,
                length=len(logs_bytes),
                content_type="text/plain"
            )
            
            logger.info(f"Uploaded logs for job {job_id}: {object_key}")
            return object_key
            
        except S3Error as e:
            logger.error(f"Error uploading logs for job {job_id}: {e}")
            raise
    
    def upload_benchmark_artifacts(
        self, 
        job_id: str, 
        artifacts: Dict[str, Any]
    ) -> str:
        """
        Upload benchmark artifacts (configs, intermediate results, etc.)
        
        Args:
            job_id: Benchmark job ID
            artifacts: Dictionary of artifacts to store
            
        Returns:
            Object key/path in storage
        """
        try:
            timestamp = datetime.utcnow().strftime("%Y%m%d_%H%M%S")
            object_key = f"artifacts/{job_id}/artifacts_{timestamp}.json"
            
            # Convert artifacts to JSON bytes
            artifacts_json = json.dumps(artifacts, indent=2, default=str)
            artifacts_bytes = artifacts_json.encode('utf-8')
            artifacts_stream = io.BytesIO(artifacts_bytes)
            
            # Upload to MinIO
            self.client.put_object(
                bucket_name=self.bucket_name,
                object_name=object_key,
                data=artifacts_stream,
                length=len(artifacts_bytes),
                content_type="application/json"
            )
            
            logger.info(f"Uploaded artifacts for job {job_id}: {object_key}")
            return object_key
            
        except S3Error as e:
            logger.error(f"Error uploading artifacts for job {job_id}: {e}")
            raise
    
    def upload_performance_report(
        self, 
        user_id: int, 
        workload_id: int, 
        report_data: Dict[str, Any]
    ) -> str:
        """
        Upload performance analysis report
        
        Args:
            user_id: User ID
            workload_id: Workload ID
            report_data: Report data to store
            
        Returns:
            Object key/path in storage
        """
        try:
            timestamp = datetime.utcnow().strftime("%Y%m%d_%H%M%S")
            object_key = f"reports/user_{user_id}/workload_{workload_id}/report_{timestamp}.json"
            
            # Convert report to JSON bytes
            report_json = json.dumps(report_data, indent=2, default=str)
            report_bytes = report_json.encode('utf-8')
            report_stream = io.BytesIO(report_bytes)
            
            # Upload to MinIO
            self.client.put_object(
                bucket_name=self.bucket_name,
                object_name=object_key,
                data=report_stream,
                length=len(report_bytes),
                content_type="application/json"
            )
            
            logger.info(f"Uploaded report for user {user_id}, workload {workload_id}: {object_key}")
            return object_key
            
        except S3Error as e:
            logger.error(f"Error uploading report: {e}")
            raise
    
    def get_object(self, object_key: str) -> bytes:
        """
        Retrieve object from storage
        
        Args:
            object_key: Object key/path
            
        Returns:
            Object content as bytes
        """
        try:
            response = self.client.get_object(self.bucket_name, object_key)
            return response.read()
            
        except S3Error as e:
            logger.error(f"Error retrieving object {object_key}: {e}")
            raise
        finally:
            if 'response' in locals():
                response.close()
                response.release_conn()
    
    def get_object_url(self, object_key: str, expires: timedelta = timedelta(hours=1)) -> str:
        """
        Get presigned URL for object access
        
        Args:
            object_key: Object key/path
            expires: URL expiration time
            
        Returns:
            Presigned URL
        """
        try:
            url = self.client.presigned_get_object(
                bucket_name=self.bucket_name,
                object_name=object_key,
                expires=expires
            )
            return url
            
        except S3Error as e:
            logger.error(f"Error generating URL for {object_key}: {e}")
            raise
    
    def list_job_artifacts(self, job_id: str) -> List[Dict[str, Any]]:
        """
        List all artifacts for a specific job
        
        Args:
            job_id: Benchmark job ID
            
        Returns:
            List of artifact metadata
        """
        try:
            artifacts = []
            
            # List logs
            log_prefix = f"logs/{job_id}/"
            for obj in self.client.list_objects(self.bucket_name, prefix=log_prefix):
                artifacts.append({
                    "type": "log",
                    "key": obj.object_name,
                    "size": obj.size,
                    "last_modified": obj.last_modified,
                    "url": self.get_object_url(obj.object_name)
                })
            
            # List artifacts
            artifact_prefix = f"artifacts/{job_id}/"
            for obj in self.client.list_objects(self.bucket_name, prefix=artifact_prefix):
                artifacts.append({
                    "type": "artifact",
                    "key": obj.object_name,
                    "size": obj.size,
                    "last_modified": obj.last_modified,
                    "url": self.get_object_url(obj.object_name)
                })
            
            return artifacts
            
        except S3Error as e:
            logger.error(f"Error listing artifacts for job {job_id}: {e}")
            raise
    
    def cleanup_old_artifacts(self, retention_days: int = 30) -> Dict[str, int]:
        """
        Clean up old artifacts based on retention policy
        
        Args:
            retention_days: Number of days to retain artifacts
            
        Returns:
            Cleanup statistics
        """
        try:
            cutoff_date = datetime.utcnow() - timedelta(days=retention_days)
            deleted_count = 0
            total_size = 0
            
            # List all objects
            for obj in self.client.list_objects(self.bucket_name, recursive=True):
                if obj.last_modified < cutoff_date:
                    self.client.remove_object(self.bucket_name, obj.object_name)
                    deleted_count += 1
                    total_size += obj.size
                    logger.debug(f"Deleted old artifact: {obj.object_name}")
            
            logger.info(f"Cleanup completed: {deleted_count} objects, {total_size} bytes")
            return {
                "deleted_objects": deleted_count,
                "total_size_bytes": total_size,
                "retention_days": retention_days
            }
            
        except S3Error as e:
            logger.error(f"Error during cleanup: {e}")
            raise


# Global storage service instance
storage_service = StorageService()
