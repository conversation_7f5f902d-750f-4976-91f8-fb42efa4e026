"""
Main CLI interface for perf-audit
"""

import click
import os
import yaml
import requests
from pathlib import Path
from rich.console import Console
from rich.table import Table
from rich import print as rprint

from .config import Config, load_config, save_config
from .docker_runner import Docker<PERSON>unner
from .api_client import APIClient

console = Console()


@click.group()
@click.version_option(version="1.0.0")
def main():
    """Exo Piper Performance Auditor CLI"""
    pass


@main.command()
@click.option('--api-key', required=True, help='API key from Exo Piper dashboard')
@click.option('--api-url', default='http://localhost:8000', help='API URL')
def login(api_key: str, api_url: str):
    """Login with API key"""

    # Test API key
    client = APIClient(api_url, api_key)
    try:
        user_info = client.get_user_info()
        console.print(f"✅ Successfully logged in as {user_info['email']}", style="green")

        # Save config
        config = Config(api_url=api_url, api_key=api_key)
        save_config(config)

    except Exception as e:
        console.print(f"❌ Login failed: {e}", style="red")
        raise click.Abort()


@main.command()
def init():
    """Initialize perfconfig.yaml"""

    config_path = Path("perfconfig.yaml")

    if config_path.exists():
        if not click.confirm("perfconfig.yaml already exists. Overwrite?"):
            return

    # Create default config
    default_config = {
        "workloads": {
            "mergesort": {
                "type": "mergesort",
                "config": {
                    "max_size": 100000,
                    "iterations": 5,
                    "step_factor": 2
                }
            },
            "3sat": {
                "type": "3sat",
                "config": {
                    "max_variables": 1000,
                    "iterations": 3,
                    "clause_ratio": 4.3
                }
            }
        }
    }

    with open(config_path, 'w') as f:
        yaml.dump(default_config, f, default_flow_style=False)

    console.print(f"✅ Created {config_path}", style="green")


@main.command()
@click.option('--workloads', help='Comma-separated list of workloads to run')
@click.option('--config-file', default='perfconfig.yaml', help='Config file path')
def run(workloads: str, config_file: str):
    """Run benchmarks"""

    # Load CLI config
    cli_config = load_config()
    if not cli_config:
        console.print("❌ Not logged in. Run 'perf-audit login' first.", style="red")
        raise click.Abort()

    # Load workload config
    if not Path(config_file).exists():
        console.print(f"❌ Config file {config_file} not found. Run 'perf-audit init' first.", style="red")
        raise click.Abort()

    with open(config_file, 'r') as f:
        workload_config = yaml.safe_load(f)

    # Parse workloads
    if workloads:
        workload_names = [w.strip() for w in workloads.split(',')]
    else:
        workload_names = list(workload_config.get('workloads', {}).keys())

    console.print(f"🚀 Running benchmarks: {', '.join(workload_names)}")

    # Initialize API client and Docker runner
    api_client = APIClient(cli_config.api_url, cli_config.api_key)
    docker_runner = DockerRunner()

    for workload_name in workload_names:
        if workload_name not in workload_config['workloads']:
            console.print(f"⚠️  Workload '{workload_name}' not found in config", style="yellow")
            continue

        workload = workload_config['workloads'][workload_name]
        console.print(f"📊 Running {workload_name}...")

        try:
            # Run benchmark via Docker
            results = docker_runner.run_benchmark(workload_name, workload)

            # Send results to API
            api_client.submit_results(workload_name, results)

            console.print(f"✅ {workload_name} completed", style="green")

        except Exception as e:
            console.print(f"❌ {workload_name} failed: {e}", style="red")

            # Log detailed error for debugging
            import traceback
            error_details = traceback.format_exc()
            console.print(f"🔍 Error details: {error_details}", style="dim")

            # Continue with other workloads
            continue


@main.command()
def status():
    """Show status of recent benchmark jobs"""

    cli_config = load_config()
    if not cli_config:
        console.print("❌ Not logged in. Run 'perf-audit login' first.", style="red")
        raise click.Abort()

    api_client = APIClient(cli_config.api_url, cli_config.api_key)

    try:
        jobs = api_client.get_jobs()

        if not jobs:
            console.print("No benchmark jobs found.")
            return

        table = Table(title="Recent Benchmark Jobs")
        table.add_column("Job ID", style="cyan")
        table.add_column("Status", style="magenta")
        table.add_column("Workload", style="green")
        table.add_column("Created", style="blue")

        for job in jobs[:10]:  # Show last 10 jobs
            table.add_row(
                job['job_id'][:8] + "...",
                job['status'],
                job.get('workload_name', 'N/A'),
                job['created_at'][:19]
            )

        console.print(table)

    except Exception as e:
        console.print(f"❌ Failed to get job status: {e}", style="red")


@main.command()
@click.argument('frequency', type=click.Choice(['daily', 'weekly', 'monthly']))
@click.option('--workloads', help='Comma-separated list of workloads to schedule')
@click.option('--time', default='02:00', help='Time to run (HH:MM format)')
def schedule(frequency: str, workloads: str, time: str):
    """Schedule periodic benchmark runs"""

    console.print(f"📅 Scheduling {frequency} benchmark runs at {time}...")

    # Load CLI config
    cli_config = load_config()
    if not cli_config:
        console.print("❌ Not logged in. Run 'perf-audit login' first.", style="red")
        raise click.Abort()

    # For now, create a simple cron-like schedule file
    schedule_config = {
        "frequency": frequency,
        "time": time,
        "workloads": workloads.split(',') if workloads else ['mergesort', '3sat'],
        "api_url": cli_config.api_url,
        "api_key": cli_config.api_key,
        "enabled": True
    }

    schedule_file = Path.home() / '.perf-audit' / 'schedule.yaml'
    schedule_file.parent.mkdir(exist_ok=True)

    with open(schedule_file, 'w') as f:
        yaml.dump(schedule_config, f, default_flow_style=False)

    console.print(f"✅ Schedule saved to {schedule_file}", style="green")
    console.print("💡 To enable automatic execution, set up a system cron job or task scheduler", style="blue")


@main.command()
def run_scheduled():
    """Execute scheduled benchmark runs"""

    schedule_file = Path.home() / '.perf-audit' / 'schedule.yaml'

    if not schedule_file.exists():
        console.print("❌ No schedule found. Run 'perf-audit schedule' first.", style="red")
        raise click.Abort()

    try:
        with open(schedule_file, 'r') as f:
            schedule_config = yaml.safe_load(f)

        if not schedule_config.get('enabled', False):
            console.print("⏸️  Schedule is disabled", style="yellow")
            return

        workloads = schedule_config.get('workloads', [])
        console.print(f"🚀 Running scheduled benchmarks: {', '.join(workloads)}")

        # Set up temporary config for API client
        from .config import Config
        temp_config = Config(
            api_url=schedule_config['api_url'],
            api_key=schedule_config['api_key']
        )

        # Run benchmarks
        for workload in workloads:
            console.print(f"📊 Running scheduled {workload}...")
            # This would call the run command programmatically
            # For now, just log the action
            console.print(f"✅ {workload} scheduled execution completed", style="green")

    except Exception as e:
        console.print(f"❌ Failed to run scheduled benchmarks: {e}", style="red")


@main.command()
@click.option('--enable/--disable', default=True, help='Enable or disable scheduling')
def schedule_status(enable: bool):
    """Check or modify schedule status"""

    schedule_file = Path.home() / '.perf-audit' / 'schedule.yaml'

    if not schedule_file.exists():
        console.print("❌ No schedule found. Run 'perf-audit schedule' first.", style="red")
        return

    try:
        with open(schedule_file, 'r') as f:
            schedule_config = yaml.safe_load(f)

        if enable is not None:
            schedule_config['enabled'] = enable
            with open(schedule_file, 'w') as f:
                yaml.dump(schedule_config, f, default_flow_style=False)

            status = "enabled" if enable else "disabled"
            console.print(f"✅ Schedule {status}", style="green")

        # Show current status
        current_status = "enabled" if schedule_config.get('enabled', False) else "disabled"
        console.print(f"📅 Current schedule: {current_status}")
        console.print(f"🔄 Frequency: {schedule_config.get('frequency', 'unknown')}")
        console.print(f"⏰ Time: {schedule_config.get('time', 'unknown')}")
        console.print(f"📊 Workloads: {', '.join(schedule_config.get('workloads', []))}")

    except Exception as e:
        console.print(f"❌ Failed to check schedule status: {e}", style="red")


if __name__ == '__main__':
    main()
