import Link from 'next/link'
import { ChartBarIcon, CpuChipIcon, ClockIcon, BoltIcon } from '@heroicons/react/24/outline'

export default function HomePage() {
  return (
    <div className="min-h-screen">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <CpuChipIcon className="h-8 w-8 text-primary-600" />
              <h1 className="ml-2 text-2xl font-bold text-gray-900">Exo Piper</h1>
            </div>
            <div className="flex space-x-4">
              <Link href="/login" className="btn-secondary">
                Login
              </Link>
              <Link href="/register" className="btn-primary">
                Come<PERSON><PERSON> Grá<PERSON>
              </Link>
            </div>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="bg-gradient-to-r from-primary-600 to-primary-700 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
          <div className="text-center">
            <h2 className="text-4xl font-bold mb-6">
              Performance Auditor SaaS
            </h2>
            <p className="text-xl mb-8 text-primary-100">
              Baseado no Teorema da Relatividade da Complexidade
            </p>
            <p className="text-lg mb-12 max-w-3xl mx-auto">
              Transforme auditorias pontuais de performance em um produto self-service, 
              recorrente e escalável. Separe efeitos de hardware (constantes) dos 
              efeitos algorítmicos (expoentes).
            </p>
            <Link href="/register" className="bg-white text-primary-600 hover:bg-gray-50 font-bold py-3 px-8 rounded-lg text-lg transition-colors">
              Começar Agora
            </Link>
          </div>
        </div>
      </section>

      {/* Features */}
      <section className="py-24">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h3 className="text-3xl font-bold text-gray-900 mb-4">
              Como Funciona
            </h3>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              O Exo Piper aplica o protocolo do Teorema da Relatividade da Complexidade 
              para análise automatizada de performance.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="bg-primary-100 rounded-full p-4 w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                <CpuChipIcon className="h-8 w-8 text-primary-600" />
              </div>
              <h4 className="text-xl font-semibold mb-2">Agent Docker + CLI</h4>
              <p className="text-gray-600">
                Executa benchmarks (mergesort, 3-SAT, ML inference) localmente
              </p>
            </div>

            <div className="text-center">
              <div className="bg-primary-100 rounded-full p-4 w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                <ChartBarIcon className="h-8 w-8 text-primary-600" />
              </div>
              <h4 className="text-xl font-semibold mb-2">Coleta λ e p</h4>
              <p className="text-gray-600">
                Usando TorchScript, subtração de overhead e regressão log-log
              </p>
            </div>

            <div className="text-center">
              <div className="bg-primary-100 rounded-full p-4 w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                <ClockIcon className="h-8 w-8 text-primary-600" />
              </div>
              <h4 className="text-xl font-semibold mb-2">FastAPI/Celery</h4>
              <p className="text-gray-600">
                Orquestra jobs, normaliza tempos e valida invariância
              </p>
            </div>

            <div className="text-center">
              <div className="bg-primary-100 rounded-full p-4 w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                <BoltIcon className="h-8 w-8 text-primary-600" />
              </div>
              <h4 className="text-xl font-semibold mb-2">Alertas Automáticos</h4>
              <p className="text-gray-600">
                Disparam quando Δp > 0.05 ou outras métricas críticas
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Pricing */}
      <section className="bg-gray-50 py-24">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h3 className="text-3xl font-bold text-gray-900 mb-4">
              Planos Simples e Transparentes
            </h3>
          </div>

          <div className="grid md:grid-cols-3 gap-8 max-w-5xl mx-auto">
            {/* Free Plan */}
            <div className="card text-center">
              <h4 className="text-2xl font-bold mb-2">Free</h4>
              <div className="text-4xl font-bold text-primary-600 mb-4">$0</div>
              <ul className="text-left space-y-2 mb-8">
                <li>✓ 2 workloads</li>
                <li>✓ Retenção 7 dias</li>
                <li>✓ Suporte básico</li>
              </ul>
              <Link href="/register" className="btn-secondary w-full">
                Começar Grátis
              </Link>
            </div>

            {/* Pro Plan */}
            <div className="card text-center border-primary-500 border-2 relative">
              <div className="absolute -top-4 left-1/2 transform -translate-x-1/2 bg-primary-600 text-white px-4 py-1 rounded-full text-sm font-medium">
                Mais Popular
              </div>
              <h4 className="text-2xl font-bold mb-2">Pro</h4>
              <div className="text-4xl font-bold text-primary-600 mb-4">$29</div>
              <ul className="text-left space-y-2 mb-8">
                <li>✓ 5 workloads</li>
                <li>✓ Retenção 30 dias</li>
                <li>✓ Alertas por email</li>
                <li>✓ Suporte prioritário</li>
              </ul>
              <Link href="/register" className="btn-primary w-full">
                Começar Teste
              </Link>
            </div>

            {/* Team Plan */}
            <div className="card text-center">
              <h4 className="text-2xl font-bold mb-2">Team</h4>
              <div className="text-4xl font-bold text-primary-600 mb-4">$79</div>
              <ul className="text-left space-y-2 mb-8">
                <li>✓ 20 workloads</li>
                <li>✓ Retenção 90 dias</li>
                <li>✓ Múltiplos usuários</li>
                <li>✓ SSO integration</li>
                <li>✓ Suporte premium</li>
              </ul>
              <Link href="/register" className="btn-secondary w-full">
                Falar com Vendas
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <div className="flex items-center justify-center mb-4">
              <CpuChipIcon className="h-8 w-8 text-primary-400" />
              <span className="ml-2 text-2xl font-bold">Exo Piper</span>
            </div>
            <p className="text-gray-400">
              Performance Auditor SaaS - Teorema da Relatividade da Complexidade
            </p>
          </div>
        </div>
      </footer>
    </div>
  )
}
