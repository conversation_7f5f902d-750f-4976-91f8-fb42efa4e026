"""
3-SAT problem benchmark implementation
"""

import random
from typing import List, Dict, Any, Tuple
from .base import BaseBenchmark


class SAT3Benchmark(BaseBenchmark):
    """3-SAT problem solver benchmark"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.validate_config(['max_variables'])
    
    def get_algorithm_name(self) -> str:
        return "3sat"
    
    def generate_3sat_instance(self, num_vars: int, num_clauses: int) -> List[Tuple[int, int, int]]:
        """Generate a random 3-SAT instance"""
        clauses = []
        
        for _ in range(num_clauses):
            # Pick 3 different variables
            vars_in_clause = random.sample(range(1, num_vars + 1), 3)
            
            # Randomly negate each variable
            clause = tuple(
                var if random.choice([True, False]) else -var
                for var in vars_in_clause
            )
            clauses.append(clause)
        
        return clauses
    
    def evaluate_assignment(self, clauses: List[Tuple[int, int, int]], assignment: Dict[int, bool]) -> bool:
        """Evaluate if an assignment satisfies all clauses"""
        for clause in clauses:
            clause_satisfied = False
            
            for literal in clause:
                var = abs(literal)
                value = assignment.get(var, False)
                
                # If literal is positive, we want the variable to be True
                # If literal is negative, we want the variable to be False
                if (literal > 0 and value) or (literal < 0 and not value):
                    clause_satisfied = True
                    break
            
            if not clause_satisfied:
                return False
        
        return True
    
    def solve_3sat_bruteforce(self, clauses: List[Tuple[int, int, int]], num_vars: int) -> bool:
        """Solve 3-SAT using brute force (exponential time)"""
        # Try all possible assignments
        for i in range(2 ** num_vars):
            assignment = {}
            
            # Convert binary representation to variable assignment
            for var in range(1, num_vars + 1):
                assignment[var] = bool((i >> (var - 1)) & 1)
            
            if self.evaluate_assignment(clauses, assignment):
                return True
        
        return False
    
    def get_input_sizes(self) -> List[int]:
        """Generate variable counts for benchmarking"""
        max_vars = self.config.get('max_variables', 20)
        min_vars = self.config.get('min_variables', 5)
        step = self.config.get('step', 1)
        
        sizes = []
        current = min_vars
        
        while current <= max_vars:
            sizes.append(current)
            current += step
        
        return sizes
    
    def benchmark_3sat(self, num_vars: int) -> None:
        """Benchmark 3-SAT solver for a specific number of variables"""
        # Use standard clause-to-variable ratio for 3-SAT
        clause_ratio = self.config.get('clause_ratio', 4.3)
        num_clauses = int(num_vars * clause_ratio)
        
        # Generate random 3-SAT instance
        clauses = self.generate_3sat_instance(num_vars, num_clauses)
        
        # Solve using brute force
        is_satisfiable = self.solve_3sat_bruteforce(clauses, num_vars)
        
        # Log result for verification
        self.log(f"3-SAT with {num_vars} vars, {num_clauses} clauses: {'SAT' if is_satisfiable else 'UNSAT'}")
    
    def run(self) -> List[Dict[str, Any]]:
        """Run 3-SAT benchmark"""
        self.log("Starting 3-SAT benchmark")
        
        input_sizes = self.get_input_sizes()
        results = []
        
        for num_vars in input_sizes:
            self.log(f"Benchmarking 3-SAT with {num_vars} variables")
            
            try:
                result = self.run_multiple_iterations(self.benchmark_3sat, num_vars)
                
                # Add 3-SAT specific metadata
                clause_ratio = self.config.get('clause_ratio', 4.3)
                result['metadata'] = {
                    'num_variables': num_vars,
                    'num_clauses': int(num_vars * clause_ratio),
                    'clause_ratio': clause_ratio,
                    'complexity_class': 'NP-complete'
                }
                
                results.append(result)
                
                self.log(f"Completed {num_vars} variables: {result['execution_time_ns']} ns")
                
            except Exception as e:
                self.log(f"Error with {num_vars} variables: {e}")
                continue
        
        self.log(f"3-SAT benchmark completed: {len(results)} data points")
        return results
