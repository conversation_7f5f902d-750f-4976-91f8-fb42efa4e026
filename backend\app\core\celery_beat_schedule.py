"""
Celery Beat schedule configuration for periodic tasks
"""

from celery.schedules import crontab

# Celery Beat schedule for periodic tasks
beat_schedule = {
    # Monitor pending payments every 5 minutes
    'monitor-pending-payments': {
        'task': 'app.services.benchmark_tasks.monitor_pending_payments_task',
        'schedule': 300.0,  # 5 minutes in seconds
        'options': {'queue': 'payments'}
    },

    # Clean up expired payments every hour
    'cleanup-expired-payments': {
        'task': 'app.services.benchmark_tasks.cleanup_user_data_task',
        'schedule': crontab(minute=0),  # Every hour at minute 0
        'options': {'queue': 'cleanup'}
    },

    # Daily data retention cleanup at 2 AM
    'daily-data-cleanup': {
        'task': 'app.services.benchmark_tasks.cleanup_user_data_task',
        'schedule': crontab(hour=2, minute=0),  # Daily at 2:00 AM
        'options': {'queue': 'cleanup'}
    },

    # Weekly comprehensive cleanup on Sundays at 3 AM
    'weekly-comprehensive-cleanup': {
        'task': 'app.services.benchmark_tasks.cleanup_user_data_task',
        'schedule': crontab(hour=3, minute=0, day_of_week=0),  # Sunday at 3:00 AM
        'options': {'queue': 'cleanup'}
    },

    # Check for performance regressions every 30 minutes
    'check-performance-regressions': {
        'task': 'app.services.benchmark_tasks.analyze_performance_regression',
        'schedule': 1800.0,  # 30 minutes in seconds
        'options': {'queue': 'analysis'}
    },

    # Check user limits every hour
    'check-user-limits': {
        'task': 'app.services.benchmark_tasks.check_user_limits_task',
        'schedule': 3600.0,  # 1 hour in seconds
        'options': {'queue': 'notifications'}
    }
}

# Task routing configuration
task_routes = {
    'app.services.benchmark_tasks.run_benchmark_job': {'queue': 'benchmarks'},
    'app.services.benchmark_tasks.analyze_performance_regression': {'queue': 'analysis'},
    'app.services.benchmark_tasks.monitor_pending_payments_task': {'queue': 'payments'},
    'app.services.benchmark_tasks.cleanup_user_data_task': {'queue': 'cleanup'},
    'app.services.benchmark_tasks.check_user_limits_task': {'queue': 'notifications'},
    'app.services.notification_tasks.*': {'queue': 'notifications'},
}
