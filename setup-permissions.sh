#!/bin/bash

# Setup script permissions for Exo Piper
echo "🔧 Setting up script permissions for Exo Piper..."

# Make all shell scripts executable
chmod +x *.sh
chmod +x backend/*.sh
chmod +x backend/run-tests.sh 2>/dev/null || echo "backend/run-tests.sh not found"

# Make Python scripts executable
chmod +x *.py

echo "✅ All script permissions set!"
echo ""
echo "Available scripts:"
echo "  ./test-system.sh          - Test complete system"
echo "  ./build-agent.sh          - Build Docker agent"
echo "  ./deploy-production.sh    - Deploy to production"
echo "  ./backup-system.sh        - Backup system data"
echo "  ./test-payment-system.py  - Test payment system"
echo ""
echo "Backend scripts:"
echo "  ./backend/run-tests.sh    - Run backend tests"
echo ""
echo "Ready to use! 🚀"
