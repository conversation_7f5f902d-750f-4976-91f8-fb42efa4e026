"""
Analytics service for payment and usage metrics
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List
from decimal import Decimal
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, or_

from ..core.database import AsyncSessionLocal
from ..models.payment import Payment, PaymentStatus, TANOSSwap
from ..models.user import User, PlanType
from ..models.benchmark import BenchmarkJob

logger = logging.getLogger(__name__)


class AnalyticsService:
    """Service for payment and usage analytics"""
    
    async def get_payment_analytics(
        self, 
        start_date: datetime = None, 
        end_date: datetime = None
    ) -> Dict[str, Any]:
        """
        Get comprehensive payment analytics
        
        Args:
            start_date: Start date for analytics (default: 30 days ago)
            end_date: End date for analytics (default: now)
            
        Returns:
            Dictionary with payment analytics data
        """
        
        if not start_date:
            start_date = datetime.utcnow() - timedelta(days=30)
        if not end_date:
            end_date = datetime.utcnow()
        
        async with AsyncSessionLocal() as db:
            # Total payments
            total_payments_query = select(func.count(Payment.id)).where(
                and_(
                    Payment.created_at >= start_date,
                    Payment.created_at <= end_date
                )
            )
            total_payments_result = await db.execute(total_payments_query)
            total_payments = total_payments_result.scalar() or 0
            
            # Confirmed payments
            confirmed_payments_query = select(func.count(Payment.id)).where(
                and_(
                    Payment.created_at >= start_date,
                    Payment.created_at <= end_date,
                    Payment.status == PaymentStatus.CONFIRMED
                )
            )
            confirmed_payments_result = await db.execute(confirmed_payments_query)
            confirmed_payments = confirmed_payments_result.scalar() or 0
            
            # Total revenue
            revenue_query = select(func.sum(Payment.amount)).where(
                and_(
                    Payment.created_at >= start_date,
                    Payment.created_at <= end_date,
                    Payment.status == PaymentStatus.CONFIRMED,
                    Payment.currency == "USDT"
                )
            )
            revenue_result = await db.execute(revenue_query)
            total_revenue = revenue_result.scalar() or Decimal('0')
            
            # Revenue by network
            network_revenue_query = select(
                Payment.network,
                func.sum(Payment.amount).label('revenue'),
                func.count(Payment.id).label('count')
            ).where(
                and_(
                    Payment.created_at >= start_date,
                    Payment.created_at <= end_date,
                    Payment.status == PaymentStatus.CONFIRMED
                )
            ).group_by(Payment.network)
            
            network_revenue_result = await db.execute(network_revenue_query)
            network_revenue = [
                {
                    "network": row.network,
                    "revenue": float(row.revenue or 0),
                    "count": row.count
                }
                for row in network_revenue_result
            ]
            
            # Revenue by plan
            plan_revenue_query = select(
                Payment.target_plan,
                func.sum(Payment.amount).label('revenue'),
                func.count(Payment.id).label('count')
            ).where(
                and_(
                    Payment.created_at >= start_date,
                    Payment.created_at <= end_date,
                    Payment.status == PaymentStatus.CONFIRMED
                )
            ).group_by(Payment.target_plan)
            
            plan_revenue_result = await db.execute(plan_revenue_query)
            plan_revenue = [
                {
                    "plan": row.target_plan,
                    "revenue": float(row.revenue or 0),
                    "count": row.count
                }
                for row in plan_revenue_result
            ]
            
            # Conversion rate
            conversion_rate = (confirmed_payments / total_payments * 100) if total_payments > 0 else 0
            
            # Average payment time (time to confirmation)
            avg_payment_time_query = select(
                func.avg(
                    func.extract('epoch', Payment.confirmed_at - Payment.created_at)
                )
            ).where(
                and_(
                    Payment.created_at >= start_date,
                    Payment.created_at <= end_date,
                    Payment.status == PaymentStatus.CONFIRMED,
                    Payment.confirmed_at.isnot(None)
                )
            )
            avg_payment_time_result = await db.execute(avg_payment_time_query)
            avg_payment_time_seconds = avg_payment_time_result.scalar() or 0
            
            return {
                "period": {
                    "start_date": start_date.isoformat(),
                    "end_date": end_date.isoformat(),
                    "days": (end_date - start_date).days
                },
                "overview": {
                    "total_payments": total_payments,
                    "confirmed_payments": confirmed_payments,
                    "pending_payments": total_payments - confirmed_payments,
                    "conversion_rate": round(conversion_rate, 2),
                    "total_revenue_usdt": float(total_revenue),
                    "average_payment_time_minutes": round(avg_payment_time_seconds / 60, 2) if avg_payment_time_seconds else 0
                },
                "by_network": network_revenue,
                "by_plan": plan_revenue
            }
    
    async def get_user_analytics(self) -> Dict[str, Any]:
        """Get user analytics and plan distribution"""
        
        async with AsyncSessionLocal() as db:
            # Total users
            total_users_query = select(func.count(User.id))
            total_users_result = await db.execute(total_users_query)
            total_users = total_users_result.scalar() or 0
            
            # Users by plan
            plan_distribution_query = select(
                User.plan_type,
                func.count(User.id).label('count')
            ).group_by(User.plan_type)
            
            plan_distribution_result = await db.execute(plan_distribution_query)
            plan_distribution = {
                row.plan_type: row.count
                for row in plan_distribution_result
            }
            
            # Active users (users with jobs in last 30 days)
            thirty_days_ago = datetime.utcnow() - timedelta(days=30)
            active_users_query = select(func.count(func.distinct(BenchmarkJob.user_id))).where(
                BenchmarkJob.created_at >= thirty_days_ago
            )
            active_users_result = await db.execute(active_users_query)
            active_users = active_users_result.scalar() or 0
            
            # New users (last 30 days)
            new_users_query = select(func.count(User.id)).where(
                User.created_at >= thirty_days_ago
            )
            new_users_result = await db.execute(new_users_query)
            new_users = new_users_result.scalar() or 0
            
            # Users with expired plans
            expired_plans_query = select(func.count(User.id)).where(
                and_(
                    User.plan_expires_at < datetime.utcnow(),
                    User.plan_type != 'free'
                )
            )
            expired_plans_result = await db.execute(expired_plans_query)
            expired_plans = expired_plans_result.scalar() or 0
            
            return {
                "total_users": total_users,
                "active_users_30d": active_users,
                "new_users_30d": new_users,
                "expired_plans": expired_plans,
                "plan_distribution": plan_distribution,
                "activity_rate": round((active_users / total_users * 100) if total_users > 0 else 0, 2)
            }
    
    async def get_tanos_analytics(self) -> Dict[str, Any]:
        """Get TANOS swap analytics"""
        
        async with AsyncSessionLocal() as db:
            # Total TANOS swaps
            total_swaps_query = select(func.count(TANOSSwap.id))
            total_swaps_result = await db.execute(total_swaps_query)
            total_swaps = total_swaps_result.scalar() or 0
            
            # Swaps by status
            status_distribution_query = select(
                TANOSSwap.status,
                func.count(TANOSSwap.id).label('count')
            ).group_by(TANOSSwap.status)
            
            status_distribution_result = await db.execute(status_distribution_query)
            status_distribution = {
                row.status: row.count
                for row in status_distribution_result
            }
            
            # Completed swaps in last 30 days
            thirty_days_ago = datetime.utcnow() - timedelta(days=30)
            completed_swaps_query = select(func.count(TANOSSwap.id)).where(
                and_(
                    TANOSSwap.completed_at >= thirty_days_ago,
                    TANOSSwap.status == "completed"
                )
            )
            completed_swaps_result = await db.execute(completed_swaps_query)
            completed_swaps_30d = completed_swaps_result.scalar() or 0
            
            # Average swap completion time
            avg_completion_time_query = select(
                func.avg(
                    func.extract('epoch', TANOSSwap.completed_at - TANOSSwap.created_at)
                )
            ).where(
                and_(
                    TANOSSwap.status == "completed",
                    TANOSSwap.completed_at.isnot(None)
                )
            )
            avg_completion_time_result = await db.execute(avg_completion_time_query)
            avg_completion_time_seconds = avg_completion_time_result.scalar() or 0
            
            return {
                "total_swaps": total_swaps,
                "completed_swaps_30d": completed_swaps_30d,
                "status_distribution": status_distribution,
                "average_completion_time_minutes": round(avg_completion_time_seconds / 60, 2) if avg_completion_time_seconds else 0,
                "success_rate": round((status_distribution.get("completed", 0) / total_swaps * 100) if total_swaps > 0 else 0, 2)
            }
    
    async def get_revenue_forecast(self, days_ahead: int = 30) -> Dict[str, Any]:
        """Generate revenue forecast based on historical data"""
        
        async with AsyncSessionLocal() as db:
            # Get last 90 days of revenue data
            ninety_days_ago = datetime.utcnow() - timedelta(days=90)
            
            daily_revenue_query = select(
                func.date(Payment.confirmed_at).label('date'),
                func.sum(Payment.amount).label('revenue')
            ).where(
                and_(
                    Payment.confirmed_at >= ninety_days_ago,
                    Payment.status == PaymentStatus.CONFIRMED,
                    Payment.currency == "USDT"
                )
            ).group_by(func.date(Payment.confirmed_at)).order_by(func.date(Payment.confirmed_at))
            
            daily_revenue_result = await db.execute(daily_revenue_query)
            daily_revenue = [
                {
                    "date": row.date.isoformat(),
                    "revenue": float(row.revenue)
                }
                for row in daily_revenue_result
            ]
            
            # Simple moving average forecast
            if len(daily_revenue) >= 7:
                recent_avg = sum(day["revenue"] for day in daily_revenue[-7:]) / 7
                forecast_daily = recent_avg
                forecast_total = forecast_daily * days_ahead
            else:
                forecast_daily = 0
                forecast_total = 0
            
            return {
                "historical_data": daily_revenue,
                "forecast": {
                    "days_ahead": days_ahead,
                    "estimated_daily_revenue": round(forecast_daily, 2),
                    "estimated_total_revenue": round(forecast_total, 2),
                    "method": "7-day moving average"
                }
            }
    
    async def get_comprehensive_dashboard(self) -> Dict[str, Any]:
        """Get comprehensive analytics dashboard data"""
        
        payment_analytics = await self.get_payment_analytics()
        user_analytics = await self.get_user_analytics()
        tanos_analytics = await self.get_tanos_analytics()
        revenue_forecast = await self.get_revenue_forecast()
        
        return {
            "generated_at": datetime.utcnow().isoformat(),
            "payments": payment_analytics,
            "users": user_analytics,
            "tanos": tanos_analytics,
            "forecast": revenue_forecast
        }


# Global analytics service instance
analytics_service = AnalyticsService()
