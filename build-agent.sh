#!/bin/bash

# Build Exo Piper Agent Docker Image

echo "🐳 Building Exo Piper Agent Docker Image..."

# Navigate to agent directory
cd agent

# Build the Docker image
docker build -t exopiper/agent:latest .

if [ $? -eq 0 ]; then
    echo "✅ Agent image built successfully!"
    echo "📦 Image: exopiper/agent:latest"
    
    # Show image info
    docker images exopiper/agent:latest
    
    echo ""
    echo "🚀 To test the agent:"
    echo "docker run --rm -e WORKLOAD_TYPE=mergesort -e WORKLOAD_CONFIG='{\"max_size\":1000,\"iterations\":3}' -e JOB_ID=test exopiper/agent:latest"
else
    echo "❌ Failed to build agent image"
    exit 1
fi
