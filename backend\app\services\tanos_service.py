"""
TANOS integration service for Bitcoin-Nostr atomic swaps
"""

import asyncio
import logging
import json
import subprocess
import tempfile
import os
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, Any, Optional
from decimal import Decimal
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from ..core.database import AsyncSessionLocal
from ..core.config import settings
from ..models.payment import Payment, TANOSSwap, PaymentStatus
from ..models.user import User, PlanType
from .qr_service import qr_service

logger = logging.getLogger(__name__)


class TANOSService:
    """Service for TANOS atomic swaps integration"""
    
    # Our Nostr keys for TANOS swaps (in production, store securely)
    NOSTR_PRIVATE_KEY = "your_nostr_private_key_here"  # Replace with actual key
    NOSTR_PUBLIC_KEY = "your_nostr_public_key_here"    # Replace with actual key
    
    # TANOS binary path (adjust based on deployment)
    TANOS_BINARY_PATH = "/app/tanos/bin/tanos"
    
    # Bitcoin network (testnet for development, mainnet for production)
    BITCOIN_NETWORK = "testnet"  # Change to "mainnet" for production
    
    # Plan prices in Bitcoin (approximate, update based on current rates)
    PLAN_PRICES_BTC = {
        PlanType.BASIC: Decimal("0.001"),      # ~$50 at $50k BTC
        PlanType.PRO: Decimal("0.004"),        # ~$200 at $50k BTC
        PlanType.ENTERPRISE: Decimal("0.020")  # ~$1000 at $50k BTC
    }
    
    def __init__(self):
        self.swaps_in_progress = {}  # Track active swaps
    
    async def create_tanos_swap(
        self, 
        user_id: int, 
        plan_type: PlanType
    ) -> Dict[str, Any]:
        """
        Create a new TANOS atomic swap for plan upgrade
        
        Args:
            user_id: User ID requesting the upgrade
            plan_type: Target plan type
            
        Returns:
            TANOS swap details including commitment and Bitcoin address
        """
        
        if plan_type not in self.PLAN_PRICES_BTC:
            raise ValueError(f"Invalid plan type: {plan_type}")
        
        amount_btc = self.PLAN_PRICES_BTC[plan_type]
        
        async with AsyncSessionLocal() as db:
            # Create payment record
            payment = Payment(
                user_id=user_id,
                amount=amount_btc,
                currency="BTC",
                network="bitcoin",
                target_plan=plan_type.value,
                payment_address="",  # Will be set after TANOS setup
                status=PaymentStatus.PENDING,
                expires_at=datetime.utcnow() + timedelta(hours=24)
            )
            
            db.add(payment)
            await db.commit()
            await db.refresh(payment)
            
            # Create TANOS swap
            try:
                swap_data = await self._initialize_tanos_swap(amount_btc, payment.id)
                
                # Create TANOS swap record
                tanos_swap = TANOSSwap(
                    payment_id=payment.id,
                    swap_id=swap_data["swap_id"],
                    seller_nostr_pubkey=self.NOSTR_PUBLIC_KEY,
                    seller_commitment=swap_data["commitment"],
                    status="initiated",
                    expires_at=datetime.utcnow() + timedelta(hours=24),
                    swap_data=json.dumps(swap_data)
                )
                
                db.add(tanos_swap)
                
                # Update payment with Bitcoin address
                payment.payment_address = swap_data["bitcoin_address"]
                
                await db.commit()
                await db.refresh(tanos_swap)
                
                # Generate QR code for Bitcoin payment
                qr_data = qr_service.generate_bitcoin_qr(
                    bitcoin_address=swap_data["bitcoin_address"],
                    amount_btc=amount_btc,
                    label=f"Exo Piper {plan_type.value.title()} Plan",
                    message=f"Payment for {plan_type.value} plan upgrade"
                )
                
                # Generate TANOS-specific QR code
                tanos_qr = qr_service.generate_tanos_swap_qr({
                    "swap_id": swap_data["swap_id"],
                    "commitment": swap_data["commitment"],
                    "amount": str(amount_btc)
                })
                
                return {
                    "payment_id": payment.id,
                    "swap_id": swap_data["swap_id"],
                    "amount_btc": str(amount_btc),
                    "bitcoin_address": swap_data["bitcoin_address"],
                    "commitment": swap_data["commitment"],
                    "expires_at": payment.expires_at.isoformat(),
                    "qr_code_bitcoin": qr_data,
                    "qr_code_tanos": tanos_qr,
                    "instructions": self._get_tanos_instructions()
                }
                
            except Exception as e:
                # Clean up payment record on error
                await db.delete(payment)
                await db.commit()
                raise e
    
    async def _initialize_tanos_swap(self, amount_btc: Decimal, payment_id: int) -> Dict[str, Any]:
        """Initialize TANOS swap using the TANOS binary"""
        
        try:
            # Create temporary files for TANOS communication
            with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as config_file:
                config = {
                    "seller_private_key": self.NOSTR_PRIVATE_KEY,
                    "amount_btc": str(amount_btc),
                    "network": self.BITCOIN_NETWORK,
                    "payment_id": payment_id
                }
                json.dump(config, config_file)
                config_path = config_file.name
            
            # Call TANOS binary to initialize swap
            cmd = [
                self.TANOS_BINARY_PATH,
                "init-swap",
                "--config", config_path,
                "--output", "json"
            ]
            
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=30
            )
            
            # Clean up config file
            os.unlink(config_path)
            
            if result.returncode != 0:
                raise Exception(f"TANOS initialization failed: {result.stderr}")
            
            swap_data = json.loads(result.stdout)
            
            # Store swap in memory for quick access
            self.swaps_in_progress[swap_data["swap_id"]] = {
                "payment_id": payment_id,
                "amount_btc": amount_btc,
                "created_at": datetime.utcnow(),
                "status": "initiated"
            }
            
            return swap_data
            
        except subprocess.TimeoutExpired:
            raise Exception("TANOS initialization timed out")
        except json.JSONDecodeError:
            raise Exception("Invalid response from TANOS binary")
        except Exception as e:
            logger.error(f"TANOS initialization error: {e}")
            raise Exception(f"Failed to initialize TANOS swap: {e}")
    
    async def check_tanos_swap_status(self, swap_id: str) -> Dict[str, Any]:
        """Check the status of a TANOS swap"""
        
        async with AsyncSessionLocal() as db:
            # Get TANOS swap record
            swap_query = select(TANOSSwap).where(TANOSSwap.swap_id == swap_id)
            swap_result = await db.execute(swap_query)
            tanos_swap = swap_result.scalar_one_or_none()
            
            if not tanos_swap:
                raise ValueError("TANOS swap not found")
            
            # Get associated payment
            payment_query = select(Payment).where(Payment.id == tanos_swap.payment_id)
            payment_result = await db.execute(payment_query)
            payment = payment_result.scalar_one_or_none()
            
            if not payment:
                raise ValueError("Associated payment not found")
            
            # If already completed, return status
            if tanos_swap.status == "completed":
                return {
                    "status": "completed",
                    "locking_tx_hash": tanos_swap.locking_tx_hash,
                    "spending_tx_hash": tanos_swap.spending_tx_hash,
                    "completed_at": tanos_swap.completed_at.isoformat() if tanos_swap.completed_at else None
                }
            
            # If expired, mark as expired
            if tanos_swap.expires_at < datetime.utcnow():
                tanos_swap.status = "expired"
                payment.status = PaymentStatus.EXPIRED
                await db.commit()
                return {"status": "expired"}
            
            # Check TANOS binary for swap status
            try:
                status_info = await self._check_tanos_binary_status(swap_id)
                
                if status_info["status"] == "completed":
                    # Update swap and payment status
                    tanos_swap.status = "completed"
                    tanos_swap.locking_tx_hash = status_info.get("locking_tx_hash")
                    tanos_swap.spending_tx_hash = status_info.get("spending_tx_hash")
                    tanos_swap.completed_at = datetime.utcnow()
                    
                    payment.status = PaymentStatus.CONFIRMED
                    payment.transaction_hash = status_info.get("spending_tx_hash")
                    payment.confirmed_at = datetime.utcnow()
                    
                    # Upgrade user plan
                    await self._upgrade_user_plan(payment.user_id, payment.target_plan, db)
                    
                    await db.commit()
                    
                    return {
                        "status": "completed",
                        "locking_tx_hash": tanos_swap.locking_tx_hash,
                        "spending_tx_hash": tanos_swap.spending_tx_hash,
                        "completed_at": tanos_swap.completed_at.isoformat()
                    }
                elif status_info["status"] == "locked":
                    # Bitcoin has been locked, waiting for completion
                    tanos_swap.status = "locked"
                    tanos_swap.locked_at = datetime.utcnow()
                    tanos_swap.locking_tx_hash = status_info.get("locking_tx_hash")
                    
                    await db.commit()
                    
                    return {
                        "status": "locked",
                        "locking_tx_hash": tanos_swap.locking_tx_hash,
                        "locked_at": tanos_swap.locked_at.isoformat()
                    }
                else:
                    return {"status": "pending"}
                    
            except Exception as e:
                logger.error(f"Error checking TANOS swap status: {e}")
                return {"status": "error", "message": str(e)}
    
    async def _check_tanos_binary_status(self, swap_id: str) -> Dict[str, Any]:
        """Check swap status using TANOS binary"""
        
        try:
            cmd = [
                self.TANOS_BINARY_PATH,
                "check-swap",
                "--swap-id", swap_id,
                "--output", "json"
            ]
            
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=30
            )
            
            if result.returncode != 0:
                raise Exception(f"TANOS status check failed: {result.stderr}")
            
            return json.loads(result.stdout)
            
        except subprocess.TimeoutExpired:
            raise Exception("TANOS status check timed out")
        except json.JSONDecodeError:
            raise Exception("Invalid response from TANOS binary")
    
    async def _upgrade_user_plan(self, user_id: int, target_plan: str, db: AsyncSession):
        """Upgrade user to the target plan"""
        
        user_query = select(User).where(User.id == user_id)
        user_result = await db.execute(user_query)
        user = user_result.scalar_one_or_none()
        
        if user:
            user.plan_type = target_plan
            user.plan_upgraded_at = datetime.utcnow()
            user.plan_expires_at = datetime.utcnow() + timedelta(days=30)
            
            logger.info(f"User {user_id} upgraded to {target_plan} via TANOS swap")
    
    def _get_tanos_instructions(self) -> list[str]:
        """Get instructions for TANOS atomic swap"""
        
        return [
            "1. Use uma wallet Bitcoin compatível com Taproot",
            "2. Envie o valor exato em Bitcoin para o endereço fornecido",
            "3. O sistema criará automaticamente o evento Nostr",
            "4. Após confirmação, você receberá acesso ao plano",
            "5. O swap é atômico - ou ambos recebem ou ninguém perde nada"
        ]
    
    async def cleanup_expired_swaps(self):
        """Clean up expired TANOS swaps"""
        
        async with AsyncSessionLocal() as db:
            expired_swaps_query = select(TANOSSwap).where(
                TANOSSwap.expires_at < datetime.utcnow(),
                TANOSSwap.status.in_(["initiated", "locked"])
            )
            
            expired_result = await db.execute(expired_swaps_query)
            expired_swaps = expired_result.scalars().all()
            
            for swap in expired_swaps:
                swap.status = "expired"
                
                # Also mark associated payment as expired
                payment_query = select(Payment).where(Payment.id == swap.payment_id)
                payment_result = await db.execute(payment_query)
                payment = payment_result.scalar_one_or_none()
                
                if payment:
                    payment.status = PaymentStatus.EXPIRED
            
            await db.commit()
            
            logger.info(f"Marked {len(expired_swaps)} TANOS swaps as expired")


# Global TANOS service instance
tanos_service = TANOSService()
