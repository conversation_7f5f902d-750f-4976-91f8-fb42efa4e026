#!/bin/bash

# Comprehensive test runner for Exo Piper
# Runs unit tests, integration tests, and system tests

set -e

echo "🧪 Exo Piper Comprehensive Test Suite"
echo "====================================="

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Test results tracking
TESTS_PASSED=0
TESTS_FAILED=0
TOTAL_TESTS=0

# Function to run a test and track results
run_test() {
    local test_name="$1"
    local test_command="$2"
    
    log_info "Running $test_name..."
    
    if eval "$test_command"; then
        log_success "$test_name passed"
        TESTS_PASSED=$((TESTS_PASSED + 1))
    else
        log_error "$test_name failed"
        TESTS_FAILED=$((TESTS_FAILED + 1))
    fi
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    echo
}

# Check if Docker is running
check_docker() {
    log_info "Checking Docker..."
    
    if ! docker info > /dev/null 2>&1; then
        log_error "Docker is not running. Please start Docker first."
        exit 1
    fi
    
    log_success "Docker is running"
}

# Setup test environment
setup_test_environment() {
    log_info "Setting up test environment..."
    
    # Stop any existing containers
    docker-compose down > /dev/null 2>&1 || true
    
    # Start test infrastructure
    docker-compose up -d postgres redis minio
    
    # Wait for services to be ready
    log_info "Waiting for services to be ready..."
    sleep 10
    
    # Check if services are healthy
    for service in postgres redis minio; do
        if docker-compose ps $service | grep -q "Up"; then
            log_success "$service is ready"
        else
            log_warning "$service may not be ready"
        fi
    done
}

# Backend tests
run_backend_tests() {
    log_info "Running backend tests..."
    
    cd backend
    
    # Install test dependencies if needed
    pip install -q pytest pytest-asyncio httpx
    
    # Run unit tests
    run_test "Backend Unit Tests" "python -m pytest tests/test_auth.py -v"
    run_test "Backend Workload Tests" "python -m pytest tests/test_workloads.py -v"
    run_test "Backend Analysis Tests" "python -m pytest tests/test_analysis_service.py -v"
    
    # Run integration tests
    run_test "Backend Integration Tests" "python -m pytest tests/test_integration.py -v"
    
    cd ..
}

# Frontend tests (if available)
run_frontend_tests() {
    log_info "Checking for frontend tests..."
    
    if [ -d "frontend" ] && [ -f "frontend/package.json" ]; then
        cd frontend
        
        # Check if test script exists
        if npm run | grep -q "test"; then
            run_test "Frontend Tests" "npm test -- --watchAll=false"
        else
            log_warning "No frontend tests configured"
        fi
        
        # Check if build works
        run_test "Frontend Build" "npm run build"
        
        cd ..
    else
        log_warning "Frontend directory not found or no package.json"
    fi
}

# API tests
run_api_tests() {
    log_info "Running API tests..."
    
    # Start backend
    cd backend
    uvicorn main:app --host 0.0.0.0 --port 8000 &
    BACKEND_PID=$!
    cd ..
    
    # Wait for backend to start
    sleep 5
    
    # Test API endpoints
    run_test "API Health Check" "curl -f http://localhost:8000/health"
    run_test "API Docs Available" "curl -f http://localhost:8000/docs"
    run_test "API OpenAPI Spec" "curl -f http://localhost:8000/openapi.json"
    
    # Kill backend
    kill $BACKEND_PID 2>/dev/null || true
    wait $BACKEND_PID 2>/dev/null || true
}

# System integration tests
run_system_tests() {
    log_info "Running system integration tests..."
    
    # Start full system
    docker-compose up -d
    
    # Wait for system to be ready
    log_info "Waiting for system to be ready..."
    sleep 30
    
    # Test system endpoints
    run_test "System Health Check" "curl -f http://localhost:8000/health"
    run_test "Frontend Accessible" "curl -f http://localhost:3000"
    
    # Test payment system
    if [ -f "test-payment-system.py" ]; then
        run_test "Payment System Test" "python test-payment-system.py"
    fi
    
    # Test CLI if available
    if [ -f "cli/setup.py" ]; then
        cd cli
        pip install -e . > /dev/null 2>&1
        run_test "CLI Installation" "perf-audit --help"
        cd ..
    fi
}

# Performance tests
run_performance_tests() {
    log_info "Running performance tests..."
    
    # Test Docker agent build
    if [ -f "build-agent.sh" ]; then
        run_test "Agent Docker Build" "./build-agent.sh"
    fi
    
    # Test benchmark execution (if system is running)
    if curl -f http://localhost:8000/health > /dev/null 2>&1; then
        log_info "Testing benchmark execution..."
        # This would require a more complex test setup
        log_warning "Benchmark execution tests require manual setup"
    fi
}

# Security tests
run_security_tests() {
    log_info "Running security tests..."
    
    # Test for common security issues
    run_test "No Hardcoded Secrets" "! grep -r 'password.*=' backend/ --include='*.py' | grep -v test"
    run_test "No Debug Mode in Production" "! grep -r 'DEBUG.*=.*True' backend/ --include='*.py'"
    
    # Test API security
    if curl -f http://localhost:8000/health > /dev/null 2>&1; then
        run_test "API Requires Auth" "! curl -f http://localhost:8000/api/v1/workloads/"
        run_test "CORS Headers Present" "curl -I http://localhost:8000/health | grep -i 'access-control'"
    fi
}

# Cleanup function
cleanup() {
    log_info "Cleaning up test environment..."
    
    # Stop all containers
    docker-compose down > /dev/null 2>&1 || true
    
    # Kill any remaining processes
    pkill -f "uvicorn" 2>/dev/null || true
    pkill -f "celery" 2>/dev/null || true
    
    log_info "Cleanup completed"
}

# Main test execution
main() {
    log_info "Starting comprehensive test suite..."
    
    # Setup
    check_docker
    setup_test_environment
    
    # Run test suites
    run_backend_tests
    run_frontend_tests
    run_api_tests
    run_system_tests
    run_performance_tests
    run_security_tests
    
    # Results summary
    echo
    echo "🏁 Test Results Summary"
    echo "======================"
    echo "Total Tests: $TOTAL_TESTS"
    echo "Passed: $TESTS_PASSED"
    echo "Failed: $TESTS_FAILED"
    
    if [ $TESTS_FAILED -eq 0 ]; then
        log_success "All tests passed! 🎉"
        echo
        echo "✅ System is ready for deployment"
        echo "✅ All components are working correctly"
        echo "✅ Security checks passed"
        echo "✅ Performance tests completed"
    else
        log_error "$TESTS_FAILED tests failed"
        echo
        echo "❌ Please fix failing tests before deployment"
        echo "❌ Check logs above for details"
    fi
    
    # Calculate success rate
    SUCCESS_RATE=$(( TESTS_PASSED * 100 / TOTAL_TESTS ))
    echo "Success Rate: $SUCCESS_RATE%"
    
    if [ $SUCCESS_RATE -ge 90 ]; then
        echo "🎯 Excellent! System is production-ready"
    elif [ $SUCCESS_RATE -ge 75 ]; then
        echo "⚠️  Good, but some improvements needed"
    else
        echo "🚨 Significant issues found, not ready for production"
    fi
}

# Trap cleanup on exit
trap cleanup EXIT

# Handle command line arguments
case "$1" in
    "backend")
        check_docker
        setup_test_environment
        run_backend_tests
        ;;
    "frontend")
        run_frontend_tests
        ;;
    "api")
        check_docker
        setup_test_environment
        run_api_tests
        ;;
    "system")
        check_docker
        setup_test_environment
        run_system_tests
        ;;
    "security")
        run_security_tests
        ;;
    "performance")
        check_docker
        setup_test_environment
        run_performance_tests
        ;;
    *)
        main
        ;;
esac

# Exit with appropriate code
if [ $TESTS_FAILED -eq 0 ]; then
    exit 0
else
    exit 1
fi
