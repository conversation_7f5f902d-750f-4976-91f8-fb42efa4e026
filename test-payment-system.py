#!/usr/bin/env python3
"""
Test script for USDT payment system with TANOS integration
"""

import asyncio
import httpx
import json
from datetime import datetime

# Configuration
API_BASE_URL = "http://localhost:8000/api/v1"
TEST_USER_EMAIL = "<EMAIL>"
TEST_USER_PASSWORD = "testpassword"

class PaymentSystemTester:
    """Test the payment system functionality"""
    
    def __init__(self):
        self.client = httpx.AsyncClient(timeout=30.0)
        self.token = None
        self.user_info = None
    
    async def login(self):
        """Login and get authentication token"""
        print("🔐 Logging in...")
        
        login_data = {
            "email": TEST_USER_EMAIL,
            "password": TEST_USER_PASSWORD
        }
        
        response = await self.client.post(f"{API_BASE_URL}/auth/login", json=login_data)
        
        if response.status_code == 200:
            data = response.json()
            self.token = data["access_token"]
            print(f"✅ Login successful")
            
            # Get user info
            await self.get_user_info()
        else:
            print(f"❌ Login failed: {response.text}")
            return False
        
        return True
    
    async def get_user_info(self):
        """Get current user information"""
        print("👤 Getting user info...")
        
        headers = {"Authorization": f"Bearer {self.token}"}
        response = await self.client.get(f"{API_BASE_URL}/auth/me", headers=headers)
        
        if response.status_code == 200:
            self.user_info = response.json()
            print(f"✅ User: {self.user_info['username']} (Plan: {self.user_info['plan_type']})")
        else:
            print(f"❌ Failed to get user info: {response.text}")
    
    async def get_available_plans(self):
        """Get available plans and pricing"""
        print("\n💰 Getting available plans...")
        
        headers = {"Authorization": f"Bearer {self.token}"}
        response = await self.client.get(f"{API_BASE_URL}/payments/plans", headers=headers)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Current plan: {data['current_plan']}")
            
            print("\n📋 Available plans:")
            for plan in data['plans']:
                status = "✅ Current" if plan['current'] else ("🔓 Available" if plan['available'] else "🔒 Not available")
                print(f"  {plan['display_name']}: ${plan['price_usd']}/month ({plan['price_usdt']} USDT) - {status}")
                if plan['features']:
                    for feature in plan['features'][:3]:  # Show first 3 features
                        print(f"    • {feature}")
            
            print("\n🌐 Payment networks:")
            for network in data['payment_networks']:
                print(f"  {network['name']} ({network['id']}): {network['fees']} fees, {network['confirmation_time']} confirmation")
            
            return data
        else:
            print(f"❌ Failed to get plans: {response.text}")
            return None
    
    async def create_payment_request(self, plan_type: str = "basic", network: str = "bep20"):
        """Create a payment request"""
        print(f"\n💳 Creating payment request for {plan_type} plan on {network}...")
        
        headers = {"Authorization": f"Bearer {self.token}"}
        payment_data = {
            "plan_type": plan_type,
            "network": network
        }
        
        response = await self.client.post(f"{API_BASE_URL}/payments/create", json=payment_data, headers=headers)
        
        if response.status_code == 200:
            data = response.json()
            payment = data['payment']
            
            print(f"✅ Payment request created!")
            print(f"   Payment ID: {payment['payment_id']}")
            print(f"   Amount: {payment['amount']} {payment['currency']}")
            print(f"   Network: {payment['network']}")
            print(f"   Wallet: {payment['wallet_address']}")
            print(f"   Contract: {payment['contract_address']}")
            print(f"   Expires: {payment['expires_at']}")
            
            print(f"\n📱 Instructions:")
            for i, instruction in enumerate(payment['instructions'], 1):
                print(f"   {i}. {instruction}")
            
            return payment
        else:
            print(f"❌ Failed to create payment: {response.text}")
            return None
    
    async def check_payment_status(self, payment_id: int):
        """Check payment status"""
        print(f"\n🔍 Checking payment status for ID {payment_id}...")
        
        headers = {"Authorization": f"Bearer {self.token}"}
        response = await self.client.get(f"{API_BASE_URL}/payments/status/{payment_id}", headers=headers)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Payment status: {data['status']}")
            
            if data.get('transaction_hash'):
                print(f"   Transaction: {data['transaction_hash']}")
            if data.get('confirmed_at'):
                print(f"   Confirmed: {data['confirmed_at']}")
            if data.get('message'):
                print(f"   Message: {data['message']}")
            
            return data
        else:
            print(f"❌ Failed to check payment status: {response.text}")
            return None
    
    async def get_payment_history(self):
        """Get payment history"""
        print(f"\n📜 Getting payment history...")
        
        headers = {"Authorization": f"Bearer {self.token}"}
        response = await self.client.get(f"{API_BASE_URL}/payments/history", headers=headers)
        
        if response.status_code == 200:
            data = response.json()
            payments = data['payments']
            
            if payments:
                print(f"✅ Found {len(payments)} payments:")
                for payment in payments:
                    print(f"   ID {payment['id']}: {payment['amount']} {payment['currency']} for {payment['target_plan']} - {payment['status']}")
                    print(f"      Created: {payment['created_at']}")
                    if payment['transaction_hash']:
                        print(f"      TX: {payment['transaction_hash']}")
            else:
                print("✅ No payment history found")
            
            return payments
        else:
            print(f"❌ Failed to get payment history: {response.text}")
            return None
    
    async def test_user_limits(self):
        """Test user limits and usage"""
        print(f"\n📊 Checking user limits and usage...")
        
        headers = {"Authorization": f"Bearer {self.token}"}
        
        # Check limits
        response = await self.client.get(f"{API_BASE_URL}/auth/limits", headers=headers)
        if response.status_code == 200:
            limits = response.json()
            print(f"✅ Current limits:")
            print(f"   Workloads: {limits['workloads']['current']}/{limits['workloads']['limit']} ({'Exceeded' if limits['workloads']['exceeded'] else 'OK'})")
            print(f"   Monthly jobs: {limits['monthly_jobs']['current']}/{limits['monthly_jobs']['limit']} ({'Exceeded' if limits['monthly_jobs']['exceeded'] else 'OK'})")
            print(f"   Plan: {limits['plan_type']}")
        
        # Check usage
        response = await self.client.get(f"{API_BASE_URL}/auth/usage", headers=headers)
        if response.status_code == 200:
            usage = response.json()
            print(f"✅ Current usage:")
            print(f"   Total jobs: {usage['usage']['jobs']['total']}")
            print(f"   Total results: {usage['usage']['results']['total']}")
    
    async def run_full_test(self):
        """Run complete payment system test"""
        print("🚀 Starting Payment System Test")
        print("=" * 50)
        
        try:
            # Login
            if not await self.login():
                return
            
            # Get plans
            plans_data = await self.get_available_plans()
            if not plans_data:
                return
            
            # Check user limits
            await self.test_user_limits()
            
            # Get payment history
            await self.get_payment_history()
            
            # Create a test payment request (only if user can upgrade)
            available_plans = [p for p in plans_data['plans'] if p['available']]
            if available_plans:
                test_plan = available_plans[0]['name']
                payment = await self.create_payment_request(test_plan, "bep20")
                
                if payment:
                    # Check payment status
                    await self.check_payment_status(payment['payment_id'])
                    
                    print(f"\n💡 To complete the payment:")
                    print(f"   1. Send exactly {payment['amount']} USDT")
                    print(f"   2. To address: {payment['wallet_address']}")
                    print(f"   3. On Binance Smart Chain (BEP20)")
                    print(f"   4. Check status again with: payment_id = {payment['payment_id']}")
            else:
                print("\n✅ User already has the highest available plan")
            
            print(f"\n✅ Payment system test completed successfully!")
            
        except Exception as e:
            print(f"❌ Test failed with error: {e}")
        finally:
            await self.client.aclose()


async def main():
    """Main test function"""
    tester = PaymentSystemTester()
    await tester.run_full_test()


if __name__ == "__main__":
    print("🧪 Exo Piper Payment System Tester")
    print("This script tests the USDT payment integration with TANOS")
    print("Make sure the backend is running on http://localhost:8000")
    print()
    
    asyncio.run(main())
