/**
 * API client for Exo Piper backend
 */

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'

export interface User {
  id: number
  email: string
  username: string
  full_name?: string
  is_active: boolean
  is_verified: boolean
  plan_type: 'free' | 'pro' | 'team'
  plan_expires_at?: string
  api_key?: string
  created_at: string
  updated_at?: string
}

export interface LoginRequest {
  email: string
  password: string
}

export interface RegisterRequest {
  email: string
  username: string
  full_name?: string
  password: string
}

export interface TokenResponse {
  access_token: string
  refresh_token: string
  token_type: string
}

export interface Workload {
  id: number
  user_id: number
  name: string
  description?: string
  workload_type: string
  config: Record<string, any>
  is_active: boolean
  schedule_enabled: boolean
  schedule_cron?: string
  created_at: string
  updated_at?: string
}

export interface BenchmarkJob {
  id: number
  job_id: string
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled'
  workload_id: number
  started_at?: string
  completed_at?: string
  duration_seconds?: number
  error_message?: string
  created_at: string
}

export interface Alert {
  id: string
  type: string
  severity: 'low' | 'medium' | 'high'
  workload_id: number
  workload_name: string
  message: string
  details: Record<string, any>
  created_at: string
}

class ApiClient {
  private baseURL: string
  private token: string | null = null

  constructor() {
    this.baseURL = API_BASE_URL
    // Try to get token from localStorage on client side
    if (typeof window !== 'undefined') {
      this.token = localStorage.getItem('access_token')
    }
  }

  setToken(token: string) {
    this.token = token
    if (typeof window !== 'undefined') {
      localStorage.setItem('access_token', token)
    }
  }

  clearToken() {
    this.token = null
    if (typeof window !== 'undefined') {
      localStorage.removeItem('access_token')
      localStorage.removeItem('refresh_token')
    }
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseURL}${endpoint}`
    
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      ...options.headers,
    }

    if (this.token) {
      headers.Authorization = `Bearer ${this.token}`
    }

    const response = await fetch(url, {
      ...options,
      headers,
    })

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      throw new Error(errorData.detail || `HTTP ${response.status}`)
    }

    return response.json()
  }

  // Authentication
  async login(credentials: LoginRequest): Promise<TokenResponse> {
    const response = await this.request<TokenResponse>('/api/v1/auth/login', {
      method: 'POST',
      body: JSON.stringify(credentials),
    })
    
    this.setToken(response.access_token)
    if (typeof window !== 'undefined') {
      localStorage.setItem('refresh_token', response.refresh_token)
    }
    
    return response
  }

  async register(userData: RegisterRequest): Promise<User> {
    return this.request<User>('/api/v1/auth/register', {
      method: 'POST',
      body: JSON.stringify(userData),
    })
  }

  async getCurrentUser(): Promise<User> {
    return this.request<User>('/api/v1/auth/me')
  }

  async getApiKey(): Promise<{ api_key: string; created_at: string }> {
    return this.request('/api/v1/auth/api-key')
  }

  async regenerateApiKey(): Promise<{ api_key: string; created_at: string }> {
    return this.request('/api/v1/auth/api-key', {
      method: 'POST',
    })
  }

  // Workloads
  async getWorkloads(page = 1, perPage = 10): Promise<{
    workloads: Workload[]
    total: number
    page: number
    per_page: number
  }> {
    return this.request(`/api/v1/workloads/?page=${page}&per_page=${perPage}`)
  }

  async getWorkload(id: number): Promise<Workload> {
    return this.request(`/api/v1/workloads/${id}`)
  }

  async createWorkload(workload: Omit<Workload, 'id' | 'user_id' | 'created_at' | 'updated_at'>): Promise<Workload> {
    return this.request('/api/v1/workloads/', {
      method: 'POST',
      body: JSON.stringify(workload),
    })
  }

  async updateWorkload(id: number, updates: Partial<Workload>): Promise<Workload> {
    return this.request(`/api/v1/workloads/${id}`, {
      method: 'PUT',
      body: JSON.stringify(updates),
    })
  }

  async deleteWorkload(id: number): Promise<{ message: string }> {
    return this.request(`/api/v1/workloads/${id}`, {
      method: 'DELETE',
    })
  }

  // Benchmarks
  async runBenchmark(workloadId: number): Promise<{
    job_id: string
    status: string
    message: string
  }> {
    return this.request(`/api/v1/benchmarks/run/${workloadId}`, {
      method: 'POST',
    })
  }

  async getBenchmarkJobs(): Promise<{ jobs: BenchmarkJob[] }> {
    return this.request('/api/v1/benchmarks/jobs')
  }

  async getBenchmarkJob(jobId: string): Promise<BenchmarkJob> {
    return this.request(`/api/v1/benchmarks/jobs/${jobId}`)
  }

  async cancelBenchmarkJob(jobId: string): Promise<{ message: string }> {
    return this.request(`/api/v1/benchmarks/jobs/${jobId}/cancel`, {
      method: 'POST',
    })
  }

  // Reports
  async getPerformanceSummary(workloadId?: number, days = 30): Promise<any> {
    const params = new URLSearchParams({ days: days.toString() })
    if (workloadId) {
      params.append('workload_id', workloadId.toString())
    }
    return this.request(`/api/v1/reports/performance-summary?${params}`)
  }

  async getComplexityAnalysis(workloadId: number, jobLimit = 5): Promise<any> {
    return this.request(`/api/v1/reports/complexity-analysis/${workloadId}?job_limit=${jobLimit}`)
  }

  async getAlerts(days = 7): Promise<{
    alerts: Alert[]
    total_alerts: number
    period_days: number
    generated_at: string
  }> {
    return this.request(`/api/v1/reports/alerts?days=${days}`)
  }

  // Billing
  async getAvailablePlans(): Promise<any> {
    return this.request('/api/v1/billing/plans')
  }

  async getCurrentSubscription(): Promise<any> {
    return this.request('/api/v1/billing/subscription')
  }

  async upgradePlan(planType: string): Promise<any> {
    return this.request('/api/v1/billing/upgrade', {
      method: 'POST',
      body: JSON.stringify({ plan_type: planType }),
    })
  }
}

export const apiClient = new ApiClient()
export default apiClient
