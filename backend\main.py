"""
Exo Piper - Performance Auditor SaaS
Main FastAPI application entry point
"""

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn
from contextlib import asynccontextmanager

from app.core.config import settings
from app.core.database import engine, Base
from app.api.auth import router as auth_router
from app.api.workloads import router as workloads_router
from app.api.benchmarks import router as benchmarks_router
from app.api.reports import router as reports_router
from app.api.billing import router as billing_router
from app.api.payments import router as payments_router
from app.api.v1.admin import router as admin_router


@asynccontextmanager
async def lifespan(_: FastAPI):
    """Application lifespan events"""
    # Startup
    print("🚀 Starting Exo Piper Performance Auditor...")

    # Create database tables
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)

    yield

    # Shutdown
    print("🛑 Shutting down Exo Piper...")


# Create FastAPI app
app = FastAPI(
    title="Exo Piper - Performance Auditor",
    description="Micro-SaaS baseado no Teorema da Relatividade da Complexidade",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_HOSTS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "service": "exo-Piper-api",
        "version": "1.0.0",
        "environment": settings.ENVIRONMENT
    }


# Root endpoint
@app.get("/")
async def root():
    """Root endpoint with API information"""
    return {
        "message": "Exo Piper - Performance Auditor API",
        "version": "1.0.0",
        "docs": "/docs",
        "health": "/health"
    }


# Include API routers
app.include_router(auth_router, prefix="/api/v1/auth", tags=["Authentication"])
app.include_router(workloads_router, prefix="/api/v1/workloads", tags=["Workloads"])
app.include_router(benchmarks_router, prefix="/api/v1/benchmarks", tags=["Benchmarks"])
app.include_router(reports_router, prefix="/api/v1/reports", tags=["Reports"])
app.include_router(billing_router, prefix="/api/v1/billing", tags=["Billing"])
app.include_router(payments_router, prefix="/api/v1/payments", tags=["Payments"])
app.include_router(admin_router, prefix="/api/v1", tags=["Admin"])


# Global exception handler
@app.exception_handler(Exception)
async def global_exception_handler(_, exc):
    """Global exception handler"""
    return JSONResponse(
        status_code=500,
        content={
            "error": "Internal server error",
            "detail": str(exc) if settings.ENVIRONMENT == "development" else "Something went wrong"
        }
    )


if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.ENVIRONMENT == "development"
    )
