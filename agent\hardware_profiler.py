"""
Hardware profiling utilities for benchmark agent
"""

import psutil
import platform
import subprocess
import json
from typing import Dict, Any, Optional


class HardwareProfiler:
    """Collects detailed hardware information for benchmarks"""
    
    def __init__(self):
        self.cpu_info = self._get_cpu_info()
        self.memory_info = self._get_memory_info()
        self.system_info = self._get_system_info()
    
    def _get_cpu_info(self) -> Dict[str, Any]:
        """Get CPU information"""
        try:
            cpu_freq = psutil.cpu_freq()
            return {
                "physical_cores": psutil.cpu_count(logical=False),
                "logical_cores": psutil.cpu_count(logical=True),
                "max_frequency_mhz": cpu_freq.max if cpu_freq else None,
                "current_frequency_mhz": cpu_freq.current if cpu_freq else None,
                "architecture": platform.machine(),
                "processor": platform.processor(),
            }
        except Exception as e:
            return {"error": str(e)}
    
    def _get_memory_info(self) -> Dict[str, Any]:
        """Get memory information"""
        try:
            memory = psutil.virtual_memory()
            return {
                "total_gb": round(memory.total / (1024**3), 2),
                "available_gb": round(memory.available / (1024**3), 2),
                "used_percent": memory.percent,
            }
        except Exception as e:
            return {"error": str(e)}
    
    def _get_system_info(self) -> Dict[str, Any]:
        """Get system information"""
        try:
            return {
                "platform": platform.platform(),
                "system": platform.system(),
                "release": platform.release(),
                "version": platform.version(),
                "python_version": platform.python_version(),
            }
        except Exception as e:
            return {"error": str(e)}
    
    def get_current_usage(self) -> Dict[str, Any]:
        """Get current system usage"""
        try:
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            
            return {
                "cpu_percent": cpu_percent,
                "memory_percent": memory.percent,
                "memory_used_gb": round((memory.total - memory.available) / (1024**3), 2),
                "load_average": psutil.getloadavg() if hasattr(psutil, 'getloadavg') else None,
            }
        except Exception as e:
            return {"error": str(e)}
    
    def get_full_profile(self) -> Dict[str, Any]:
        """Get complete hardware profile"""
        return {
            "cpu": self.cpu_info,
            "memory": self.memory_info,
            "system": self.system_info,
            "current_usage": self.get_current_usage(),
            "timestamp": psutil.boot_time(),
        }
    
    def measure_overhead(self, iterations: int = 1000) -> Dict[str, float]:
        """Measure system overhead for timing corrections"""
        import time
        
        # Measure time.perf_counter_ns() overhead
        start = time.perf_counter_ns()
        for _ in range(iterations):
            time.perf_counter_ns()
        end = time.perf_counter_ns()
        
        timing_overhead_ns = (end - start) / iterations
        
        # Measure function call overhead
        def empty_function():
            pass
        
        start = time.perf_counter_ns()
        for _ in range(iterations):
            empty_function()
        end = time.perf_counter_ns()
        
        function_overhead_ns = (end - start) / iterations
        
        return {
            "timing_overhead_ns": timing_overhead_ns,
            "function_overhead_ns": function_overhead_ns,
            "total_overhead_ns": timing_overhead_ns + function_overhead_ns,
        }


def get_docker_stats() -> Optional[Dict[str, Any]]:
    """Get Docker container resource limits and usage"""
    try:
        # Try to read cgroup information
        with open('/proc/self/cgroup', 'r') as f:
            cgroup_info = f.read()
        
        # Check if running in Docker
        if 'docker' in cgroup_info or 'containerd' in cgroup_info:
            stats = {}
            
            # Try to get memory limit
            try:
                with open('/sys/fs/cgroup/memory/memory.limit_in_bytes', 'r') as f:
                    memory_limit = int(f.read().strip())
                    if memory_limit < (1 << 62):  # Not unlimited
                        stats['memory_limit_gb'] = round(memory_limit / (1024**3), 2)
            except:
                pass
            
            # Try to get CPU limit
            try:
                with open('/sys/fs/cgroup/cpu/cpu.cfs_quota_us', 'r') as f:
                    quota = int(f.read().strip())
                with open('/sys/fs/cgroup/cpu/cpu.cfs_period_us', 'r') as f:
                    period = int(f.read().strip())
                
                if quota > 0:
                    stats['cpu_limit'] = quota / period
            except:
                pass
            
            return stats if stats else {"docker": True}
    
    except:
        pass
    
    return None
