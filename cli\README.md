# Perf-Audit CLI

CLI oficial para o Exo Piper Performance Auditor SaaS.

## Instala<PERSON>

```bash
pip install perf-audit
```

## Uso Rápido

```bash
# Login
perf-audit login --api-key <your-api-key>

# Inicializar configuração
perf-audit init

# Executar benchmarks
perf-audit run --workloads mergesort,3sat

# Ver status dos jobs
perf-audit status

# Agendar execução diária
perf-audit schedule daily
```

## Comandos Disponíveis

- `login` - Autenticar com API key
- `init` - Inicializar configuração local
- `run` - Executar benchmarks
- `status` - Ver status dos jobs
- `schedule` - Agendar execuções
- `config` - Gerenciar configurações
- `workloads` - Gerenciar workloads

## Configuração

O CLI usa um arquivo `perfconfig.yaml` para configurações locais:

```yaml
api_url: https://api.exoPiper.com
api_key: ep_your_api_key_here
workloads:
  mergesort:
    type: mergesort
    config:
      max_size: 1000000
      iterations: 10
```
