#!/bin/bash

# Run tests for Exo Piper backend

echo "🧪 Running Exo Piper Backend Tests..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    local status=$1
    local message=$2
    case $status in
        "SUCCESS")
            echo -e "${GREEN}✅ $message${NC}"
            ;;
        "ERROR")
            echo -e "${RED}❌ $message${NC}"
            ;;
        "WARNING")
            echo -e "${YELLOW}⚠️  $message${NC}"
            ;;
        "INFO")
            echo -e "${BLUE}ℹ️  $message${NC}"
            ;;
    esac
}

# Check if we're in the backend directory
if [ ! -f "requirements.txt" ]; then
    print_status "ERROR" "Please run this script from the backend directory"
    exit 1
fi

# Check if virtual environment exists
if [ ! -d "venv" ]; then
    print_status "INFO" "Creating virtual environment..."
    python -m venv venv
fi

# Activate virtual environment
print_status "INFO" "Activating virtual environment..."
source venv/bin/activate

# Install dependencies
print_status "INFO" "Installing dependencies..."
pip install -r requirements.txt

# Set environment variables for testing
export ENVIRONMENT=testing
export DATABASE_URL=sqlite+aiosqlite:///:memory:
export SECRET_KEY=test-secret-key
export REDIS_URL=redis://localhost:6379/1

# Run tests with coverage
print_status "INFO" "Running tests..."

# Install coverage if not present
pip install coverage

# Run tests with coverage
coverage run -m pytest tests/ -v --tb=short

if [ $? -eq 0 ]; then
    print_status "SUCCESS" "All tests passed!"
    
    # Generate coverage report
    print_status "INFO" "Generating coverage report..."
    coverage report
    coverage html
    
    print_status "SUCCESS" "Coverage report generated in htmlcov/"
    
else
    print_status "ERROR" "Some tests failed"
    exit 1
fi

# Deactivate virtual environment
deactivate

print_status "SUCCESS" "Test run completed!"
