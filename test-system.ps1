# Test script for Exo Piper system (PowerShell)

Write-Host "🧪 Testing Exo Piper System..." -ForegroundColor Cyan

# Function to print colored output
function Write-Status {
    param(
        [string]$Status,
        [string]$Message
    )
    
    switch ($Status) {
        "SUCCESS" { Write-Host "✅ $Message" -ForegroundColor Green }
        "ERROR" { Write-Host "❌ $Message" -ForegroundColor Red }
        "WARNING" { Write-Host "⚠️  $Message" -ForegroundColor Yellow }
        "INFO" { Write-Host "ℹ️  $Message" -ForegroundColor Blue }
    }
}

# Test 1: Check Docker
Write-Status "INFO" "Testing Docker availability..."
try {
    $dockerVersion = docker --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        docker info 2>$null | Out-Null
        if ($LASTEXITCODE -eq 0) {
            Write-Status "SUCCESS" "Docker is running"
        } else {
            Write-Status "ERROR" "Docker is installed but not running"
            exit 1
        }
    } else {
        Write-Status "ERROR" "Docker is not installed"
        exit 1
    }
} catch {
    Write-Status "ERROR" "Docker check failed"
    exit 1
}

# Test 2: Check Docker Compose
Write-Status "INFO" "Testing Docker Compose availability..."
try {
    docker-compose --version 2>$null | Out-Null
    if ($LASTEXITCODE -eq 0) {
        Write-Status "SUCCESS" "Docker Compose is available"
    } else {
        Write-Status "ERROR" "Docker Compose is not installed"
        exit 1
    }
} catch {
    Write-Status "ERROR" "Docker Compose check failed"
    exit 1
}

# Test 3: Build Agent Image
Write-Status "INFO" "Building Agent Docker image..."
Set-Location agent
try {
    docker build -t exopiper/agent:latest . 2>$null | Out-Null
    if ($LASTEXITCODE -eq 0) {
        Write-Status "SUCCESS" "Agent image built successfully"
    } else {
        Write-Status "ERROR" "Failed to build agent image"
        exit 1
    }
} catch {
    Write-Status "ERROR" "Agent build failed"
    exit 1
}
Set-Location ..

# Test 4: Test Agent
Write-Status "INFO" "Testing Agent with sample workload..."
try {
    $agentOutput = docker run --rm -e WORKLOAD_TYPE=mergesort -e WORKLOAD_CONFIG='{"max_size":1000,"iterations":2}' -e JOB_ID=test exopiper/agent:latest 2>&1
    if ($agentOutput -match "JSON_OUTPUT_START") {
        Write-Status "SUCCESS" "Agent executed successfully"
    } else {
        Write-Status "ERROR" "Agent execution failed"
        Write-Host $agentOutput
        exit 1
    }
} catch {
    Write-Status "ERROR" "Agent test failed"
    exit 1
}

# Test 5: Start Infrastructure
Write-Status "INFO" "Starting infrastructure services..."
try {
    docker-compose up -d postgres redis minio 2>$null | Out-Null
    if ($LASTEXITCODE -eq 0) {
        Write-Status "SUCCESS" "Infrastructure services started"
        Start-Sleep -Seconds 10
    } else {
        Write-Status "ERROR" "Failed to start infrastructure"
        exit 1
    }
} catch {
    Write-Status "ERROR" "Infrastructure startup failed"
    exit 1
}

# Test 6: Start Backend
Write-Status "INFO" "Starting backend service..."
try {
    docker-compose up -d backend 2>$null | Out-Null
    if ($LASTEXITCODE -eq 0) {
        Write-Status "SUCCESS" "Backend service started"
        Start-Sleep -Seconds 15
    } else {
        Write-Status "ERROR" "Failed to start backend"
        exit 1
    }
} catch {
    Write-Status "ERROR" "Backend startup failed"
    exit 1
}

# Test 7: Start Frontend
Write-Status "INFO" "Starting frontend service..."
try {
    docker-compose up -d frontend 2>$null | Out-Null
    if ($LASTEXITCODE -eq 0) {
        Write-Status "SUCCESS" "Frontend service started"
        Start-Sleep -Seconds 10
    } else {
        Write-Status "ERROR" "Failed to start frontend"
        exit 1
    }
} catch {
    Write-Status "ERROR" "Frontend startup failed"
    exit 1
}

# Test 8: CLI Installation
Write-Status "INFO" "Testing CLI installation..."
Set-Location cli
try {
    pip install -e . 2>$null | Out-Null
    if ($LASTEXITCODE -eq 0) {
        Write-Status "SUCCESS" "CLI installed successfully"
    } else {
        Write-Status "ERROR" "Failed to install CLI"
        exit 1
    }
} catch {
    Write-Status "ERROR" "CLI installation failed"
    exit 1
}
Set-Location ..

# Test 9: CLI Help
Write-Status "INFO" "Testing CLI help command..."
try {
    perf-audit --help 2>$null | Out-Null
    if ($LASTEXITCODE -eq 0) {
        Write-Status "SUCCESS" "CLI is working"
    } else {
        Write-Status "ERROR" "CLI help command failed"
        exit 1
    }
} catch {
    Write-Status "ERROR" "CLI test failed"
    exit 1
}

# Summary
Write-Host ""
Write-Status "SUCCESS" "System test completed!"
Write-Host ""
Write-Status "INFO" "Services running:"
Write-Host "  🌐 Frontend: http://localhost:3000"
Write-Host "  🔧 Backend API: http://localhost:8000"
Write-Host "  📚 API Docs: http://localhost:8000/docs"
Write-Host "  🗄️  MinIO Console: http://localhost:9001 (admin/password)"
Write-Host ""
Write-Status "INFO" "Next steps:"
Write-Host "  1. Register a user at http://localhost:3000/register"
Write-Host "  2. Get API key from dashboard"
Write-Host "  3. Test CLI: perf-audit login --api-key <your-key>"
Write-Host "  4. Run benchmark: perf-audit run --workloads mergesort"
Write-Host ""
Write-Status "SUCCESS" "Exo Piper is ready! 🚀"
