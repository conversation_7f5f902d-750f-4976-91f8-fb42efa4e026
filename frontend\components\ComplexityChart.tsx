'use client'

import { useEffect, useRef } from 'react'
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  LogarithmicScale,
} from 'chart.js'
import { Scatter } from 'react-chartjs-2'

ChartJS.register(
  CategoryScale,
  LinearScale,
  LogarithmicScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend
)

interface ComplexityChartProps {
  data: Array<{
    input_size: number
    execution_time_ns: number
    clean_time_ns?: number
    hardware_type?: string
  }>
  analysis?: {
    lambda_value: number
    p_exponent: number
    r_squared: number
  }
  title?: string
}

export default function ComplexityChart({ data, analysis, title = "Análise de Complexidade" }: ComplexityChartProps) {
  const chartRef = useRef<ChartJS<'scatter'>>(null)

  // Prepare data for log-log plot
  const scatterData = data.map(point => ({
    x: Math.log10(point.input_size),
    y: Math.log10(point.clean_time_ns || point.execution_time_ns)
  }))

  // Generate regression line if analysis is available
  const regressionData = analysis ? (() => {
    const minX = Math.min(...scatterData.map(p => p.x))
    const maxX = Math.max(...scatterData.map(p => p.x))
    const logLambda = Math.log10(analysis.lambda_value)
    
    return [
      { x: minX, y: logLambda + analysis.p_exponent * minX },
      { x: maxX, y: logLambda + analysis.p_exponent * maxX }
    ]
  })() : []

  const chartData = {
    datasets: [
      {
        label: 'Dados Observados',
        data: scatterData,
        backgroundColor: 'rgba(59, 130, 246, 0.6)',
        borderColor: 'rgba(59, 130, 246, 1)',
        pointRadius: 6,
        pointHoverRadius: 8,
        showLine: false,
      },
      ...(analysis ? [{
        label: `Regressão (p=${analysis.p_exponent.toFixed(3)}, R²=${(analysis.r_squared * 100).toFixed(1)}%)`,
        data: regressionData,
        backgroundColor: 'rgba(239, 68, 68, 0.1)',
        borderColor: 'rgba(239, 68, 68, 1)',
        borderWidth: 2,
        pointRadius: 0,
        showLine: true,
        tension: 0,
      }] : [])
    ]
  }

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top' as const,
      },
      title: {
        display: true,
        text: title,
        font: {
          size: 16,
          weight: 'bold' as const
        }
      },
      tooltip: {
        callbacks: {
          title: function(context: any) {
            return `Ponto ${context[0].dataIndex + 1}`
          },
          label: function(context: any) {
            const originalData = data[context.dataIndex]
            if (originalData) {
              return [
                `Tamanho da entrada: ${originalData.input_size.toLocaleString()}`,
                `Tempo de execução: ${(originalData.clean_time_ns || originalData.execution_time_ns / 1000000).toFixed(2)} ms`,
                `log₁₀(n): ${context.parsed.x.toFixed(3)}`,
                `log₁₀(t): ${context.parsed.y.toFixed(3)}`
              ]
            }
            return `log₁₀(n): ${context.parsed.x.toFixed(3)}, log₁₀(t): ${context.parsed.y.toFixed(3)}`
          }
        }
      }
    },
    scales: {
      x: {
        type: 'linear' as const,
        display: true,
        title: {
          display: true,
          text: 'log₁₀(Tamanho da Entrada)',
          font: {
            size: 14,
            weight: 'bold' as const
          }
        },
        grid: {
          color: 'rgba(0, 0, 0, 0.1)',
        }
      },
      y: {
        type: 'linear' as const,
        display: true,
        title: {
          display: true,
          text: 'log₁₀(Tempo de Execução)',
          font: {
            size: 14,
            weight: 'bold' as const
          }
        },
        grid: {
          color: 'rgba(0, 0, 0, 0.1)',
        }
      }
    },
    interaction: {
      intersect: false,
      mode: 'point' as const,
    },
  }

  return (
    <div className="w-full h-96 p-4 bg-white rounded-lg border border-gray-200">
      <Scatter ref={chartRef} data={chartData} options={options} />
      
      {analysis && (
        <div className="mt-4 p-4 bg-gray-50 rounded-lg">
          <h4 className="font-medium text-gray-900 mb-2">Equação da Regressão</h4>
          <div className="text-sm text-gray-700 space-y-1">
            <div>
              <strong>T(n) = λ × n^p</strong>
            </div>
            <div>
              λ (constante de hardware) = {analysis.lambda_value.toExponential(3)} ns
            </div>
            <div>
              p (expoente algorítmico) = {analysis.p_exponent.toFixed(3)}
            </div>
            <div>
              R² (qualidade do ajuste) = {(analysis.r_squared * 100).toFixed(1)}%
            </div>
          </div>
          
          <div className="mt-3 text-xs text-gray-600">
            <p>
              <strong>Interpretação:</strong> O algoritmo tem complexidade aproximada de O(n^{analysis.p_exponent.toFixed(2)}). 
              {analysis.p_exponent <= 1.2 && " Excelente eficiência - complexidade linear ou quase-linear."}
              {analysis.p_exponent > 1.2 && analysis.p_exponent <= 2.2 && " Boa eficiência - complexidade quadrática ou sub-quadrática."}
              {analysis.p_exponent > 2.2 && " Atenção - complexidade polinomial alta pode não escalar bem."}
            </p>
          </div>
        </div>
      )}
    </div>
  )
}
