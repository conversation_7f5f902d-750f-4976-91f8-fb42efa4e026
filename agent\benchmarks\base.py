"""
Base benchmark class for all Exo Piper benchmarks
"""

import time
import psutil
from abc import ABC, abstractmethod
from typing import Dict, Any, List


class BaseBenchmark(ABC):
    """Base class for all benchmarks"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logs = []
        self.start_time = None
        self.end_time = None
    
    def log(self, message: str):
        """Add a log message"""
        timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"[{timestamp}] {message}"
        self.logs.append(log_entry)
        print(log_entry)
    
    def get_logs(self) -> List[str]:
        """Get all log messages"""
        return self.logs
    
    def measure_execution(self, func, *args, **kwargs) -> Dict[str, Any]:
        """Measure execution time and resource usage of a function"""
        
        # Get initial resource usage
        process = psutil.Process()
        initial_memory = process.memory_info().rss
        
        # Measure execution time
        start_time = time.perf_counter_ns()
        result = func(*args, **kwargs)
        end_time = time.perf_counter_ns()
        
        # Get final resource usage
        final_memory = process.memory_info().rss
        cpu_percent = process.cpu_percent()
        
        execution_time_ns = end_time - start_time
        memory_delta_mb = (final_memory - initial_memory) / (1024 * 1024)
        
        return {
            "result": result,
            "execution_time_ns": execution_time_ns,
            "memory_delta_mb": memory_delta_mb,
            "cpu_percent": cpu_percent,
            "start_time_ns": start_time,
            "end_time_ns": end_time,
        }
    
    @abstractmethod
    def run(self) -> List[Dict[str, Any]]:
        """Run the benchmark and return results"""
        pass
    
    @abstractmethod
    def get_algorithm_name(self) -> str:
        """Get the name of the algorithm being benchmarked"""
        pass
    
    def validate_config(self, required_keys: List[str]) -> None:
        """Validate that required configuration keys are present"""
        missing_keys = [key for key in required_keys if key not in self.config]
        if missing_keys:
            raise ValueError(f"Missing required configuration keys: {missing_keys}")
    
    def get_input_sizes(self) -> List[int]:
        """Generate input sizes for benchmarking"""
        max_size = self.config.get('max_size', 1000)
        step_factor = self.config.get('step_factor', 2)
        min_size = self.config.get('min_size', 10)
        
        sizes = []
        current_size = min_size
        
        while current_size <= max_size:
            sizes.append(current_size)
            current_size = int(current_size * step_factor)
        
        return sizes
    
    def get_iterations(self) -> int:
        """Get number of iterations per input size"""
        return self.config.get('iterations', 3)
    
    def run_multiple_iterations(self, benchmark_func, input_size: int) -> Dict[str, Any]:
        """Run benchmark function multiple times and return statistics"""
        iterations = self.get_iterations()
        times = []
        memory_usage = []
        
        self.log(f"Running {iterations} iterations for input size {input_size}")
        
        for i in range(iterations):
            measurement = self.measure_execution(benchmark_func, input_size)
            times.append(measurement['execution_time_ns'])
            memory_usage.append(measurement.get('memory_delta_mb', 0))
        
        # Calculate statistics
        avg_time = sum(times) / len(times)
        min_time = min(times)
        max_time = max(times)
        avg_memory = sum(memory_usage) / len(memory_usage)
        
        return {
            "input_size": input_size,
            "execution_time_ns": int(avg_time),
            "min_time_ns": int(min_time),
            "max_time_ns": int(max_time),
            "memory_usage_mb": avg_memory,
            "iterations": iterations,
            "hardware_type": "CPU",
            "algorithm": self.get_algorithm_name(),
            "all_times_ns": times,
        }
