import { chromium, FullConfig } from '@playwright/test'

async function globalSetup(config: FullConfig) {
  console.log('🚀 Starting global test setup...')
  
  const browser = await chromium.launch()
  const page = await browser.newPage()
  
  try {
    // Wait for backend to be ready
    console.log('⏳ Waiting for backend to be ready...')
    await page.goto('http://localhost:8000/health', { timeout: 60000 })
    
    // Wait for frontend to be ready
    console.log('⏳ Waiting for frontend to be ready...')
    await page.goto('http://localhost:3000', { timeout: 60000 })
    
    // Create test user if it doesn't exist
    console.log('👤 Setting up test user...')
    await setupTestUser(page)
    
    console.log('✅ Global setup completed successfully')
    
  } catch (error) {
    console.error('❌ Global setup failed:', error)
    throw error
  } finally {
    await browser.close()
  }
}

async function setupTestUser(page: any) {
  try {
    // Try to register test user
    const response = await page.request.post('http://localhost:8000/api/v1/auth/register', {
      data: {
        username: 'testuser',
        email: '<EMAIL>',
        password: 'testpassword'
      }
    })
    
    if (response.ok()) {
      console.log('✅ Test user created successfully')
    } else {
      // User might already exist, try to login
      const loginResponse = await page.request.post('http://localhost:8000/api/v1/auth/login', {
        data: {
          email: '<EMAIL>',
          password: 'testpassword'
        }
      })
      
      if (loginResponse.ok()) {
        console.log('✅ Test user already exists and is accessible')
      } else {
        console.warn('⚠️ Could not create or access test user')
      }
    }
    
    // Create some test workloads
    await setupTestWorkloads(page)
    
  } catch (error) {
    console.warn('⚠️ Test user setup failed:', error)
  }
}

async function setupTestWorkloads(page: any) {
  try {
    // Login to get token
    const loginResponse = await page.request.post('http://localhost:8000/api/v1/auth/login', {
      data: {
        email: '<EMAIL>',
        password: 'testpassword'
      }
    })
    
    if (!loginResponse.ok()) {
      console.warn('⚠️ Could not login to create test workloads')
      return
    }
    
    const loginData = await loginResponse.json()
    const token = loginData.access_token
    
    // Create test workloads
    const testWorkloads = [
      {
        name: 'Bubble Sort Test',
        description: 'Test implementation of bubble sort',
        code: `def bubble_sort(arr):
    n = len(arr)
    for i in range(n):
        for j in range(0, n-i-1):
            if arr[j] > arr[j+1]:
                arr[j], arr[j+1] = arr[j+1], arr[j]
    return arr`,
        language: 'python',
        complexity_target: 'O(n^2)'
      },
      {
        name: 'Linear Search Test',
        description: 'Test implementation of linear search',
        code: `def linear_search(arr, target):
    for i, value in enumerate(arr):
        if value == target:
            return i
    return -1`,
        language: 'python',
        complexity_target: 'O(n)'
      }
    ]
    
    for (const workload of testWorkloads) {
      try {
        const response = await page.request.post('http://localhost:8000/api/v1/workloads/', {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          },
          data: workload
        })
        
        if (response.ok()) {
          console.log(`✅ Created test workload: ${workload.name}`)
        }
      } catch (error) {
        console.warn(`⚠️ Could not create workload ${workload.name}:`, error)
      }
    }
    
  } catch (error) {
    console.warn('⚠️ Test workloads setup failed:', error)
  }
}

export default globalSetup
