<div align="center">
  <img src="logo.png" alt="Exo Piper Logo" width="250" />
  
  # ✨ Exo Piper
  ## Performance Auditor SaaS

  > *Transformando a auditoria de performance em uma solução self-service, escalável e baseada em ciência*
  
  <p>Micro-SaaS baseado no Teorema da Relatividade da Complexidade para auditoria automatizada de performance.</p>

  <div>
    <img src="https://img.shields.io/badge/Status-100%25%20Completo-brightgreen?style=for-the-badge" alt="Status: 100% Completo" />
    <img src="https://img.shields.io/badge/Licença-MIT-blue?style=for-the-badge" alt="Licença: MIT" />
    <img src="https://img.shields.io/badge/Python-3.11+-green?style=for-the-badge&logo=python" alt="Python: 3.11+" />
    <img src="https://img.shields.io/badge/Node.js-18+-brightgreen?style=for-the-badge&logo=node.js" alt="Node.js: 18+" />
    <img src="https://img.shields.io/badge/Docker-Ready-blue?style=for-the-badge&logo=docker" alt="Docker Ready" />
    <img src="https://img.shields.io/badge/Payments-USDT%20%7C%20Bitcoin-orange?style=for-the-badge&logo=bitcoin" alt="Payments: USDT | Bitcoin" />
  </div>
</div>

---

<p align="center">
  <a href="#-visão-geral">Visão Geral</a> •
  <a href="#%EF%B8%8F-fluxo-principal">Fluxo Principal</a> •
  <a href="#-stack-tecnológico">Stack</a> •
  <a href="#-planos-de-preços">Preços</a> •
  <a href="#-instalação-rápida">Instalação</a> •
  <a href="#-desenvolvimento">Desenvolvimento</a> •
  <a href="#-documentação-completa">Documentação</a>
</p>

---

## 🔍 Visão Geral

<div align="center">
  <img src="https://via.placeholder.com/800x400?text=Exo+Piper+Dashboard" alt="Exo Piper Dashboard" width="80%" />
</div>

O **Exo Piper** transforma auditorias pontuais de performance em um produto self-service, recorrente e escalável. O sistema aplica o protocolo do Teorema da Relatividade da Complexidade para separar efeitos de hardware (constantes) dos efeitos algorítmicos (expoentes).

## ⚙️ Fluxo Principal

<div align="center">
  <table>
    <tr>
      <td align="center" width="20%"><h3>🐳</h3><b>Agent Docker + CLI</b></td>
      <td align="center" width="20%"><h3>📊</h3><b>Coleta λ e p</b></td>
      <td align="center" width="20%"><h3>🚀</h3><b>FastAPI/Celery</b></td>
      <td align="center" width="20%"><h3>📈</h3><b>Dashboard React</b></td>
      <td align="center" width="20%"><h3>🔔</h3><b>Alertas Automáticos</b></td>
    </tr>
    <tr>
      <td>Executa benchmarks (mergesort, 3-SAT, ML inference, etc.)</td>
      <td>Usando TorchScript, subtração de overhead e regressão log-log</td>
      <td>Orquestra jobs, normaliza tempos e valida invariância</td>
      <td>Exibe curvas log-log comparando CPU vs GPU/FPGA</td>
      <td>Disparam quando Δp > 0.05 ou outras métricas críticas</td>
    </tr>
  </table>
</div>

## 💻 Stack Tecnológico

<div align="center">
  <table>
    <tr>
      <td align="center" width="33%"><h3>🔄 Backend</h3></td>
      <td align="center" width="33%"><h3>🖥️ Frontend</h3></td>
      <td align="center" width="33%"><h3>🏗️ Infraestrutura</h3></td>
    </tr>
    <tr>
      <td align="center">
        <img src="https://img.shields.io/badge/FastAPI-009688?style=for-the-badge&logo=fastapi&logoColor=white" alt="FastAPI" /><br/>
        <img src="https://img.shields.io/badge/Celery-37814A?style=for-the-badge&logo=celery&logoColor=white" alt="Celery" /><br/>
        <img src="https://img.shields.io/badge/Redis-DC382D?style=for-the-badge&logo=redis&logoColor=white" alt="Redis" /><br/>
        <img src="https://img.shields.io/badge/PostgreSQL-4169E1?style=for-the-badge&logo=postgresql&logoColor=white" alt="PostgreSQL" />
      </td>
      <td align="center">
        <img src="https://img.shields.io/badge/Next.js-000000?style=for-the-badge&logo=nextdotjs&logoColor=white" alt="Next.js" /><br/>
        <img src="https://img.shields.io/badge/React-61DAFB?style=for-the-badge&logo=react&logoColor=black" alt="React" /><br/>
        <img src="https://img.shields.io/badge/Chart.js-FF6384?style=for-the-badge&logo=chart.js&logoColor=white" alt="Chart.js" />
      </td>
      <td align="center">
        <img src="https://img.shields.io/badge/Docker-2496ED?style=for-the-badge&logo=docker&logoColor=white" alt="Docker" /><br/>
        <img src="https://img.shields.io/badge/MinIO-C72E49?style=for-the-badge&logo=minio&logoColor=white" alt="MinIO" /><br/>
        <img src="https://img.shields.io/badge/Python_CLI-3776AB?style=for-the-badge&logo=python&logoColor=white" alt="Python CLI" /><br/>
        <img src="https://img.shields.io/badge/Agent_Containerizado-2496ED?style=for-the-badge&logo=docker&logoColor=white" alt="Agent Containerizado" />
      </td>
    </tr>
  </table>
</div>

## 💰 Planos de Preços

<div align="center">
  <table>
    <tr>
      <th width="25%" style="background-color:#f8f9fa; color:#202124; padding:15px; border-radius:10px 10px 0 0;">
        <h3>🆓 Free</h3>
      </th>
      <th width="25%" style="background-color:#e8f0fe; color:#1a73e8; padding:15px; border-radius:10px 10px 0 0;">
        <h3>🔹 Basic</h3>
      </th>
      <th width="25%" style="background-color:#e6f4ea; color:#137333; padding:15px; border-radius:10px 10px 0 0;">
        <h3>🔷 Pro</h3>
      </th>
      <th width="25%" style="background-color:#fce8e6; color:#c5221f; padding:15px; border-radius:10px 10px 0 0;">
        <h3>💎 Enterprise</h3>
      </th>
    </tr>
    <tr>
      <td align="center" valign="top" style="padding:20px;">
        <h4>Grátis</h4>
        <ul style="text-align:left; list-style-type:none; padding-left:0;">
          <li>✅ 3 workloads</li>
          <li>✅ 50 jobs/mês</li>
          <li>✅ Retenção 30 dias</li>
        </ul>
      </td>
      <td align="center" valign="top" style="padding:20px;">
        <h4>$50/mês</h4>
        <p><i>50 USDT</i></p>
        <ul style="text-align:left; list-style-type:none; padding-left:0;">
          <li>✅ 10 workloads</li>
          <li>✅ 200 jobs/mês</li>
          <li>✅ Retenção 90 dias</li>
          <li>✅ Alertas por email</li>
        </ul>
      </td>
      <td align="center" valign="top" style="padding:20px;">
        <h4>$200/mês</h4>
        <p><i>200 USDT</i></p>
        <ul style="text-align:left; list-style-type:none; padding-left:0;">
          <li>✅ 50 workloads</li>
          <li>✅ 1000 jobs/mês</li>
          <li>✅ Retenção 1 ano</li>
          <li>✅ Webhooks</li>
          <li>✅ Suporte prioritário</li>
        </ul>
      </td>
      <td align="center" valign="top" style="padding:20px;">
        <h4>$1000/mês</h4>
        <p><i>1000 USDT</i></p>
        <ul style="text-align:left; list-style-type:none; padding-left:0;">
          <li>✅ Workloads ilimitados</li>
          <li>✅ Jobs ilimitados</li>
          <li>✅ Retenção ilimitada</li>
          <li>✅ SLA 24x7</li>
          <li>✅ SSO + VPC</li>
          <li>✅ White-label</li>
        </ul>
      </td>
    </tr>
  </table>
</div>

### 💳 Métodos de Pagamento

<div align="center">
  <table>
    <tr>
      <td align="center" width="33%">
        <img src="https://img.shields.io/badge/USDT_BEP20-F0B90B?style=for-the-badge&logo=binance&logoColor=white" alt="USDT BEP20" />
        <p>Binance Smart Chain</p>
        <p><small>Taxas baixas (~$0.20)</small></p>
      </td>
      <td align="center" width="33%">
        <img src="https://img.shields.io/badge/USDT_ERC20-627EEA?style=for-the-badge&logo=ethereum&logoColor=white" alt="USDT ERC20" />
        <p>Ethereum</p>
        <p><small>Mais seguro (~$5-20)</small></p>
      </td>
      <td align="center" width="33%">
        <img src="https://img.shields.io/badge/Bitcoin_via_TANOS-F7931A?style=for-the-badge&logo=bitcoin&logoColor=white" alt="Bitcoin via TANOS" />
        <p>Swaps atômicos com Nostr</p>
      </td>
    </tr>
  </table>

  <br/>
  <p><b>Wallet:</b> <code>******************************************</code></p>
</div>

## 🚀 Instalação Rápida

<div align="center">
  <img src="https://via.placeholder.com/800x250?text=Instalacao+ExoPiper" alt="Instalação ExoPiper" width="80%" />
</div>

### Teste Automático (Recomendado)

<table>
  <tr>
    <td width="50%">
      <b>Linux/macOS:</b>
      <pre><code>chmod +x test-system.sh
./test-system.sh</code></pre>
    </td>
    <td width="50%">
      <b>Windows:</b>
      <pre><code>.\test-system.ps1</code></pre>
    </td>
  </tr>
</table>

### Instalação Manual

```bash
# 1. Construir Agent Docker
chmod +x build-agent.sh && ./build-agent.sh

# 2. Subir infraestrutura
docker-compose up -d

# 3. Instalar CLI
cd cli && pip install -e .

# 4. Usar CLI
perf-audit login --api-key <token>
perf-audit init
perf-audit run --workloads mergesort,3sat,mlmodel
```

## 📁 Estrutura do Projeto

<div align="center">
  <table>
    <tr>
      <td>
        <pre>
exo-Piper/
├── 📂 backend/          # FastAPI + Celery
├── 📂 frontend/         # Next.js Dashboard
├── 📂 cli/              # CLI Python
├── 📂 agent/            # Docker Agent
├── 📂 benchmarks/       # Scripts de benchmark
├── 📂 docker/           # Configurações Docker
└── 📂 docs/             # Documentação
        </pre>
      </td>
    </tr>
  </table>
</div>

## 🔧 Setup Detalhado

### Pré-requisitos

<div align="center">
  <table>
    <tr>
      <td align="center" width="33%">
        <img src="https://img.shields.io/badge/Docker-2496ED?style=for-the-badge&logo=docker&logoColor=white" alt="Docker" />
        <p>Docker e Docker Compose</p>
      </td>
      <td align="center" width="33%">
        <img src="https://img.shields.io/badge/Node.js-339933?style=for-the-badge&logo=nodedotjs&logoColor=white" alt="Node.js" />
        <p>Node.js 18+</p>
      </td>
      <td align="center" width="33%">
        <img src="https://img.shields.io/badge/Python-3776AB?style=for-the-badge&logo=python&logoColor=white" alt="Python" />
        <p>Python 3.11+</p>
      </td>
    </tr>
  </table>
</div>

### Instalação Automática

<table>
  <tr>
    <td width="50%">
      <b>Linux/macOS:</b>
      <pre><code>chmod +x setup.sh
./setup.sh</code></pre>
    </td>
    <td width="50%">
      <b>Windows (PowerShell):</b>
      <pre><code>Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
.\setup.ps1</code></pre>
    </td>
  </tr>
</table>

### Instalação Manual

<details>
<summary><b>🔽 Clique para expandir instruções detalhadas</b></summary>

1. **Clone e configure:**
```bash
git clone <repository>
cd exo-Piper
cp .env.example .env
# Edite .env com suas configurações
```

2. **Inicie os serviços:**
```bash
docker-compose up -d
```

3. **Configure o backend:**
```bash
cd backend
python -m venv venv
source venv/bin/activate  # Linux/macOS
# ou venv\Scripts\Activate.ps1  # Windows
pip install -r requirements.txt
uvicorn main:app --reload
```

4. **Configure o frontend:**
```bash
cd frontend
npm install
npm run dev
```

5. **Configure o CLI:**
```bash
cd cli
pip install -e .
perf-audit --help
```

6. **Inicie o Celery worker:**
```bash
cd backend
source venv/bin/activate
celery -A app.core.celery_app worker --loglevel=info
```
</details>

## 🛠️ Desenvolvimento

### Serviços Disponíveis

<div align="center">
  <table>
    <tr>
      <td align="center"><b>Frontend</b></td>
      <td align="center"><a href="http://localhost:3000" target="_blank">http://localhost:3000</a></td>
    </tr>
    <tr>
      <td align="center"><b>Backend API</b></td>
      <td align="center"><a href="http://localhost:8000" target="_blank">http://localhost:8000</a></td>
    </tr>
    <tr>
      <td align="center"><b>API Docs</b></td>
      <td align="center"><a href="http://localhost:8000/docs" target="_blank">http://localhost:8000/docs</a></td>
    </tr>
    <tr>
      <td align="center"><b>MinIO Console</b></td>
      <td align="center"><a href="http://localhost:9001" target="_blank">http://localhost:9001</a> (admin/password)</td>
    </tr>
  </table>
</div>

### Comandos Úteis

<details>
<summary><b>🔽 Clique para expandir comandos úteis</b></summary>

```bash
# Parar todos os serviços
docker-compose down

# Ver logs
docker-compose logs -f

# Resetar banco de dados
docker-compose down -v
docker-compose up -d postgres

# Testar CLI
perf-audit login --api-key <your-key>
perf-audit init
perf-audit run --workloads mergesort
```
</details>

## 📚 Documentação Completa

<div align="center">
  <table>
    <tr>
      <td align="center" width="33%">
        <h3>📖 Guias</h3>
        <p><a href="USER_GUIDE.md">Guia do Usuário</a></p>
        <p><a href="API_DOCUMENTATION.md">Documentação da API REST</a></p>
        <p><a href="PAYMENT_SYSTEM.md">Sistema de Pagamento</a></p>
      </td>
      <td align="center" width="33%">
        <h3>🔧 Scripts</h3>
        <p><a href="deploy-production.sh">Deploy em Produção</a></p>
        <p><a href="backup-system.sh">Sistema de Backup</a></p>
        <p><a href="optimize-system.sh">Otimização de Performance</a></p>
      </td>
      <td align="center" width="33%">
        <h3>📋 Status</h3>
        <p><a href="CHECKLIST.md">Checklist de Desenvolvimento</a></p>
        <p><img src="https://img.shields.io/badge/Status-100%25%20Completo-brightgreen?style=for-the-badge" alt="Status: 100% Completo" /></p>
      </td>
    </tr>
  </table>
</div>

## 🧪 Scripts Disponíveis

<div align="center">
  <table>
    <tr>
      <th>Testes e Validação</th>
      <th>Deployment e Produção</th>
      <th>Desenvolvimento</th>
    </tr>
    <tr>
      <td>
        <ul>
          <li><code>./run-all-tests.sh</code> - Testa todo o sistema</li>
          <li><code>./test-payment-system.py</code> - Testa pagamentos USDT</li>
        </ul>
      </td>
      <td>
        <ul>
          <li><code>./deploy-production.sh</code> - Deploy completo</li>
          <li><code>./backup-system.sh</code> - Backup do sistema</li>
          <li><code>./optimize-system.sh</code> - Otimização de performance</li>
        </ul>
      </td>
      <td>
        <ul>
          <li><code>./build-agent.sh</code> - Build do Docker agent</li>
          <li><code>./setup-permissions.sh</code> - Configura permissões</li>
        </ul>
      </td>
    </tr>
  </table>
</div>

## 🎯 Status do Projeto: 100% COMPLETO

<div align="center">
  <img src="https://via.placeholder.com/800x250?text=Projeto+100%25+Completo" alt="Projeto 100% Completo" width="80%" />
</div>

<details>
<summary><b>🔽 Funcionalidades Implementadas (Clique para expandir)</b></summary>

✅ **Todas as Funcionalidades Implementadas:**
- Sistema de autenticação JWT completo
- CRUD de workloads com validação
- Execução de benchmarks em Docker
- Análise de complexidade (Teorema da Relatividade)
- Sistema de alertas e notificações inteligentes
- Retenção de dados por plano
- **Pagamentos USDT** (BEP20/ERC20) + **Bitcoin via TANOS**
- QR codes para pagamentos
- Webhooks com retry automático
- Analytics completos de pagamentos
- Interface admin avançada
- CLI funcional com scheduling
- Testes unitários, integração e E2E
- Testes de carga (K6)
- Pipeline CI/CD completo
- Scripts de deployment e backup
- CDN e load balancing
- Kubernetes manifests
- Documentação completa para usuários e desenvolvedores
- Scripts de vídeos tutoriais
</details>

<div align="center">
  <h3>🎉 Sistema 100% Pronto para Produção!</h3>
</div>

---

<div align="center">
  <p>
    <img src="https://img.shields.io/badge/Licença-MIT-blue?style=for-the-badge" alt="Licença: MIT" />
    <br>
    MIT License - veja <a href="LICENSE">LICENSE</a> para detalhes.
  </p>
  
  <p>© 2023 Exo Piper. Todos os direitos reservados.</p>
</div>
