# 🎬 Vídeo Tutorial 2: Primeiros Passos no Exo Piper

**Duração:** 8-10 minutos  
**Público:** Novos usuários  
**Objetivo:** Guiar o usuário através do registro, primeiro workload e benchmark

## 📝 **Roteiro do Vídeo**

### **Abertura (30 segundos)**
- **Visual:** Intro animada + título do episódio
- **Narração:** 
  > "Olá! No vídeo anterior, conhecemos o Exo Piper. Hoje vamos colocar a mão na massa e criar nossa primeira análise de performance. Vou mostrar passo a passo como se registrar, criar um workload e executar seu primeiro benchmark."

### **C<PERSON><PERSON> sua <PERSON>ta (2 minutos)**

#### **Acessando o Site**
- **Visual:** Navegador abrindo exopiper.com
- **Narração:**
  > "Primeiro, acesse exopiper.com. Você verá nossa página inicial com informações sobre o sistema."

#### **Processo de Registro**
- **Visual:** Clicando em "Registrar" + preenchendo formulário
- **Narração:**
  > "Clique em 'Registrar' no canto superior direito. Preencha seus dados: nome de usuário único, email válido e uma senha segura com pelo menos 8 caracteres."

- **Mostrar na tela:**
  - Formulário de registro
  - Validações em tempo real
  - Mensagem de confirmação

#### **Confirmação de Email**
- **Visual:** Caixa de email + clique no link
- **Narração:**
  > "Verifique seu email e clique no link de confirmação. Isso garante a segurança da sua conta."

#### **Primeiro Login**
- **Visual:** Tela de login + dashboard inicial
- **Narração:**
  > "Agora faça login com suas credenciais. Você será direcionado para o dashboard principal."

### **Explorando o Dashboard (1.5 minutos)**
- **Visual:** Tour pelo dashboard
- **Narração:**
  > "Este é seu dashboard. Aqui você vê estatísticas dos seus workloads, jobs recentes e informações do seu plano atual. Como usuário gratuito, você tem 3 workloads e 50 jobs por mês."

- **Destacar:**
  - Estatísticas de uso
  - Workloads ativos
  - Plano atual e limites
  - Menu de navegação

### **Criando seu Primeiro Workload (3 minutos)**

#### **Acessando a Página de Workloads**
- **Visual:** Clicando em "Workloads" no menu
- **Narração:**
  > "Vamos criar nosso primeiro workload. Clique em 'Workloads' no menu lateral."

#### **Novo Workload**
- **Visual:** Clicando em "Novo Workload"
- **Narração:**
  > "Clique em 'Novo Workload' para começar."

#### **Preenchendo as Informações**
- **Visual:** Formulário de criação sendo preenchido
- **Narração:**
  > "Vamos criar um algoritmo simples de busca linear. Preencha o nome: 'Minha Busca Linear', descrição: 'Implementação básica de busca linear', e selecione Python como linguagem."

#### **Escrevendo o Código**
- **Visual:** Editor de código + digitação
- **Narração:**
  > "Agora vamos escrever o código. No editor, digite a função de busca linear:"

```python
def busca_linear(lista, item):
    for i, valor in enumerate(lista):
        if valor == item:
            return i
    return -1
```

#### **Configurações Avançadas**
- **Visual:** Seção de configurações
- **Narração:**
  > "Defina a complexidade esperada como O(n) - linear. Isso ajuda o sistema a validar a análise."

#### **Salvando o Workload**
- **Visual:** Clicando em "Criar Workload"
- **Narração:**
  > "Clique em 'Criar Workload'. Pronto! Seu primeiro workload foi criado."

### **Executando seu Primeiro Benchmark (2.5 minutos)**

#### **Acessando o Workload**
- **Visual:** Lista de workloads + clique no workload criado
- **Narração:**
  > "Agora vamos executar um benchmark. Clique no workload que acabamos de criar."

#### **Configurando o Benchmark**
- **Visual:** Página do workload + botão "Executar Benchmark"
- **Narração:**
  > "Clique em 'Executar Benchmark'. Aqui configuramos os parâmetros do teste."

- **Mostrar:**
  - Tamanhos de entrada: 100, 500, 1000, 5000
  - Iterações: 3
  - Timeout: 60 segundos

#### **Iniciando a Execução**
- **Visual:** Clicando em "Iniciar Benchmark"
- **Narração:**
  > "Com as configurações definidas, clique em 'Iniciar Benchmark'. O sistema criará um container Docker isolado para executar seu código com segurança."

#### **Acompanhando o Progresso**
- **Visual:** Página de status + barra de progresso
- **Narração:**
  > "Você pode acompanhar o progresso em tempo real. O sistema testa diferentes tamanhos de entrada e mede o tempo de execução."

### **Analisando os Resultados (1.5 minutos)**

#### **Resultados Completos**
- **Visual:** Página de resultados com gráficos
- **Narração:**
  > "Excelente! O benchmark foi concluído. Vamos analisar os resultados."

#### **Análise de Complexidade**
- **Visual:** Gráfico de complexidade + métricas
- **Narração:**
  > "O sistema determinou que a complexidade é O(n) com 95% de confiança, confirmando nossa expectativa. O valor lambda de aproximadamente 1 indica crescimento linear."

#### **Métricas de Performance**
- **Visual:** Tabela de métricas + gráfico de tempo
- **Narração:**
  > "Aqui vemos os tempos de execução para cada tamanho de entrada. Note como o tempo cresce linearmente com o tamanho da lista."

#### **Gráficos Interativos**
- **Visual:** Interagindo com os gráficos
- **Narração:**
  > "Os gráficos são interativos. Você pode fazer zoom, filtrar dados e comparar diferentes execuções."

### **Próximos Passos (30 segundos)**
- **Visual:** Menu de navegação + outras funcionalidades
- **Narração:**
  > "Parabéns! Você executou seu primeiro benchmark no Exo Piper. No próximo vídeo, vamos explorar funcionalidades avançadas como comparação de algoritmos e sistema de alertas."

### **Encerramento (30 segundos)**
- **Visual:** Call-to-action + links
- **Narração:**
  > "Gostou do tutorial? Deixe seu like, se inscreva no canal e experimente criar seus próprios workloads. Até a próxima!"

## 🎯 **Demonstrações Práticas**

### **Código de Exemplo Completo:**
```python
def busca_linear(lista, item):
    """
    Busca linear simples
    Complexidade: O(n)
    """
    for i, valor in enumerate(lista):
        if valor == item:
            return i
    return -1

# Função de teste para o benchmark
def main(n):
    import random
    lista = list(range(n))
    item = random.choice(lista)
    return busca_linear(lista, item)
```

### **Configurações de Benchmark:**
- **Tamanhos de entrada:** [100, 500, 1000, 5000, 10000]
- **Iterações:** 3
- **Timeout:** 60 segundos
- **Complexidade esperada:** O(n)

## 📊 **Resultados Esperados**

- **Lambda (λ):** ~1.0 (indicando O(n))
- **P-value:** < 0.05 (confiança estatística)
- **R²:** > 0.95 (boa correlação)
- **Tempo médio:** Crescimento linear

## 🎨 **Elementos Visuais Específicos**

1. **Capturas de Tela Detalhadas:**
   - Cada passo do processo de registro
   - Formulário de workload preenchido
   - Editor de código com syntax highlighting
   - Configuração de benchmark
   - Resultados com gráficos

2. **Animações:**
   - Transições entre páginas
   - Preenchimento de formulários
   - Execução do benchmark (barra de progresso)
   - Aparição dos gráficos

3. **Overlays Informativos:**
   - Dicas durante o preenchimento
   - Explicações de termos técnicos
   - Destaque de botões importantes

## 🎵 **Audio e Timing**

- **Música:** Instrumental suave, não competitiva com a narração
- **Pausas:** 2-3 segundos entre seções principais
- **Velocidade:** Narração clara, não muito rápida
- **Efeitos:** Sons de clique, notificações de sucesso

## 📱 **Adaptações para Diferentes Plataformas**

### **YouTube (Versão Completa - 8-10 min):**
- Todos os passos detalhados
- Explicações completas
- Múltiplos exemplos

### **Instagram/TikTok (Versão Curta - 60-90 seg):**
- Apenas os passos principais
- Texto overlay para informações
- Ritmo mais acelerado

### **LinkedIn (Versão Profissional - 3-5 min):**
- Foco nos benefícios para desenvolvedores
- Menos detalhes técnicos
- Mais contexto de negócio

## 🎬 **Checklist de Produção**

- [ ] Script finalizado e revisado
- [ ] Capturas de tela em alta resolução
- [ ] Ambiente de teste configurado
- [ ] Áudio gravado e editado
- [ ] Animações criadas
- [ ] Vídeo editado e renderizado
- [ ] Legendas adicionadas
- [ ] Thumbnail criada
- [ ] Descrição e tags definidas
- [ ] Upload e programação feitos

## 📈 **KPIs de Sucesso**

- **Taxa de Conclusão:** >75% assistem até o final
- **Engajamento:** >5% de likes/views
- **Conversão:** >3% se registram após assistir
- **Retenção:** >60% assistem o próximo vídeo da série
