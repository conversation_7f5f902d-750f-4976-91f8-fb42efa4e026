'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import {
  UsersIcon,
  TrashIcon,
  ChartBarIcon,
  ExclamationTriangleIcon,
  ClockIcon,
  CpuChipIcon,
  ArrowPathIcon
} from '@heroicons/react/24/outline'

interface UserStats {
  user_id: number
  plan_type: string
  usage: {
    workloads: { current: number; limit: number }
    jobs: { total: number; this_month: number; monthly_limit: number }
    results: { total: number }
  }
  limits_exceeded: {
    workloads: boolean
    monthly_jobs: boolean
  }
}

interface CleanupStats {
  processed_users: number
  total_deleted_results: number
  total_deleted_jobs: number
  total_deleted_artifacts: number
  errors: string[]
}

export default function AdminPage() {
  const router = useRouter()
  const [userStats, setUserStats] = useState<UserStats[]>([])
  const [summary, setSummary] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isCleanupRunning, setIsCleanupRunning] = useState(false)
  const [cleanupResults, setCleanupResults] = useState<CleanupStats | null>(null)
  const [selectedUser, setSelectedUser] = useState<number | null>(null)
  const [analytics, setAnalytics] = useState<any>(null)
  const [showAnalytics, setShowAnalytics] = useState(false)

  useEffect(() => {
    checkAdminAccess()
    fetchUsageStats()
  }, [])

  const checkAdminAccess = async () => {
    try {
      const token = localStorage.getItem('token')
      const response = await fetch('/api/v1/auth/me', {
        headers: { 'Authorization': `Bearer ${token}` }
      })

      if (response.ok) {
        const user = await response.json()
        if (user.plan_type !== 'enterprise' && !user.is_admin) {
          router.push('/dashboard')
          return
        }
      } else {
        router.push('/login')
      }
    } catch (error) {
      console.error('Error checking admin access:', error)
      router.push('/login')
    }
  }

  const fetchUsageStats = async () => {
    try {
      const token = localStorage.getItem('token')
      const response = await fetch('/api/v1/admin/usage/all', {
        headers: { 'Authorization': `Bearer ${token}` }
      })

      if (response.ok) {
        const data = await response.json()
        setUserStats(data.user_stats || [])
        setSummary(data.summary || {})
      }
    } catch (error) {
      console.error('Error fetching usage stats:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const fetchAnalytics = async () => {
    try {
      const token = localStorage.getItem('token')
      const response = await fetch('/api/v1/admin/analytics/dashboard', {
        headers: { 'Authorization': `Bearer ${token}` }
      })

      if (response.ok) {
        const data = await response.json()
        setAnalytics(data.dashboard)
      }
    } catch (error) {
      console.error('Error fetching analytics:', error)
    }
  }

  const runCleanupAll = async () => {
    if (!confirm('Tem certeza que deseja executar limpeza para todos os usuários?')) return

    setIsCleanupRunning(true)
    setCleanupResults(null)

    try {
      const token = localStorage.getItem('token')
      const response = await fetch('/api/v1/admin/cleanup/all', {
        method: 'POST',
        headers: { 'Authorization': `Bearer ${token}` }
      })

      if (response.ok) {
        const data = await response.json()
        setCleanupResults(data.stats)
        await fetchUsageStats() // Refresh stats
      } else {
        alert('Erro ao executar limpeza')
      }
    } catch (error) {
      console.error('Error running cleanup:', error)
      alert('Erro ao executar limpeza')
    } finally {
      setIsCleanupRunning(false)
    }
  }

  const runCleanupUser = async (userId: number) => {
    if (!confirm(`Tem certeza que deseja executar limpeza para o usuário ${userId}?`)) return

    try {
      const token = localStorage.getItem('token')
      const response = await fetch(`/api/v1/admin/cleanup/user/${userId}`, {
        method: 'POST',
        headers: { 'Authorization': `Bearer ${token}` }
      })

      if (response.ok) {
        const data = await response.json()
        alert(`Limpeza concluída: ${JSON.stringify(data.stats, null, 2)}`)
        await fetchUsageStats() // Refresh stats
      } else {
        alert('Erro ao executar limpeza do usuário')
      }
    } catch (error) {
      console.error('Error running user cleanup:', error)
      alert('Erro ao executar limpeza do usuário')
    }
  }

  const getPlanColor = (plan: string) => {
    switch (plan.toLowerCase()) {
      case 'free': return 'bg-gray-100 text-gray-800'
      case 'basic': return 'bg-blue-100 text-blue-800'
      case 'pro': return 'bg-purple-100 text-purple-800'
      case 'enterprise': return 'bg-gold-100 text-gold-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getUsagePercentage = (current: number, limit: number) => {
    if (limit === -1) return 0 // Unlimited
    return Math.min((current / limit) * 100, 100)
  }

  const getUsageColor = (percentage: number) => {
    if (percentage >= 90) return 'bg-red-500'
    if (percentage >= 75) return 'bg-yellow-500'
    return 'bg-green-500'
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4 lg:py-6">
            <div className="flex items-center">
              <UsersIcon className="h-6 w-6 sm:h-8 sm:w-8 text-primary-600" />
              <h1 className="ml-2 text-lg sm:text-2xl font-bold text-gray-900">Administração</h1>
            </div>
            <button
              onClick={() => router.push('/dashboard')}
              className="btn-secondary"
            >
              Voltar ao Dashboard
            </button>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8">
        {/* Summary Cards */}
        {summary && (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 mb-6 sm:mb-8">
            <div className="card">
              <div className="flex items-center">
                <UsersIcon className="h-6 w-6 sm:h-8 sm:w-8 text-blue-600" />
                <div className="ml-3 sm:ml-4">
                  <p className="text-xs sm:text-sm font-medium text-gray-600">Total Usuários</p>
                  <p className="text-xl sm:text-2xl font-bold text-gray-900">{summary.total_users}</p>
                </div>
              </div>
            </div>

            <div className="card">
              <div className="flex items-center">
                <ExclamationTriangleIcon className="h-6 w-6 sm:h-8 sm:w-8 text-red-600" />
                <div className="ml-3 sm:ml-4">
                  <p className="text-xs sm:text-sm font-medium text-gray-600">Acima dos Limites</p>
                  <p className="text-xl sm:text-2xl font-bold text-gray-900">{summary.users_over_limits}</p>
                </div>
              </div>
            </div>

            <div className="card">
              <div className="flex items-center">
                <ChartBarIcon className="h-6 w-6 sm:h-8 sm:w-8 text-green-600" />
                <div className="ml-3 sm:ml-4">
                  <p className="text-xs sm:text-sm font-medium text-gray-600">Planos</p>
                  <div className="text-xs text-gray-600">
                    {Object.entries(summary.plan_distribution || {}).map(([plan, count]) => (
                      <div key={plan}>{plan}: {count as number}</div>
                    ))}
                  </div>
                </div>
              </div>
            </div>

            <div className="card">
              <div className="flex items-center">
                <ClockIcon className="h-6 w-6 sm:h-8 sm:w-8 text-purple-600" />
                <div className="ml-3 sm:ml-4">
                  <p className="text-xs sm:text-sm font-medium text-gray-600">Última Atualização</p>
                  <p className="text-sm text-gray-900">{new Date().toLocaleTimeString()}</p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Cleanup Section */}
        <div className="card mb-6 sm:mb-8">
          <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-4 space-y-4 sm:space-y-0">
            <h2 className="text-lg font-semibold text-gray-900">Limpeza de Dados</h2>
            <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-4">
              <button
                onClick={runCleanupAll}
                disabled={isCleanupRunning}
                className="btn-primary flex items-center justify-center"
              >
                {isCleanupRunning ? (
                  <ArrowPathIcon className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <TrashIcon className="h-4 w-4 mr-2" />
                )}
                {isCleanupRunning ? 'Executando...' : 'Limpeza Geral'}
              </button>
              <button
                onClick={fetchUsageStats}
                className="btn-secondary flex items-center justify-center"
              >
                <ArrowPathIcon className="h-4 w-4 mr-2" />
                Atualizar
              </button>
              <button
                onClick={() => {
                  setShowAnalytics(!showAnalytics)
                  if (!showAnalytics && !analytics) {
                    fetchAnalytics()
                  }
                }}
                className="btn-primary flex items-center justify-center"
              >
                <ChartBarIcon className="h-4 w-4 mr-2" />
                {showAnalytics ? 'Ocultar' : 'Mostrar'} Analytics
              </button>
            </div>
          </div>

          {cleanupResults && (
            <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-4">
              <h3 className="font-medium text-green-900 mb-2">Resultado da Limpeza</h3>
              <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 text-sm">
                <div>
                  <span className="text-green-700">Usuários:</span>
                  <span className="ml-1 font-medium">{cleanupResults.processed_users}</span>
                </div>
                <div>
                  <span className="text-green-700">Jobs:</span>
                  <span className="ml-1 font-medium">{cleanupResults.total_deleted_jobs}</span>
                </div>
                <div>
                  <span className="text-green-700">Resultados:</span>
                  <span className="ml-1 font-medium">{cleanupResults.total_deleted_results}</span>
                </div>
                <div>
                  <span className="text-green-700">Artefatos:</span>
                  <span className="ml-1 font-medium">{cleanupResults.total_deleted_artifacts}</span>
                </div>
              </div>
              {cleanupResults.errors.length > 0 && (
                <div className="mt-2">
                  <span className="text-red-700 text-sm">Erros: {cleanupResults.errors.length}</span>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Analytics Section */}
        {showAnalytics && analytics && (
          <div className="card mb-6 sm:mb-8">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Analytics Dashboard</h2>

            {/* Payment Analytics */}
            {analytics.payments && (
              <div className="mb-6">
                <h3 className="text-md font-medium text-gray-800 mb-3">Pagamentos (últimos 30 dias)</h3>
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                  <div className="bg-blue-50 p-4 rounded-lg">
                    <div className="text-2xl font-bold text-blue-600">
                      {analytics.payments.overview.total_payments}
                    </div>
                    <div className="text-sm text-blue-800">Total Pagamentos</div>
                  </div>
                  <div className="bg-green-50 p-4 rounded-lg">
                    <div className="text-2xl font-bold text-green-600">
                      {analytics.payments.overview.confirmed_payments}
                    </div>
                    <div className="text-sm text-green-800">Confirmados</div>
                  </div>
                  <div className="bg-purple-50 p-4 rounded-lg">
                    <div className="text-2xl font-bold text-purple-600">
                      {analytics.payments.overview.conversion_rate}%
                    </div>
                    <div className="text-sm text-purple-800">Taxa Conversão</div>
                  </div>
                  <div className="bg-yellow-50 p-4 rounded-lg">
                    <div className="text-2xl font-bold text-yellow-600">
                      ${analytics.payments.overview.total_revenue_usdt}
                    </div>
                    <div className="text-sm text-yellow-800">Receita USDT</div>
                  </div>
                </div>

                {/* Revenue by Network */}
                {analytics.payments.by_network && analytics.payments.by_network.length > 0 && (
                  <div className="mt-4">
                    <h4 className="text-sm font-medium text-gray-700 mb-2">Receita por Rede</h4>
                    <div className="space-y-2">
                      {analytics.payments.by_network.map((network: any, index: number) => (
                        <div key={index} className="flex justify-between items-center bg-gray-50 p-2 rounded">
                          <span className="text-sm font-medium">{network.network.toUpperCase()}</span>
                          <div className="text-right">
                            <div className="text-sm font-bold">${network.revenue}</div>
                            <div className="text-xs text-gray-600">{network.count} pagamentos</div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* User Analytics */}
            {analytics.users && (
              <div className="mb-6">
                <h3 className="text-md font-medium text-gray-800 mb-3">Usuários</h3>
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                  <div className="bg-indigo-50 p-4 rounded-lg">
                    <div className="text-2xl font-bold text-indigo-600">
                      {analytics.users.total_users}
                    </div>
                    <div className="text-sm text-indigo-800">Total Usuários</div>
                  </div>
                  <div className="bg-green-50 p-4 rounded-lg">
                    <div className="text-2xl font-bold text-green-600">
                      {analytics.users.active_users_30d}
                    </div>
                    <div className="text-sm text-green-800">Ativos (30d)</div>
                  </div>
                  <div className="bg-blue-50 p-4 rounded-lg">
                    <div className="text-2xl font-bold text-blue-600">
                      {analytics.users.new_users_30d}
                    </div>
                    <div className="text-sm text-blue-800">Novos (30d)</div>
                  </div>
                  <div className="bg-orange-50 p-4 rounded-lg">
                    <div className="text-2xl font-bold text-orange-600">
                      {analytics.users.activity_rate}%
                    </div>
                    <div className="text-sm text-orange-800">Taxa Atividade</div>
                  </div>
                </div>

                {/* Plan Distribution */}
                {analytics.users.plan_distribution && (
                  <div className="mt-4">
                    <h4 className="text-sm font-medium text-gray-700 mb-2">Distribuição de Planos</h4>
                    <div className="grid grid-cols-2 sm:grid-cols-4 gap-2">
                      {Object.entries(analytics.users.plan_distribution).map(([plan, count]) => (
                        <div key={plan} className="bg-gray-50 p-2 rounded text-center">
                          <div className="text-lg font-bold">{count as number}</div>
                          <div className="text-xs text-gray-600">{plan.toUpperCase()}</div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* TANOS Analytics */}
            {analytics.tanos && (
              <div className="mb-6">
                <h3 className="text-md font-medium text-gray-800 mb-3">TANOS Swaps</h3>
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                  <div className="bg-bitcoin-50 p-4 rounded-lg">
                    <div className="text-2xl font-bold text-orange-600">
                      {analytics.tanos.total_swaps}
                    </div>
                    <div className="text-sm text-orange-800">Total Swaps</div>
                  </div>
                  <div className="bg-green-50 p-4 rounded-lg">
                    <div className="text-2xl font-bold text-green-600">
                      {analytics.tanos.completed_swaps_30d}
                    </div>
                    <div className="text-sm text-green-800">Completos (30d)</div>
                  </div>
                  <div className="bg-purple-50 p-4 rounded-lg">
                    <div className="text-2xl font-bold text-purple-600">
                      {analytics.tanos.success_rate}%
                    </div>
                    <div className="text-sm text-purple-800">Taxa Sucesso</div>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}

        {/* Users Table */}
        <div className="card">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Usuários e Uso</h2>

          {isLoading ? (
            <div className="flex justify-center py-8">
              <ArrowPathIcon className="h-8 w-8 animate-spin text-gray-400" />
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Usuário
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Plano
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Workloads
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Jobs (Mês)
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Ações
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {userStats.map((user) => (
                    <tr key={user.user_id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        #{user.user_id}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`px-2 py-1 text-xs rounded-full ${getPlanColor(user.plan_type)}`}>
                          {user.plan_type.toUpperCase()}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        <div className="flex items-center">
                          <span>{user.usage.workloads.current}</span>
                          {user.usage.workloads.limit !== -1 && (
                            <>
                              <span className="mx-1">/</span>
                              <span>{user.usage.workloads.limit}</span>
                              <div className="ml-2 w-16 bg-gray-200 rounded-full h-2">
                                <div
                                  className={`h-2 rounded-full ${getUsageColor(getUsagePercentage(user.usage.workloads.current, user.usage.workloads.limit))}`}
                                  style={{ width: `${getUsagePercentage(user.usage.workloads.current, user.usage.workloads.limit)}%` }}
                                ></div>
                              </div>
                            </>
                          )}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        <div className="flex items-center">
                          <span>{user.usage.jobs.this_month}</span>
                          {user.usage.jobs.monthly_limit !== -1 && (
                            <>
                              <span className="mx-1">/</span>
                              <span>{user.usage.jobs.monthly_limit}</span>
                              <div className="ml-2 w-16 bg-gray-200 rounded-full h-2">
                                <div
                                  className={`h-2 rounded-full ${getUsageColor(getUsagePercentage(user.usage.jobs.this_month, user.usage.jobs.monthly_limit))}`}
                                  style={{ width: `${getUsagePercentage(user.usage.jobs.this_month, user.usage.jobs.monthly_limit)}%` }}
                                ></div>
                              </div>
                            </>
                          )}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {(user.limits_exceeded.workloads || user.limits_exceeded.monthly_jobs) ? (
                          <span className="px-2 py-1 text-xs rounded-full bg-red-100 text-red-800">
                            Limite Excedido
                          </span>
                        ) : (
                          <span className="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">
                            Normal
                          </span>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <button
                          onClick={() => runCleanupUser(user.user_id)}
                          className="text-red-600 hover:text-red-900"
                          title="Limpar dados do usuário"
                        >
                          <TrashIcon className="h-4 w-4" />
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
