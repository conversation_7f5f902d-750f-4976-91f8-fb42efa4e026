apiVersion: v1
kind: Secret
metadata:
  name: exopiper-secrets
  namespace: exopiper
type: Opaque
data:
  # Base64 encoded secrets
  # To encode: echo -n "your-secret" | base64
  
  # Database password
  POSTGRES_PASSWORD: cGFzc3dvcmQxMjM=  # password123
  DATABASE_URL: ****************************************************************************************
  
  # Application secrets
  SECRET_KEY: c3VwZXItc2VjcmV0LWtleS1mb3ItcHJvZHVjdGlvbi0xMjM0NTY3ODkw  # super-secret-key-for-production-1234567890
  JWT_SECRET: and0LXNlY3JldC1rZXktZm9yLXRva2Vucy0xMjM0NTY3ODkw  # jwt-secret-key-for-tokens-1234567890
  
  # MinIO credentials
  MINIO_ROOT_USER: YWRtaW4=  # admin
  MINIO_ROOT_PASSWORD: cGFzc3dvcmQxMjM=  # password123
  MINIO_ACCESS_KEY: YWRtaW4=  # admin
  MINIO_SECRET_KEY: cGFzc3dvcmQxMjM=  # password123
  
  # Email configuration
  SMTP_PASSWORD: ZW1haWwtcGFzc3dvcmQ=  # email-password
  SMTP_USER: ****************************  # <EMAIL>
  
  # External API keys
  BLOCKCHAIN_API_KEY: YmxvY2tjaGFpbi1hcGkta2V5LTEyMzQ1Njc4OTA=  # blockchain-api-key-1234567890
  TANOS_API_KEY: dGFub3MtYXBpLWtleS0xMjM0NTY3ODkw  # tanos-api-key-1234567890
  
  # Monitoring
  GRAFANA_PASSWORD: Z3JhZmFuYS1wYXNzd29yZA==  # grafana-password

---
apiVersion: v1
kind: Secret
metadata:
  name: tls-secret
  namespace: exopiper
type: kubernetes.io/tls
data:
  # TLS certificate and key (base64 encoded)
  # Replace with your actual certificate
  tls.crt: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0t...
  tls.key: LS0tLS1CRUdJTiBQUklWQVRFIEtFWS0tLS0t...

---
apiVersion: v1
kind: Secret
metadata:
  name: docker-registry-secret
  namespace: exopiper
type: kubernetes.io/dockerconfigjson
data:
  # Docker registry credentials for private images
  # Create with: kubectl create secret docker-registry docker-registry-secret --docker-server=your-registry --docker-username=your-username --docker-password=your-password --docker-email=your-email
  .dockerconfigjson: ****************************************************************************************************************************************************************************************************************
