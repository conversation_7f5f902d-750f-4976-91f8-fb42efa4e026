"""
Tests for authentication endpoints
"""

import pytest
from httpx import <PERSON><PERSON><PERSON><PERSON>
from sqlalchemy.ext.asyncio import AsyncSession

from app.models.user import User


class TestAuth:
    """Test authentication endpoints"""
    
    async def test_register_user(self, test_client: AsyncClient):
        """Test user registration"""
        user_data = {
            "username": "newuser",
            "email": "<EMAIL>",
            "password": "testpassword123",
            "plan_type": "free"
        }
        
        response = await test_client.post("/api/v1/auth/register", json=user_data)
        
        assert response.status_code == 201
        data = response.json()
        assert data["username"] == user_data["username"]
        assert data["email"] == user_data["email"]
        assert data["plan_type"] == user_data["plan_type"]
        assert "id" in data
        assert "hashed_password" not in data  # Should not return password
    
    async def test_register_duplicate_email(self, test_client: AsyncClient, test_user: User):
        """Test registration with duplicate email"""
        user_data = {
            "username": "anotheruser",
            "email": test_user.email,  # Duplicate email
            "password": "testpassword123",
            "plan_type": "free"
        }
        
        response = await test_client.post("/api/v1/auth/register", json=user_data)
        
        assert response.status_code == 400
        assert "already registered" in response.json()["detail"].lower()
    
    async def test_login_success(self, test_client: AsyncClient, test_user: User):
        """Test successful login"""
        login_data = {
            "email": test_user.email,
            "password": "testpassword"
        }
        
        response = await test_client.post("/api/v1/auth/login", json=login_data)
        
        assert response.status_code == 200
        data = response.json()
        assert "access_token" in data
        assert "refresh_token" in data
        assert data["token_type"] == "bearer"
    
    async def test_login_invalid_credentials(self, test_client: AsyncClient, test_user: User):
        """Test login with invalid credentials"""
        login_data = {
            "email": test_user.email,
            "password": "wrongpassword"
        }
        
        response = await test_client.post("/api/v1/auth/login", json=login_data)
        
        assert response.status_code == 401
        assert "incorrect" in response.json()["detail"].lower()
    
    async def test_login_nonexistent_user(self, test_client: AsyncClient):
        """Test login with non-existent user"""
        login_data = {
            "email": "<EMAIL>",
            "password": "testpassword"
        }
        
        response = await test_client.post("/api/v1/auth/login", json=login_data)
        
        assert response.status_code == 401
        assert "incorrect" in response.json()["detail"].lower()
    
    async def test_get_current_user(self, authenticated_client: AsyncClient, test_user: User):
        """Test getting current user info"""
        response = await authenticated_client.get("/api/v1/auth/me")
        
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == test_user.id
        assert data["username"] == test_user.username
        assert data["email"] == test_user.email
        assert data["plan_type"] == test_user.plan_type
    
    async def test_get_current_user_unauthorized(self, test_client: AsyncClient):
        """Test getting current user without authentication"""
        response = await test_client.get("/api/v1/auth/me")
        
        assert response.status_code == 401
    
    async def test_get_api_key(self, authenticated_client: AsyncClient, test_user: User):
        """Test getting API key"""
        response = await authenticated_client.get("/api/v1/auth/api-key")
        
        assert response.status_code == 200
        data = response.json()
        assert "api_key" in data
        assert "created_at" in data
        assert len(data["api_key"]) > 20  # API key should be reasonably long
    
    async def test_refresh_token(self, test_client: AsyncClient, test_user: User):
        """Test token refresh"""
        # First login to get refresh token
        login_data = {
            "email": test_user.email,
            "password": "testpassword"
        }
        
        login_response = await test_client.post("/api/v1/auth/login", json=login_data)
        refresh_token = login_response.json()["refresh_token"]
        
        # Use refresh token to get new access token
        refresh_data = {"refresh_token": refresh_token}
        response = await test_client.post("/api/v1/auth/refresh", json=refresh_data)
        
        assert response.status_code == 200
        data = response.json()
        assert "access_token" in data
        assert "token_type" in data
    
    async def test_invalid_refresh_token(self, test_client: AsyncClient):
        """Test refresh with invalid token"""
        refresh_data = {"refresh_token": "invalid_token"}
        response = await test_client.post("/api/v1/auth/refresh", json=refresh_data)
        
        assert response.status_code == 401


class TestAuthValidation:
    """Test authentication input validation"""
    
    async def test_register_invalid_email(self, test_client: AsyncClient):
        """Test registration with invalid email"""
        user_data = {
            "username": "testuser",
            "email": "invalid-email",
            "password": "testpassword123",
            "plan_type": "free"
        }
        
        response = await test_client.post("/api/v1/auth/register", json=user_data)
        
        assert response.status_code == 422  # Validation error
    
    async def test_register_short_password(self, test_client: AsyncClient):
        """Test registration with short password"""
        user_data = {
            "username": "testuser",
            "email": "<EMAIL>",
            "password": "123",  # Too short
            "plan_type": "free"
        }
        
        response = await test_client.post("/api/v1/auth/register", json=user_data)
        
        assert response.status_code == 422  # Validation error
    
    async def test_register_invalid_plan_type(self, test_client: AsyncClient):
        """Test registration with invalid plan type"""
        user_data = {
            "username": "testuser",
            "email": "<EMAIL>",
            "password": "testpassword123",
            "plan_type": "invalid_plan"
        }
        
        response = await test_client.post("/api/v1/auth/register", json=user_data)
        
        assert response.status_code == 422  # Validation error
    
    async def test_login_missing_fields(self, test_client: AsyncClient):
        """Test login with missing fields"""
        # Missing password
        login_data = {"email": "<EMAIL>"}
        response = await test_client.post("/api/v1/auth/login", json=login_data)
        assert response.status_code == 422
        
        # Missing email
        login_data = {"password": "testpassword"}
        response = await test_client.post("/api/v1/auth/login", json=login_data)
        assert response.status_code == 422
