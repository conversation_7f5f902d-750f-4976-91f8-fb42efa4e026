import { test, expect } from '@playwright/test'

test.describe('Payment System', () => {
  test.beforeEach(async ({ page }) => {
    // Login before each test
    await page.goto('/')
    await page.fill('input[type="email"]', '<EMAIL>')
    await page.fill('input[type="password"]', 'testpassword')
    await page.click('button[type="submit"]')
    await expect(page).toHaveURL('/dashboard')
  })

  test('should display upgrade page', async ({ page }) => {
    await page.goto('/upgrade')
    
    await expect(page.locator('h1')).toContainText('Upgrade de Plano')
    await expect(page.locator('[data-testid="plan-card"]')).toHaveCount(3) // Basic, Pro, Enterprise
    await expect(page.locator('text=USDT')).toBeVisible()
    await expect(page.locator('text=Bitcoin')).toBeVisible()
  })

  test('should show current plan status', async ({ page }) => {
    await page.goto('/upgrade')
    
    // Should show current plan
    await expect(page.locator('[data-testid="current-plan"]')).toBeVisible()
    await expect(page.locator('text=Plano Atual')).toBeVisible()
  })

  test('should display plan features correctly', async ({ page }) => {
    await page.goto('/upgrade')
    
    // Check Basic plan features
    const basicPlan = page.locator('[data-testid="plan-basic"]')
    await expect(basicPlan.locator('text=$50/mês')).toBeVisible()
    await expect(basicPlan.locator('text=10 workloads')).toBeVisible()
    await expect(basicPlan.locator('text=200 jobs/mês')).toBeVisible()
    
    // Check Pro plan features
    const proPlan = page.locator('[data-testid="plan-pro"]')
    await expect(proPlan.locator('text=$200/mês')).toBeVisible()
    await expect(proPlan.locator('text=50 workloads')).toBeVisible()
    await expect(proPlan.locator('text=1000 jobs/mês')).toBeVisible()
    
    // Check Enterprise plan features
    const enterprisePlan = page.locator('[data-testid="plan-enterprise"]')
    await expect(enterprisePlan.locator('text=$1000/mês')).toBeVisible()
    await expect(enterprisePlan.locator('text=Ilimitado')).toBeVisible()
  })

  test('should create USDT payment request', async ({ page }) => {
    await page.goto('/upgrade')
    
    // Select Basic plan
    await page.click('[data-testid="plan-basic"] button:has-text("Selecionar")')
    
    // Should show payment method selection
    await expect(page.locator('text=Método de Pagamento')).toBeVisible()
    
    // Select BEP20 network
    await page.click('[data-testid="network-bep20"]')
    
    await page.click('button:has-text("Continuar")')
    
    // Should show payment details
    await expect(page.locator('text=Detalhes do Pagamento')).toBeVisible()
    await expect(page.locator('text=50.000000 USDT')).toBeVisible()
    await expect(page.locator('text=BEP20')).toBeVisible()
    await expect(page.locator('[data-testid="wallet-address"]')).toContainText('******************************************')
  })

  test('should display QR code for payment', async ({ page }) => {
    await page.goto('/upgrade')
    
    // Create payment request
    await page.click('[data-testid="plan-basic"] button:has-text("Selecionar")')
    await page.click('[data-testid="network-bep20"]')
    await page.click('button:has-text("Continuar")')
    
    // Should show QR code
    await expect(page.locator('[data-testid="payment-qr-code"]')).toBeVisible()
    await expect(page.locator('text=Escaneie com sua wallet')).toBeVisible()
  })

  test('should show payment instructions', async ({ page }) => {
    await page.goto('/upgrade')
    
    // Create payment request
    await page.click('[data-testid="plan-basic"] button:has-text("Selecionar")')
    await page.click('[data-testid="network-bep20"]')
    await page.click('button:has-text("Continuar")')
    
    // Should show instructions
    await expect(page.locator('text=Instruções de Pagamento')).toBeVisible()
    await expect(page.locator('text=1. Abra sua wallet')).toBeVisible()
    await expect(page.locator('text=2. Conecte à rede Binance Smart Chain')).toBeVisible()
    await expect(page.locator('text=3. Envie o valor exato')).toBeVisible()
  })

  test('should copy wallet address', async ({ page }) => {
    await page.goto('/upgrade')
    
    // Create payment request
    await page.click('[data-testid="plan-basic"] button:has-text("Selecionar")')
    await page.click('[data-testid="network-bep20"]')
    await page.click('button:has-text("Continuar")')
    
    // Click copy button
    await page.click('[data-testid="copy-address"]')
    
    // Should show success message
    await expect(page.locator('text=Endereço copiado')).toBeVisible()
  })

  test('should show payment timer', async ({ page }) => {
    await page.goto('/upgrade')
    
    // Create payment request
    await page.click('[data-testid="plan-basic"] button:has-text("Selecionar")')
    await page.click('[data-testid="network-bep20"]')
    await page.click('button:has-text("Continuar")')
    
    // Should show countdown timer
    await expect(page.locator('[data-testid="payment-timer"]')).toBeVisible()
    await expect(page.locator('text=Expira em')).toBeVisible()
  })

  test('should check payment status', async ({ page }) => {
    await page.goto('/upgrade')
    
    // Create payment request
    await page.click('[data-testid="plan-basic"] button:has-text("Selecionar")')
    await page.click('[data-testid="network-bep20"]')
    await page.click('button:has-text("Continuar")')
    
    // Should show status checking
    await expect(page.locator('text=Aguardando pagamento')).toBeVisible()
    await expect(page.locator('[data-testid="payment-status"]')).toContainText('Pendente')
    
    // Should have refresh button
    await expect(page.locator('button:has-text("Verificar Status")')).toBeVisible()
  })

  test('should create Bitcoin payment via TANOS', async ({ page }) => {
    await page.goto('/upgrade')
    
    // Select Basic plan
    await page.click('[data-testid="plan-basic"] button:has-text("Selecionar")')
    
    // Select Bitcoin payment
    await page.click('[data-testid="payment-bitcoin"]')
    
    await page.click('button:has-text("Continuar")')
    
    // Should show TANOS swap details
    await expect(page.locator('text=Swap Atômico Bitcoin')).toBeVisible()
    await expect(page.locator('text=TANOS')).toBeVisible()
    await expect(page.locator('[data-testid="bitcoin-address"]')).toBeVisible()
  })

  test('should display payment history', async ({ page }) => {
    await page.goto('/upgrade')
    
    // Go to payment history
    await page.click('text=Histórico de Pagamentos')
    
    await expect(page.locator('h2')).toContainText('Histórico de Pagamentos')
    await expect(page.locator('[data-testid="payment-history-table"]')).toBeVisible()
  })

  test('should filter payment history', async ({ page }) => {
    await page.goto('/upgrade')
    await page.click('text=Histórico de Pagamentos')
    
    // Filter by status
    await page.selectOption('[data-testid="status-filter"]', 'confirmed')
    
    // Should filter results
    const statusCells = await page.locator('[data-testid="payment-status-cell"]').allTextContents()
    statusCells.forEach(status => {
      expect(status.toLowerCase()).toContain('confirmado')
    })
  })

  test('should show network comparison', async ({ page }) => {
    await page.goto('/upgrade')
    
    await page.click('[data-testid="plan-basic"] button:has-text("Selecionar")')
    
    // Should show network options with fees
    await expect(page.locator('text=BEP20')).toBeVisible()
    await expect(page.locator('text=~$0.20')).toBeVisible() // BEP20 fees
    await expect(page.locator('text=ERC20')).toBeVisible()
    await expect(page.locator('text=~$5-20')).toBeVisible() // ERC20 fees
  })

  test('should handle payment expiration', async ({ page }) => {
    await page.goto('/upgrade')
    
    // Create payment request
    await page.click('[data-testid="plan-basic"] button:has-text("Selecionar")')
    await page.click('[data-testid="network-bep20"]')
    await page.click('button:has-text("Continuar")')
    
    // Mock expired payment (this would need to be set up in test environment)
    // For now, just check that expiration handling exists
    await expect(page.locator('[data-testid="payment-timer"]')).toBeVisible()
  })

  test('should show plan upgrade success', async ({ page }) => {
    // This test would require mocking a successful payment
    // For now, just check that the success flow exists
    await page.goto('/upgrade?success=true')
    
    await expect(page.locator('text=Pagamento Confirmado')).toBeVisible()
    await expect(page.locator('text=Seu plano foi atualizado')).toBeVisible()
  })

  test('should handle payment errors', async ({ page }) => {
    await page.goto('/upgrade')
    
    // Try to create payment without selecting plan
    await page.click('button:has-text("Continuar")')
    
    // Should show error
    await expect(page.locator('text=Selecione um plano')).toBeVisible()
  })

  test('should show current plan limits', async ({ page }) => {
    await page.goto('/upgrade')
    
    // Should show current usage and limits
    await expect(page.locator('[data-testid="current-usage"]')).toBeVisible()
    await expect(page.locator('text=Workloads:')).toBeVisible()
    await expect(page.locator('text=Jobs este mês:')).toBeVisible()
  })

  test('should disable unavailable plans', async ({ page }) => {
    await page.goto('/upgrade')
    
    // If user already has a higher plan, lower plans should be disabled
    const currentPlan = await page.locator('[data-testid="current-plan"]').textContent()
    
    if (currentPlan?.includes('Pro')) {
      // Basic plan should be disabled
      await expect(page.locator('[data-testid="plan-basic"] button')).toBeDisabled()
    }
  })
})
