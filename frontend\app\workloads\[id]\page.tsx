'use client'

import { useState, useEffect } from 'react'
import { useRouter, useParams } from 'next/navigation'
import {
  ChartBarIcon,
  CpuChipIcon,
  ArrowLeftIcon,
  PlayIcon,
  ClockIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline'
import { useAuth } from '@/contexts/AuthContext'
import { apiClient, Workload, BenchmarkJob } from '@/lib/api'
import ComplexityChart from '@/components/ComplexityChart'
import toast from 'react-hot-toast'

export default function WorkloadDetailPage() {
  const [workload, setWorkload] = useState<Workload | null>(null)
  const [jobs, setJobs] = useState<BenchmarkJob[]>([])
  const [analysis, setAnalysis] = useState<any>(null)
  const [benchmarkResults, setBenchmarkResults] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(true)

  const { isAuthenticated } = useAuth()
  const router = useRouter()
  const params = useParams()
  const workloadId = parseInt(params.id as string)

  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/login')
      return
    }

    if (workloadId) {
      loadWorkloadData()
    }
  }, [isAuthenticated, workloadId])

  const loadWorkloadData = async () => {
    try {
      setIsLoading(true)

      const [workloadData, jobsData] = await Promise.all([
        apiClient.getWorkload(workloadId),
        apiClient.getBenchmarkJobs()
      ])

      setWorkload(workloadData)

      // Filter jobs for this workload
      const workloadJobs = jobsData.jobs.filter(job => job.workload_id === workloadId)
      setJobs(workloadJobs)

      // Load complexity analysis if there are completed jobs
      const completedJobs = workloadJobs.filter(job => job.status === 'completed')
      if (completedJobs.length > 0) {
        try {
          const analysisData = await apiClient.getComplexityAnalysis(workloadId)
          setAnalysis(analysisData)

          // Extract benchmark results for chart
          if (analysisData.status === 'success' && analysisData.data_points) {
            setBenchmarkResults(analysisData.raw_data || [])
          }
        } catch (error) {
          console.log('No analysis data available yet')
        }
      }

    } catch (error: any) {
      console.error('Failed to load workload data:', error)
      toast.error('Falha ao carregar dados do workload')
      router.push('/dashboard')
    } finally {
      setIsLoading(false)
    }
  }

  const handleRunBenchmark = async () => {
    if (!workload) return

    try {
      const result = await apiClient.runBenchmark(workload.id)
      toast.success(`Benchmark iniciado: ${result.job_id}`)

      // Refresh jobs
      const jobsData = await apiClient.getBenchmarkJobs()
      const workloadJobs = jobsData.jobs.filter(job => job.workload_id === workloadId)
      setJobs(workloadJobs)

    } catch (error: any) {
      toast.error(`Falha ao iniciar benchmark: ${error.message}`)
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800'
      case 'running': return 'bg-blue-100 text-blue-800'
      case 'pending': return 'bg-yellow-100 text-yellow-800'
      case 'failed': return 'bg-red-100 text-red-800'
      case 'cancelled': return 'bg-gray-100 text-gray-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getComplexityColor = (p: number) => {
    if (p <= 1.5) return 'text-green-600'
    if (p <= 2.0) return 'text-yellow-600'
    return 'text-red-600'
  }

  if (!isAuthenticated) {
    return null
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <CpuChipIcon className="h-12 w-12 text-primary-600 mx-auto mb-4 animate-spin" />
          <p className="text-gray-600">Carregando dados do workload...</p>
        </div>
      </div>
    )
  }

  if (!workload) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <ExclamationTriangleIcon className="h-12 w-12 text-red-600 mx-auto mb-4" />
          <p className="text-gray-600">Workload não encontrado</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between py-6">
            <div className="flex items-center">
              <button
                onClick={() => router.push('/dashboard')}
                className="mr-4 p-2 text-gray-400 hover:text-gray-600"
              >
                <ArrowLeftIcon className="h-5 w-5" />
              </button>
              <CpuChipIcon className="h-8 w-8 text-primary-600" />
              <div className="ml-3">
                <h1 className="text-2xl font-bold text-gray-900">{workload.name}</h1>
                <p className="text-sm text-gray-600">{workload.workload_type}</p>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <span className={`px-3 py-1 text-sm font-medium rounded-full ${
                workload.is_active
                  ? 'bg-green-100 text-green-800'
                  : 'bg-gray-100 text-gray-800'
              }`}>
                {workload.is_active ? 'Ativo' : 'Inativo'}
              </span>
              <button
                onClick={handleRunBenchmark}
                className="btn-primary flex items-center"
                disabled={!workload.is_active}
              >
                <PlayIcon className="h-4 w-4 mr-2" />
                Executar Benchmark
              </button>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Workload Info */}
          <div className="lg:col-span-1">
            <div className="card mb-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Informações</h3>

              <div className="space-y-3">
                <div>
                  <span className="text-sm font-medium text-gray-600">Tipo:</span>
                  <span className="ml-2 text-sm text-gray-900">{workload.workload_type}</span>
                </div>

                <div>
                  <span className="text-sm font-medium text-gray-600">Descrição:</span>
                  <p className="text-sm text-gray-900 mt-1">{workload.description || 'Sem descrição'}</p>
                </div>

                <div>
                  <span className="text-sm font-medium text-gray-600">Criado em:</span>
                  <span className="ml-2 text-sm text-gray-900">{formatDate(workload.created_at)}</span>
                </div>

                {workload.updated_at && (
                  <div>
                    <span className="text-sm font-medium text-gray-600">Atualizado em:</span>
                    <span className="ml-2 text-sm text-gray-900">{formatDate(workload.updated_at)}</span>
                  </div>
                )}
              </div>
            </div>

            <div className="card">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Configuração</h3>
              <div className="space-y-2">
                {Object.entries(workload.config).map(([key, value]) => (
                  <div key={key} className="flex justify-between text-sm">
                    <span className="font-medium text-gray-600">{key}:</span>
                    <span className="text-gray-900">{String(value)}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Analysis & Jobs */}
          <div className="lg:col-span-2">
            {/* Complexity Chart */}
            {analysis && analysis.status === 'success' && benchmarkResults.length > 0 && (
              <div className="card mb-6">
                <ComplexityChart
                  data={benchmarkResults}
                  analysis={analysis}
                  title={`Análise de Complexidade - ${workload.name}`}
                />
              </div>
            )}

            {/* Complexity Analysis */}
            {analysis && analysis.status === 'success' && (
              <div className="card mb-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Análise de Complexidade</h3>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-primary-600">
                      {analysis.p_exponent?.toFixed(3)}
                    </div>
                    <div className="text-sm text-gray-600">Expoente (p)</div>
                    <div className={`text-xs mt-1 ${getComplexityColor(analysis.p_exponent)}`}>
                      {analysis.algorithm_classification?.category}
                    </div>
                  </div>

                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">
                      {analysis.lambda_value?.toExponential(2)}
                    </div>
                    <div className="text-sm text-gray-600">λ (hardware)</div>
                    <div className="text-xs text-gray-500 mt-1">nanosegundos</div>
                  </div>

                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">
                      {(analysis.r_squared * 100)?.toFixed(1)}%
                    </div>
                    <div className="text-sm text-gray-600">R² (qualidade)</div>
                    <div className="text-xs text-gray-500 mt-1">do ajuste</div>
                  </div>
                </div>

                {analysis.performance_insights && (
                  <div className="bg-blue-50 p-4 rounded-lg">
                    <h4 className="font-medium text-blue-900 mb-2">Insights</h4>
                    <ul className="text-sm text-blue-800 space-y-1">
                      {analysis.performance_insights.map((insight: string, index: number) => (
                        <li key={index}>• {insight}</li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            )}

            {/* Recent Jobs */}
            <div className="card">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-medium text-gray-900">Jobs Recentes</h3>
                <span className="text-sm text-gray-600">{jobs.length} total</span>
              </div>

              {jobs.length === 0 ? (
                <div className="text-center py-8">
                  <ClockIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-600">Nenhum job executado ainda</p>
                  <button
                    onClick={handleRunBenchmark}
                    className="btn-primary mt-4"
                  >
                    Executar Primeiro Benchmark
                  </button>
                </div>
              ) : (
                <div className="space-y-3">
                  {jobs.slice(0, 10).map((job) => (
                    <div key={job.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div className="flex items-center">
                        <div className="flex-1">
                          <div className="flex items-center">
                            <span className="font-medium text-gray-900 text-sm">
                              {job.job_id.slice(0, 8)}...
                            </span>
                            <span className={`ml-2 px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(job.status)}`}>
                              {job.status}
                            </span>
                          </div>
                          <div className="text-xs text-gray-600 mt-1">
                            {formatDate(job.created_at)}
                            {job.duration_seconds && (
                              <span className="ml-2">• {job.duration_seconds.toFixed(1)}s</span>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
