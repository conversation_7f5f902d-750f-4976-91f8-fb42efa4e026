"""
Payment models for USDT payments via TANOS
"""

from sqlalchemy import Column, Integer, String, DateTime, Enum, ForeignKey, Numeric, Text
from sqlalchemy.orm import relationship
from datetime import datetime
import enum

from ..core.database import Base


class PaymentStatus(enum.Enum):
    """Payment status enumeration"""
    PENDING = "pending"
    CONFIRMED = "confirmed"
    EXPIRED = "expired"
    FAILED = "failed"
    REFUNDED = "refunded"


class PaymentMethod(enum.Enum):
    """Payment method enumeration"""
    USDT_BEP20 = "usdt_bep20"
    USDT_ERC20 = "usdt_erc20"
    BITCOIN = "bitcoin"
    TANOS_SWAP = "tanos_swap"


class Payment(Base):
    """Payment model for tracking USDT payments"""
    
    __tablename__ = "payments"
    
    # Primary key
    id = Column(Integer, primary_key=True, index=True)
    
    # User relationship
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    user = relationship("User", back_populates="payments")
    
    # Payment details
    amount = Column(Numeric(precision=18, scale=6), nullable=False)  # Support up to 18 digits with 6 decimals
    currency = Column(String(10), nullable=False, default="USDT")
    network = Column(String(20), nullable=False)  # bep20, erc20, bitcoin
    
    # Target plan
    target_plan = Column(String(20), nullable=False)  # basic, pro, enterprise
    
    # Blockchain details
    payment_address = Column(String(100), nullable=False)  # Our wallet address
    contract_address = Column(String(100), nullable=True)  # USDT contract address
    transaction_hash = Column(String(100), nullable=True, index=True)  # Blockchain tx hash
    
    # Status and timing
    status = Column(Enum(PaymentStatus), nullable=False, default=PaymentStatus.PENDING, index=True)
    created_at = Column(DateTime, nullable=False, default=datetime.utcnow, index=True)
    confirmed_at = Column(DateTime, nullable=True)
    expires_at = Column(DateTime, nullable=True, index=True)
    
    # Additional metadata
    metadata = Column(Text, nullable=True)  # JSON metadata for additional info
    
    def __repr__(self):
        return f"<Payment(id={self.id}, user_id={self.user_id}, amount={self.amount}, status={self.status})>"


class TANOSSwap(Base):
    """TANOS atomic swap model for Bitcoin-Nostr swaps"""
    
    __tablename__ = "tanos_swaps"
    
    # Primary key
    id = Column(Integer, primary_key=True, index=True)
    
    # Payment relationship
    payment_id = Column(Integer, ForeignKey("payments.id"), nullable=False, index=True)
    payment = relationship("Payment")
    
    # TANOS swap details
    swap_id = Column(String(100), nullable=False, unique=True, index=True)  # Unique swap identifier
    
    # Seller details (us - selling access to Exo Piper)
    seller_nostr_pubkey = Column(String(64), nullable=False)  # Our Nostr public key
    seller_commitment = Column(String(64), nullable=False)    # Commitment point T = R + e*P
    
    # Buyer details (customer)
    buyer_bitcoin_pubkey = Column(String(66), nullable=True)  # Customer's Bitcoin public key
    
    # Bitcoin transaction details
    locking_tx_hash = Column(String(64), nullable=True, index=True)      # Bitcoin locking transaction
    spending_tx_hash = Column(String(64), nullable=True, index=True)     # Bitcoin spending transaction
    
    # Nostr event details
    nostr_event_id = Column(String(64), nullable=True, index=True)       # Nostr event ID
    nostr_signature = Column(String(128), nullable=True)                 # Nostr event signature
    
    # Swap status
    status = Column(String(20), nullable=False, default="initiated", index=True)
    # Possible statuses: initiated, locked, completed, failed, expired
    
    # Timing
    created_at = Column(DateTime, nullable=False, default=datetime.utcnow, index=True)
    locked_at = Column(DateTime, nullable=True)      # When Bitcoin was locked
    completed_at = Column(DateTime, nullable=True)   # When swap was completed
    expires_at = Column(DateTime, nullable=True, index=True)
    
    # Additional data
    swap_data = Column(Text, nullable=True)  # JSON data for swap details
    
    def __repr__(self):
        return f"<TANOSSwap(id={self.id}, swap_id={self.swap_id}, status={self.status})>"


class PaymentWebhook(Base):
    """Webhook events for payment notifications"""
    
    __tablename__ = "payment_webhooks"
    
    # Primary key
    id = Column(Integer, primary_key=True, index=True)
    
    # Payment relationship
    payment_id = Column(Integer, ForeignKey("payments.id"), nullable=False, index=True)
    payment = relationship("Payment")
    
    # Webhook details
    event_type = Column(String(50), nullable=False, index=True)  # payment_confirmed, payment_failed, etc.
    webhook_url = Column(String(500), nullable=True)            # URL to send webhook to
    
    # Request/Response details
    request_payload = Column(Text, nullable=True)   # JSON payload sent
    response_status = Column(Integer, nullable=True) # HTTP response status
    response_body = Column(Text, nullable=True)     # Response body
    
    # Retry logic
    attempts = Column(Integer, nullable=False, default=0)
    max_attempts = Column(Integer, nullable=False, default=3)
    next_retry_at = Column(DateTime, nullable=True)
    
    # Status
    status = Column(String(20), nullable=False, default="pending", index=True)
    # Possible statuses: pending, sent, failed, max_retries_reached
    
    # Timing
    created_at = Column(DateTime, nullable=False, default=datetime.utcnow, index=True)
    sent_at = Column(DateTime, nullable=True)
    
    def __repr__(self):
        return f"<PaymentWebhook(id={self.id}, payment_id={self.payment_id}, event_type={self.event_type})>"
