'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import {
  ChartBarIcon,
  CpuChipIcon,
  ClockIcon,
  ExclamationTriangleIcon,
  PlayIcon,
  PlusIcon,
  ArrowRightOnRectangleIcon
} from '@heroicons/react/24/outline'
import { useAuth } from '@/contexts/AuthContext'
import { apiClient, Workload, Alert, BenchmarkJob } from '@/lib/api'
import toast from 'react-hot-toast'

export default function DashboardPage() {
  const [workloads, setWorkloads] = useState<Workload[]>([])
  const [alerts, setAlerts] = useState<Alert[]>([])
  const [jobs, setJobs] = useState<BenchmarkJob[]>([])
  const [summary, setSummary] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(true)

  const { user, isAuthenticated, logout } = useAuth()
  const router = useRouter()

  // Redirect if not authenticated
  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/login')
    }
  }, [isAuthenticated, router])

  // Load dashboard data
  useEffect(() => {
    if (isAuthenticated) {
      loadDashboardData()
    }
  }, [isAuthenticated])

  const loadDashboardData = async () => {
    try {
      setIsLoading(true)

      // Load data in parallel
      const [workloadsData, alertsData, jobsData, summaryData] = await Promise.all([
        apiClient.getWorkloads(1, 20),
        apiClient.getAlerts(7),
        apiClient.getBenchmarkJobs(),
        apiClient.getPerformanceSummary()
      ])

      setWorkloads(workloadsData.workloads)
      setAlerts(alertsData.alerts)
      setJobs(jobsData.jobs)
      setSummary(summaryData)

    } catch (error: any) {
      console.error('Failed to load dashboard data:', error)
      toast.error('Falha ao carregar dados do dashboard')
    } finally {
      setIsLoading(false)
    }
  }

  const handleRunBenchmark = async (workloadId: number) => {
    try {
      const result = await apiClient.runBenchmark(workloadId)
      toast.success(`Benchmark iniciado: ${result.job_id}`)

      // Refresh jobs list
      const jobsData = await apiClient.getBenchmarkJobs()
      setJobs(jobsData.jobs)

    } catch (error: any) {
      toast.error(`Falha ao iniciar benchmark: ${error.message}`)
    }
  }

  const handleLogout = () => {
    logout()
    router.push('/')
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getComplexityColor = (p: number) => {
    if (p <= 1.5) return 'text-green-600'
    if (p <= 2.0) return 'text-yellow-600'
    return 'text-red-600'
  }

  const getComplexityLabel = (p: number) => {
    if (p <= 1.5) return 'Linear/Sub-quadratic'
    if (p <= 2.0) return 'Quadratic'
    return 'Polynomial'
  }

  if (!isAuthenticated) {
    return null // Will redirect
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4 lg:py-6">
            <div className="flex items-center">
              <CpuChipIcon className="h-6 w-6 sm:h-8 sm:w-8 text-primary-600" />
              <h1 className="ml-2 text-lg sm:text-2xl font-bold text-gray-900">Exo Piper</h1>
            </div>

            {/* Desktop Navigation */}
            <div className="hidden lg:flex items-center space-x-4">
              <span className="text-sm text-gray-600">
                Olá, {user?.username} • Plano: {user?.plan_type?.toUpperCase()}
              </span>
              <button
                onClick={handleLogout}
                className="btn-secondary flex items-center"
              >
                <ArrowRightOnRectangleIcon className="h-4 w-4 mr-2" />
                Sair
              </button>
              <button className="btn-primary">Upgrade</button>
            </div>

            {/* Mobile Navigation */}
            <div className="lg:hidden flex items-center space-x-2">
              <button
                onClick={handleLogout}
                className="btn-secondary p-2"
                title="Sair"
              >
                <ArrowRightOnRectangleIcon className="h-5 w-5" />
              </button>
              <button
                onClick={() => router.push('/upgrade')}
                className="btn-primary text-sm px-3 py-2"
              >
                Upgrade
              </button>
              {user?.plan_type === 'enterprise' && (
                <button
                  onClick={() => router.push('/admin')}
                  className="btn-secondary text-sm px-3 py-2"
                >
                  Admin
                </button>
              )}
            </div>
          </div>

          {/* Mobile User Info */}
          <div className="lg:hidden pb-4 border-t border-gray-200 pt-4">
            <span className="text-sm text-gray-600">
              Olá, {user?.username} • Plano: {user?.plan_type?.toUpperCase()}
            </span>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Stats Overview */}
        {isLoading ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 mb-6 sm:mb-8">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="card animate-pulse">
                <div className="flex items-center">
                  <div className="h-6 w-6 sm:h-8 sm:w-8 bg-gray-300 rounded"></div>
                  <div className="ml-3 sm:ml-4 flex-1">
                    <div className="h-3 sm:h-4 bg-gray-300 rounded mb-2"></div>
                    <div className="h-5 sm:h-6 bg-gray-300 rounded w-12 sm:w-16"></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 mb-6 sm:mb-8">
            <div className="card">
              <div className="flex items-center">
                <ChartBarIcon className="h-6 w-6 sm:h-8 sm:w-8 text-primary-600" />
                <div className="ml-3 sm:ml-4">
                  <p className="text-xs sm:text-sm font-medium text-gray-600">Total Workloads</p>
                  <p className="text-xl sm:text-2xl font-bold text-gray-900">{workloads.length}</p>
                </div>
              </div>
            </div>

            <div className="card">
              <div className="flex items-center">
                <ClockIcon className="h-6 w-6 sm:h-8 sm:w-8 text-green-600" />
                <div className="ml-3 sm:ml-4">
                  <p className="text-xs sm:text-sm font-medium text-gray-600">Jobs Executados</p>
                  <p className="text-xl sm:text-2xl font-bold text-gray-900">{summary?.total_jobs || jobs.length}</p>
                </div>
              </div>
            </div>

            <div className="card">
              <div className="flex items-center">
                <ExclamationTriangleIcon className="h-6 w-6 sm:h-8 sm:w-8 text-yellow-600" />
                <div className="ml-3 sm:ml-4">
                  <p className="text-xs sm:text-sm font-medium text-gray-600">Alertas Ativos</p>
                  <p className="text-xl sm:text-2xl font-bold text-gray-900">{alerts.length}</p>
                </div>
              </div>
            </div>

            <div className="card">
              <div className="flex items-center">
                <CpuChipIcon className="h-6 w-6 sm:h-8 sm:w-8 text-blue-600" />
                <div className="ml-3 sm:ml-4">
                  <p className="text-xs sm:text-sm font-medium text-gray-600">Tempo Total</p>
                  <p className="text-xl sm:text-2xl font-bold text-gray-900">
                    {summary?.job_statistics?.total_execution_time_hours?.toFixed(1) || '0'}h
                  </p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Alerts Section */}
        {alerts.length > 0 && (
          <div className="mb-8">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">🚨 Alertas Recentes</h2>
            <div className="space-y-3">
              {alerts.map((alert) => (
                <div key={alert.id} className="bg-red-50 border border-red-200 rounded-lg p-4">
                  <div className="flex items-start">
                    <ExclamationTriangleIcon className="h-5 w-5 text-red-600 mt-0.5" />
                    <div className="ml-3 flex-1">
                      <p className="text-sm font-medium text-red-800">{alert.message}</p>
                      <p className="text-xs text-red-600 mt-1">
                        {alert.workload} • {formatDate(alert.created_at)}
                      </p>
                    </div>
                    <button className="text-red-600 hover:text-red-800 text-sm font-medium">
                      Ver Detalhes
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Workloads Section */}
        <div className="mb-6 sm:mb-8">
          <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-4 space-y-2 sm:space-y-0">
            <h2 className="text-lg font-semibold text-gray-900">Workloads</h2>
            <button
              onClick={() => router.push('/workloads/new')}
              className="btn-primary flex items-center justify-center sm:justify-start"
            >
              <PlusIcon className="h-4 w-4 mr-2" />
              Novo Workload
            </button>
          </div>

          <div className="grid grid-cols-1 xl:grid-cols-2 gap-4 sm:gap-6">
            {workloads.map((workload) => (
              <div key={workload.id} className="card">
                <div className="flex justify-between items-start mb-4">
                  <div>
                    <h3 className="text-lg font-medium text-gray-900">{workload.name}</h3>
                    <p className="text-sm text-gray-600">{workload.type}</p>
                  </div>
                  <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                    workload.status === 'active'
                      ? 'bg-green-100 text-green-800'
                      : 'bg-gray-100 text-gray-800'
                  }`}>
                    {workload.status}
                  </span>
                </div>

                <div className="mb-4">
                  <h4 className="text-sm font-medium text-gray-700 mb-2">Configuração</h4>
                  <div className="text-xs text-gray-600">
                    {Object.entries(workload.config).slice(0, 3).map(([key, value]) => (
                      <div key={key} className="flex justify-between">
                        <span>{key}:</span>
                        <span>{String(value)}</span>
                      </div>
                    ))}
                  </div>
                </div>

                <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-2 sm:space-y-0">
                  <span className="text-xs text-gray-500">
                    Criado: {formatDate(workload.created_at)}
                  </span>
                  <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2">
                    <button
                      onClick={() => router.push(`/workloads/${workload.id}`)}
                      className="btn-secondary text-sm py-2 px-3 w-full sm:w-auto"
                    >
                      Ver Detalhes
                    </button>
                    <button
                      onClick={() => handleRunBenchmark(workload.id)}
                      className="btn-primary text-sm py-2 px-3 flex items-center justify-center w-full sm:w-auto"
                      disabled={!workload.is_active}
                    >
                      <PlayIcon className="h-3 w-3 mr-1" />
                      Executar
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Quick Actions */}
        <div className="card">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Ações Rápidas</h3>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
            <button className="p-3 sm:p-4 border border-gray-200 rounded-lg hover:bg-gray-50 text-left">
              <ChartBarIcon className="h-5 w-5 sm:h-6 sm:w-6 text-primary-600 mb-2" />
              <h4 className="font-medium text-gray-900 text-sm sm:text-base">Ver Relatórios</h4>
              <p className="text-xs sm:text-sm text-gray-600">Análise detalhada de performance</p>
            </button>

            <button className="p-3 sm:p-4 border border-gray-200 rounded-lg hover:bg-gray-50 text-left">
              <CpuChipIcon className="h-5 w-5 sm:h-6 sm:w-6 text-primary-600 mb-2" />
              <h4 className="font-medium text-gray-900 text-sm sm:text-base">Configurar CLI</h4>
              <p className="text-xs sm:text-sm text-gray-600">Instalar e configurar CLI local</p>
            </button>

            <button className="p-3 sm:p-4 border border-gray-200 rounded-lg hover:bg-gray-50 text-left sm:col-span-2 lg:col-span-1">
              <ExclamationTriangleIcon className="h-5 w-5 sm:h-6 sm:w-6 text-primary-600 mb-2" />
              <h4 className="font-medium text-gray-900 text-sm sm:text-base">Configurar Alertas</h4>
              <p className="text-xs sm:text-sm text-gray-600">Definir thresholds e notificações</p>
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
