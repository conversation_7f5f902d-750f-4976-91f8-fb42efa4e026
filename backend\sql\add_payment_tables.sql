-- Add payment tables for USDT payments via TANOS
-- Migration to add payment system

-- Create payments table
CREATE TABLE IF NOT EXISTS payments (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    amount NUMERIC(18,6) NOT NULL,
    currency VARCHAR(10) NOT NULL DEFAULT 'USDT',
    network VARCHAR(20) NOT NULL,
    target_plan VARCHAR(20) NOT NULL,
    payment_address VARCHAR(100) NOT NULL,
    contract_address VARCHAR(100),
    transaction_hash VARCHAR(100),
    status VARCHAR(20) NOT NULL DEFAULT 'pending',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    confirmed_at TIMESTAMP,
    expires_at TIMESTAMP,
    metadata TEXT
);

-- Create indexes for payments table
CREATE INDEX IF NOT EXISTS idx_payments_user_id ON payments(user_id);
CREATE INDEX IF NOT EXISTS idx_payments_status ON payments(status);
CREATE INDEX IF NOT EXISTS idx_payments_created_at ON payments(created_at);
CREATE INDEX IF NOT EXISTS idx_payments_expires_at ON payments(expires_at);
CREATE INDEX IF NOT EXISTS idx_payments_transaction_hash ON payments(transaction_hash);

-- Create TANOS swaps table
CREATE TABLE IF NOT EXISTS tanos_swaps (
    id SERIAL PRIMARY KEY,
    payment_id INTEGER NOT NULL REFERENCES payments(id) ON DELETE CASCADE,
    swap_id VARCHAR(100) NOT NULL UNIQUE,
    seller_nostr_pubkey VARCHAR(64) NOT NULL,
    seller_commitment VARCHAR(64) NOT NULL,
    buyer_bitcoin_pubkey VARCHAR(66),
    locking_tx_hash VARCHAR(64),
    spending_tx_hash VARCHAR(64),
    nostr_event_id VARCHAR(64),
    nostr_signature VARCHAR(128),
    status VARCHAR(20) NOT NULL DEFAULT 'initiated',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    locked_at TIMESTAMP,
    completed_at TIMESTAMP,
    expires_at TIMESTAMP,
    swap_data TEXT
);

-- Create indexes for tanos_swaps table
CREATE INDEX IF NOT EXISTS idx_tanos_swaps_payment_id ON tanos_swaps(payment_id);
CREATE INDEX IF NOT EXISTS idx_tanos_swaps_swap_id ON tanos_swaps(swap_id);
CREATE INDEX IF NOT EXISTS idx_tanos_swaps_status ON tanos_swaps(status);
CREATE INDEX IF NOT EXISTS idx_tanos_swaps_created_at ON tanos_swaps(created_at);
CREATE INDEX IF NOT EXISTS idx_tanos_swaps_expires_at ON tanos_swaps(expires_at);
CREATE INDEX IF NOT EXISTS idx_tanos_swaps_locking_tx_hash ON tanos_swaps(locking_tx_hash);
CREATE INDEX IF NOT EXISTS idx_tanos_swaps_spending_tx_hash ON tanos_swaps(spending_tx_hash);
CREATE INDEX IF NOT EXISTS idx_tanos_swaps_nostr_event_id ON tanos_swaps(nostr_event_id);

-- Create payment webhooks table
CREATE TABLE IF NOT EXISTS payment_webhooks (
    id SERIAL PRIMARY KEY,
    payment_id INTEGER NOT NULL REFERENCES payments(id) ON DELETE CASCADE,
    event_type VARCHAR(50) NOT NULL,
    webhook_url VARCHAR(500),
    request_payload TEXT,
    response_status INTEGER,
    response_body TEXT,
    attempts INTEGER NOT NULL DEFAULT 0,
    max_attempts INTEGER NOT NULL DEFAULT 3,
    next_retry_at TIMESTAMP,
    status VARCHAR(20) NOT NULL DEFAULT 'pending',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    sent_at TIMESTAMP
);

-- Create indexes for payment_webhooks table
CREATE INDEX IF NOT EXISTS idx_payment_webhooks_payment_id ON payment_webhooks(payment_id);
CREATE INDEX IF NOT EXISTS idx_payment_webhooks_event_type ON payment_webhooks(event_type);
CREATE INDEX IF NOT EXISTS idx_payment_webhooks_status ON payment_webhooks(status);
CREATE INDEX IF NOT EXISTS idx_payment_webhooks_created_at ON payment_webhooks(created_at);

-- Add plan expiry fields to users table
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS plan_upgraded_at TIMESTAMP,
ADD COLUMN IF NOT EXISTS plan_expires_at TIMESTAMP;

-- Create indexes for new user fields
CREATE INDEX IF NOT EXISTS idx_users_plan_expires_at ON users(plan_expires_at);

-- Add comments for documentation
COMMENT ON TABLE payments IS 'USDT payments for plan upgrades';
COMMENT ON TABLE tanos_swaps IS 'TANOS atomic swaps for Bitcoin-Nostr payments';
COMMENT ON TABLE payment_webhooks IS 'Webhook events for payment notifications';

COMMENT ON COLUMN payments.amount IS 'Payment amount in USDT (up to 18 digits with 6 decimals)';
COMMENT ON COLUMN payments.network IS 'Payment network: bep20, erc20, bitcoin';
COMMENT ON COLUMN payments.target_plan IS 'Target plan: basic, pro, enterprise';
COMMENT ON COLUMN payments.payment_address IS 'Our wallet address for receiving payment';
COMMENT ON COLUMN payments.contract_address IS 'USDT contract address for the network';
COMMENT ON COLUMN payments.transaction_hash IS 'Blockchain transaction hash';
COMMENT ON COLUMN payments.status IS 'Payment status: pending, confirmed, expired, failed, refunded';

COMMENT ON COLUMN tanos_swaps.swap_id IS 'Unique TANOS swap identifier';
COMMENT ON COLUMN tanos_swaps.seller_nostr_pubkey IS 'Our Nostr public key (seller)';
COMMENT ON COLUMN tanos_swaps.seller_commitment IS 'Commitment point T = R + e*P';
COMMENT ON COLUMN tanos_swaps.buyer_bitcoin_pubkey IS 'Customer Bitcoin public key';
COMMENT ON COLUMN tanos_swaps.status IS 'Swap status: initiated, locked, completed, failed, expired';

COMMENT ON COLUMN users.plan_upgraded_at IS 'When the user last upgraded their plan';
COMMENT ON COLUMN users.plan_expires_at IS 'When the current plan expires (monthly billing)';

-- Insert sample data for testing (optional)
-- INSERT INTO payments (user_id, amount, currency, network, target_plan, payment_address, contract_address, status, expires_at)
-- VALUES (1, 50.000000, 'USDT', 'bep20', 'basic', '******************************************', '******************************************', 'pending', CURRENT_TIMESTAMP + INTERVAL '24 hours');
