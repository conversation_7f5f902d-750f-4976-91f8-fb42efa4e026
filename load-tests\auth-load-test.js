import http from 'k6/http';
import { check, sleep } from 'k6';
import { Rate } from 'k6/metrics';

// Custom metrics
const errorRate = new Rate('errors');

// Test configuration
export const options = {
  stages: [
    { duration: '2m', target: 10 },   // Ramp up to 10 users
    { duration: '5m', target: 10 },   // Stay at 10 users
    { duration: '2m', target: 50 },   // Ramp up to 50 users
    { duration: '5m', target: 50 },   // Stay at 50 users
    { duration: '2m', target: 100 },  // Ramp up to 100 users
    { duration: '5m', target: 100 },  // Stay at 100 users
    { duration: '5m', target: 0 },    // Ramp down to 0 users
  ],
  thresholds: {
    http_req_duration: ['p(95)<2000'], // 95% of requests must complete below 2s
    http_req_failed: ['rate<0.1'],     // Error rate must be below 10%
    errors: ['rate<0.1'],              // Custom error rate below 10%
  },
};

const BASE_URL = __ENV.BASE_URL || 'http://localhost:8000';

// Test data
const users = [
  { email: '<EMAIL>', password: 'testpass123' },
  { email: '<EMAIL>', password: 'testpass123' },
  { email: '<EMAIL>', password: 'testpass123' },
  { email: '<EMAIL>', password: 'testpass123' },
  { email: '<EMAIL>', password: 'testpass123' },
];

export function setup() {
  console.log('Setting up load test environment...');
  
  // Create test users
  users.forEach((user, index) => {
    const registerPayload = {
      username: `loadtestuser${index + 1}`,
      email: user.email,
      password: user.password,
    };
    
    const registerResponse = http.post(
      `${BASE_URL}/api/v1/auth/register`,
      JSON.stringify(registerPayload),
      {
        headers: { 'Content-Type': 'application/json' },
      }
    );
    
    if (registerResponse.status === 201 || registerResponse.status === 400) {
      console.log(`User ${user.email} ready for testing`);
    } else {
      console.error(`Failed to create user ${user.email}: ${registerResponse.status}`);
    }
  });
  
  return { users };
}

export default function (data) {
  // Select random user
  const user = data.users[Math.floor(Math.random() * data.users.length)];
  
  // Test 1: Health check
  const healthResponse = http.get(`${BASE_URL}/health`);
  check(healthResponse, {
    'health check status is 200': (r) => r.status === 200,
    'health check response time < 500ms': (r) => r.timings.duration < 500,
  }) || errorRate.add(1);
  
  sleep(0.5);
  
  // Test 2: Login
  const loginPayload = {
    email: user.email,
    password: user.password,
  };
  
  const loginResponse = http.post(
    `${BASE_URL}/api/v1/auth/login`,
    JSON.stringify(loginPayload),
    {
      headers: { 'Content-Type': 'application/json' },
    }
  );
  
  const loginSuccess = check(loginResponse, {
    'login status is 200': (r) => r.status === 200,
    'login response time < 2s': (r) => r.timings.duration < 2000,
    'login returns access token': (r) => {
      try {
        const body = JSON.parse(r.body);
        return body.access_token !== undefined;
      } catch (e) {
        return false;
      }
    },
  });
  
  if (!loginSuccess) {
    errorRate.add(1);
    return;
  }
  
  const token = JSON.parse(loginResponse.body).access_token;
  const authHeaders = {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`,
  };
  
  sleep(1);
  
  // Test 3: Get user profile
  const profileResponse = http.get(`${BASE_URL}/api/v1/auth/me`, {
    headers: authHeaders,
  });
  
  check(profileResponse, {
    'profile status is 200': (r) => r.status === 200,
    'profile response time < 1s': (r) => r.timings.duration < 1000,
  }) || errorRate.add(1);
  
  sleep(0.5);
  
  // Test 4: Get workloads
  const workloadsResponse = http.get(`${BASE_URL}/api/v1/workloads/`, {
    headers: authHeaders,
  });
  
  check(workloadsResponse, {
    'workloads status is 200': (r) => r.status === 200,
    'workloads response time < 1.5s': (r) => r.timings.duration < 1500,
  }) || errorRate.add(1);
  
  sleep(1);
  
  // Test 5: Create workload (25% chance)
  if (Math.random() < 0.25) {
    const workloadPayload = {
      name: `Load Test Workload ${Date.now()}`,
      description: 'Workload created during load testing',
      code: 'def test_function(n):\n    return sum(range(n))',
      language: 'python',
      complexity_target: 'O(n)',
    };
    
    const createResponse = http.post(
      `${BASE_URL}/api/v1/workloads/`,
      JSON.stringify(workloadPayload),
      { headers: authHeaders }
    );
    
    check(createResponse, {
      'create workload status is 201': (r) => r.status === 201,
      'create workload response time < 3s': (r) => r.timings.duration < 3000,
    }) || errorRate.add(1);
    
    sleep(1);
  }
  
  // Test 6: Get user limits
  const limitsResponse = http.get(`${BASE_URL}/api/v1/auth/limits`, {
    headers: authHeaders,
  });
  
  check(limitsResponse, {
    'limits status is 200': (r) => r.status === 200,
    'limits response time < 1s': (r) => r.timings.duration < 1000,
  }) || errorRate.add(1);
  
  sleep(2);
}

export function teardown(data) {
  console.log('Cleaning up load test environment...');
  
  // Login as admin and clean up test data
  const adminLogin = http.post(
    `${BASE_URL}/api/v1/auth/login`,
    JSON.stringify({
      email: '<EMAIL>',
      password: 'admin123',
    }),
    {
      headers: { 'Content-Type': 'application/json' },
    }
  );
  
  if (adminLogin.status === 200) {
    const adminToken = JSON.parse(adminLogin.body).access_token;
    const adminHeaders = {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${adminToken}`,
    };
    
    // Clean up test workloads
    const cleanupResponse = http.post(
      `${BASE_URL}/api/v1/admin/cleanup/test-data`,
      '{}',
      { headers: adminHeaders }
    );
    
    if (cleanupResponse.status === 200) {
      console.log('Test data cleaned up successfully');
    }
  }
}
