import http from 'k6/http';
import { check, sleep } from 'k6';
import { Rate, Counter, Trend } from 'k6/metrics';

// Custom metrics
const errorRate = new Rate('errors');
const benchmarkSubmissions = new Counter('benchmark_submissions');
const benchmarkCompletions = new Counter('benchmark_completions');
const benchmarkDuration = new Trend('benchmark_duration');

// Test configuration for benchmark system
export const options = {
  scenarios: {
    benchmark_submission: {
      executor: 'ramping-vus',
      startVUs: 0,
      stages: [
        { duration: '30s', target: 2 },
        { duration: '2m', target: 2 },
        { duration: '30s', target: 5 },
        { duration: '2m', target: 5 },
        { duration: '1m', target: 0 },
      ],
    },
    benchmark_monitoring: {
      executor: 'constant-vus',
      vus: 3,
      duration: '5m',
      startTime: '30s',
    },
  },
  thresholds: {
    http_req_duration: ['p(95)<10000'], // Benchmarks can take longer
    http_req_failed: ['rate<0.1'],
    errors: ['rate<0.1'],
    benchmark_duration: ['p(95)<300000'], // 95% of benchmarks under 5 minutes
  },
};

const BASE_URL = __ENV.BASE_URL || 'http://localhost:8000';

// Test workloads for benchmarking
const testWorkloads = [
  {
    name: 'Load Test Linear Search',
    description: 'Simple linear search for load testing',
    code: `def linear_search(arr, target):
    for i, value in enumerate(arr):
        if value == target:
            return i
    return -1`,
    language: 'python',
    complexity_target: 'O(n)',
  },
  {
    name: 'Load Test Bubble Sort',
    description: 'Bubble sort for load testing',
    code: `def bubble_sort(arr):
    n = len(arr)
    for i in range(n):
        for j in range(0, n-i-1):
            if arr[j] > arr[j+1]:
                arr[j], arr[j+1] = arr[j+1], arr[j]
    return arr`,
    language: 'python',
    complexity_target: 'O(n^2)',
  },
  {
    name: 'Load Test Sum Function',
    description: 'Simple sum function for load testing',
    code: `def sum_function(n):
    return sum(range(n))`,
    language: 'python',
    complexity_target: 'O(n)',
  },
];

const benchmarkUsers = [
  { email: '<EMAIL>', password: 'testpass123' },
  { email: '<EMAIL>', password: 'testpass123' },
];

export function setup() {
  console.log('Setting up benchmark load test...');
  
  const setupData = { users: [], workloads: [] };
  
  // Create test users and their workloads
  benchmarkUsers.forEach((user, userIndex) => {
    // Register user
    const registerResponse = http.post(
      `${BASE_URL}/api/v1/auth/register`,
      JSON.stringify({
        username: `benchuser${userIndex + 1}`,
        email: user.email,
        password: user.password,
      }),
      { headers: { 'Content-Type': 'application/json' } }
    );
    
    if (registerResponse.status === 201 || registerResponse.status === 400) {
      // Login to get token
      const loginResponse = http.post(
        `${BASE_URL}/api/v1/auth/login`,
        JSON.stringify({ email: user.email, password: user.password }),
        { headers: { 'Content-Type': 'application/json' } }
      );
      
      if (loginResponse.status === 200) {
        const token = JSON.parse(loginResponse.body).access_token;
        const userData = { ...user, token };
        setupData.users.push(userData);
        
        // Create workloads for this user
        testWorkloads.forEach((workload, workloadIndex) => {
          const workloadPayload = {
            ...workload,
            name: `${workload.name} User${userIndex + 1}`,
          };
          
          const workloadResponse = http.post(
            `${BASE_URL}/api/v1/workloads/`,
            JSON.stringify(workloadPayload),
            {
              headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`,
              },
            }
          );
          
          if (workloadResponse.status === 201) {
            const workloadData = JSON.parse(workloadResponse.body);
            setupData.workloads.push({
              id: workloadData.id,
              userId: userIndex,
              name: workloadData.name,
            });
            console.log(`Created workload: ${workloadData.name}`);
          }
        });
      }
    }
  });
  
  console.log(`Setup complete: ${setupData.users.length} users, ${setupData.workloads.length} workloads`);
  return setupData;
}

export default function (data) {
  const scenario = __ENV.K6_SCENARIO_NAME || 'benchmark_submission';
  
  if (scenario === 'benchmark_submission') {
    testBenchmarkSubmission(data);
  } else if (scenario === 'benchmark_monitoring') {
    testBenchmarkMonitoring(data);
  }
}

function testBenchmarkSubmission(data) {
  if (data.users.length === 0 || data.workloads.length === 0) {
    console.log('No users or workloads available for testing');
    return;
  }
  
  // Select random user and workload
  const user = data.users[Math.floor(Math.random() * data.users.length)];
  const userWorkloads = data.workloads.filter(w => w.userId === data.users.indexOf(user));
  
  if (userWorkloads.length === 0) {
    console.log('No workloads available for user');
    return;
  }
  
  const workload = userWorkloads[Math.floor(Math.random() * userWorkloads.length)];
  
  const authHeaders = {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${user.token}`,
  };
  
  // Test 1: Get workload details
  const workloadResponse = http.get(
    `${BASE_URL}/api/v1/workloads/${workload.id}`,
    { headers: authHeaders }
  );
  
  if (!check(workloadResponse, {
    'workload fetch status is 200': (r) => r.status === 200,
    'workload fetch response time < 2s': (r) => r.timings.duration < 2000,
  })) {
    errorRate.add(1);
    return;
  }
  
  sleep(1);
  
  // Test 2: Submit benchmark job
  const benchmarkPayload = {
    workload_id: workload.id,
    input_sizes: [10, 20, 50], // Small sizes for load testing
    iterations: 2, // Fewer iterations for speed
    timeout: 60, // Short timeout
  };
  
  const benchmarkStartTime = Date.now();
  
  const benchmarkResponse = http.post(
    `${BASE_URL}/api/v1/benchmarks/run`,
    JSON.stringify(benchmarkPayload),
    { headers: authHeaders }
  );
  
  const benchmarkSubmitted = check(benchmarkResponse, {
    'benchmark submission status is 200': (r) => r.status === 200,
    'benchmark submission response time < 5s': (r) => r.timings.duration < 5000,
    'benchmark returns job_id': (r) => {
      try {
        const body = JSON.parse(r.body);
        return body.job_id !== undefined;
      } catch (e) {
        return false;
      }
    },
  });
  
  if (!benchmarkSubmitted) {
    errorRate.add(1);
    return;
  }
  
  benchmarkSubmissions.add(1);
  const jobId = JSON.parse(benchmarkResponse.body).job_id;
  
  sleep(2);
  
  // Test 3: Monitor benchmark progress
  let completed = false;
  let attempts = 0;
  const maxAttempts = 20; // Max 2 minutes of checking
  
  while (!completed && attempts < maxAttempts) {
    const statusResponse = http.get(
      `${BASE_URL}/api/v1/benchmarks/status/${jobId}`,
      { headers: authHeaders }
    );
    
    if (check(statusResponse, {
      'benchmark status check is 200': (r) => r.status === 200,
      'benchmark status response time < 3s': (r) => r.timings.duration < 3000,
    })) {
      try {
        const statusData = JSON.parse(statusResponse.body);
        
        if (statusData.status === 'completed') {
          completed = true;
          const duration = Date.now() - benchmarkStartTime;
          benchmarkDuration.add(duration);
          benchmarkCompletions.add(1);
          
          // Verify results structure
          check(statusResponse, {
            'benchmark has results': (r) => {
              const body = JSON.parse(r.body);
              return body.results && body.results.complexity_analysis;
            },
            'benchmark has performance metrics': (r) => {
              const body = JSON.parse(r.body);
              return body.results && body.results.performance_metrics;
            },
          }) || errorRate.add(1);
          
          console.log(`Benchmark ${jobId} completed in ${duration}ms`);
          
        } else if (statusData.status === 'failed') {
          errorRate.add(1);
          console.log(`Benchmark ${jobId} failed: ${statusData.error || 'Unknown error'}`);
          break;
        }
      } catch (e) {
        errorRate.add(1);
        break;
      }
    } else {
      errorRate.add(1);
    }
    
    attempts++;
    sleep(6); // Check every 6 seconds
  }
  
  if (!completed && attempts >= maxAttempts) {
    console.log(`Benchmark ${jobId} timed out after ${maxAttempts * 6} seconds`);
    errorRate.add(1);
  }
}

function testBenchmarkMonitoring(data) {
  if (data.users.length === 0) {
    return;
  }
  
  const user = data.users[Math.floor(Math.random() * data.users.length)];
  const authHeaders = {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${user.token}`,
  };
  
  // Test benchmark jobs list
  const jobsResponse = http.get(`${BASE_URL}/api/v1/benchmarks/jobs`, {
    headers: authHeaders,
  });
  
  check(jobsResponse, {
    'benchmark jobs status is 200': (r) => r.status === 200,
    'benchmark jobs response time < 2s': (r) => r.timings.duration < 2000,
  }) || errorRate.add(1);
  
  sleep(10);
  
  // Test workloads list
  const workloadsResponse = http.get(`${BASE_URL}/api/v1/workloads/`, {
    headers: authHeaders,
  });
  
  check(workloadsResponse, {
    'workloads list status is 200': (r) => r.status === 200,
    'workloads list response time < 2s': (r) => r.timings.duration < 2000,
  }) || errorRate.add(1);
  
  sleep(15);
}

export function teardown(data) {
  console.log('Benchmark load test completed');
  console.log(`Benchmark submissions: ${benchmarkSubmissions.count}`);
  console.log(`Benchmark completions: ${benchmarkCompletions.count}`);
  
  if (benchmarkSubmissions.count > 0) {
    const completionRate = (benchmarkCompletions.count / benchmarkSubmissions.count) * 100;
    console.log(`Completion rate: ${completionRate.toFixed(2)}%`);
  }
  
  // Cleanup test data
  data.users.forEach(user => {
    // Clean up user's workloads and benchmark jobs
    const cleanupResponse = http.delete(
      `${BASE_URL}/api/v1/admin/cleanup/user-test-data`,
      {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${user.token}`,
        },
      }
    );
    
    if (cleanupResponse.status === 200) {
      console.log(`Cleaned up data for ${user.email}`);
    }
  });
}
