"""
Admin endpoints for system management
"""

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func
from typing import Dict, Any, List

from ...core.database import get_db
from ...core.auth import require_admin
from ...models.user import User
from ...services.retention_service import retention_service
from ...services.analytics_service import analytics_service

router = APIRouter(prefix="/admin", tags=["admin"])


@router.post("/cleanup/all")
async def cleanup_all_users(
    current_user: User = Depends(require_admin),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    Run data cleanup for all users based on their retention policies
    Admin only endpoint
    """
    try:
        cleanup_stats = await retention_service.cleanup_all_users()
        return {
            "message": "Cleanup completed",
            "stats": cleanup_stats
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Cleanup failed: {str(e)}"
        )


@router.post("/cleanup/user/{user_id}")
async def cleanup_user_data(
    user_id: int,
    current_user: User = Depends(require_admin),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    Run data cleanup for a specific user
    Admin only endpoint
    """
    try:
        # Get target user
        from sqlalchemy import select
        user_query = select(User).where(User.id == user_id)
        user_result = await db.execute(user_query)
        target_user = user_result.scalar_one_or_none()

        if not target_user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )

        cleanup_stats = await retention_service.cleanup_user_data(target_user, db)
        return {
            "message": f"Cleanup completed for user {user_id}",
            "stats": cleanup_stats
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Cleanup failed: {str(e)}"
        )


@router.get("/usage/all")
async def get_all_users_usage(
    current_user: User = Depends(require_admin),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    Get usage statistics for all users
    Admin only endpoint
    """
    try:
        from sqlalchemy import select

        # Get all users
        users_query = select(User).where(User.is_active == True)
        users_result = await db.execute(users_query)
        users = users_result.scalars().all()

        usage_stats = []
        for user in users:
            user_stats = await retention_service.get_user_usage_stats(user, db)
            usage_stats.append(user_stats)

        # Calculate summary
        total_users = len(usage_stats)
        plan_distribution = {}
        users_over_limits = 0

        for stats in usage_stats:
            plan = stats["plan_type"]
            plan_distribution[plan] = plan_distribution.get(plan, 0) + 1

            if any(stats["limits_exceeded"].values()):
                users_over_limits += 1

        return {
            "summary": {
                "total_users": total_users,
                "plan_distribution": plan_distribution,
                "users_over_limits": users_over_limits
            },
            "user_stats": usage_stats
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get usage stats: {str(e)}"
        )


@router.get("/usage/user/{user_id}")
async def get_user_usage(
    user_id: int,
    current_user: User = Depends(require_admin),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    Get usage statistics for a specific user
    Admin only endpoint
    """
    try:
        from sqlalchemy import select

        # Get target user
        user_query = select(User).where(User.id == user_id)
        user_result = await db.execute(user_query)
        target_user = user_result.scalar_one_or_none()

        if not target_user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )

        usage_stats = await retention_service.get_user_usage_stats(target_user, db)
        return usage_stats
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get user usage: {str(e)}"
        )


@router.get("/retention-policies")
async def get_retention_policies(
    current_user: User = Depends(require_admin)
) -> Dict[str, Any]:
    """
    Get all retention policies by plan type
    Admin only endpoint
    """
    return {
        "retention_policies": retention_service.RETENTION_POLICIES,
        "description": {
            "benchmark_results": "Days to keep benchmark results (-1 = unlimited)",
            "benchmark_jobs": "Days to keep benchmark jobs (-1 = unlimited)",
            "storage_artifacts": "Days to keep storage artifacts (-1 = unlimited)",
            "max_workloads": "Maximum number of active workloads (-1 = unlimited)",
            "max_jobs_per_month": "Maximum jobs per month (-1 = unlimited)"
        }
    }


@router.get("/analytics/payments")
async def get_payment_analytics(
    current_user: User = Depends(require_admin),
    start_date: str = None,
    end_date: str = None
) -> Dict[str, Any]:
    """
    Get payment analytics for admin dashboard
    """

    try:
        from datetime import datetime

        # Parse dates if provided
        start_dt = datetime.fromisoformat(start_date) if start_date else None
        end_dt = datetime.fromisoformat(end_date) if end_date else None

        analytics = await analytics_service.get_payment_analytics(start_dt, end_dt)

        return {
            "success": True,
            "analytics": analytics
        }

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get payment analytics: {str(e)}"
        )


@router.get("/analytics/dashboard")
async def get_analytics_dashboard(
    current_user: User = Depends(require_admin)
) -> Dict[str, Any]:
    """
    Get comprehensive analytics dashboard for admin
    """

    try:
        dashboard = await analytics_service.get_comprehensive_dashboard()

        return {
            "success": True,
            "dashboard": dashboard
        }

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get analytics dashboard: {str(e)}"
        )


@router.get("/users/all")
async def get_all_users(
    current_user: User = Depends(require_admin),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    Get all users with detailed information for admin management
    """

    try:
        from ...models.workload import Workload
        from ...models.benchmark import BenchmarkJob

        # Get users with counts

        users_query = select(
            User.id,
            User.username,
            User.email,
            User.plan_type,
            User.created_at,
            User.last_login_at,
            User.is_active,
            func.count(Workload.id).label('workloads_count'),
            func.count(BenchmarkJob.id).label('jobs_count')
        ).outerjoin(Workload).outerjoin(BenchmarkJob).group_by(User.id)

        users_result = await db.execute(users_query)
        users_data = users_result.all()

        users = []
        for user_data in users_data:
            users.append({
                "id": user_data.id,
                "username": user_data.username,
                "email": user_data.email,
                "plan_type": user_data.plan_type,
                "created_at": user_data.created_at.isoformat(),
                "last_login": user_data.last_login_at.isoformat() if user_data.last_login_at else None,
                "is_active": user_data.is_active,
                "workloads_count": user_data.workloads_count or 0,
                "jobs_count": user_data.jobs_count or 0
            })

        return {
            "success": True,
            "users": users,
            "total_count": len(users)
        }

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get users: {str(e)}"
        )


@router.post("/users/{user_id}/activate")
async def activate_user(
    user_id: int,
    current_user: User = Depends(require_admin),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    Activate a user account
    """

    try:
        user_query = select(User).where(User.id == user_id)
        user_result = await db.execute(user_query)
        user = user_result.scalar_one_or_none()

        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )

        user.is_active = True
        await db.commit()

        return {
            "success": True,
            "message": f"User {user.username} activated successfully"
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to activate user: {str(e)}"
        )


@router.post("/users/{user_id}/deactivate")
async def deactivate_user(
    user_id: int,
    current_user: User = Depends(require_admin),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    Deactivate a user account
    """

    try:
        user_query = select(User).where(User.id == user_id)
        user_result = await db.execute(user_query)
        user = user_result.scalar_one_or_none()

        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )

        user.is_active = False
        await db.commit()

        return {
            "success": True,
            "message": f"User {user.username} deactivated successfully"
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to deactivate user: {str(e)}"
        )


@router.post("/users/bulk/{action}")
async def bulk_user_action(
    action: str,
    user_ids: List[int],
    current_user: User = Depends(require_admin),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    Perform bulk actions on multiple users
    """

    if action not in ["activate", "deactivate", "delete"]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid action. Must be 'activate', 'deactivate', or 'delete'"
        )

    try:
        users_query = select(User).where(User.id.in_(user_ids))
        users_result = await db.execute(users_query)
        users = users_result.scalars().all()

        if not users:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="No users found with provided IDs"
            )

        affected_count = 0

        for user in users:
            if action == "activate":
                user.is_active = True
                affected_count += 1
            elif action == "deactivate":
                user.is_active = False
                affected_count += 1
            elif action == "delete":
                await db.delete(user)
                affected_count += 1

        await db.commit()

        return {
            "success": True,
            "message": f"Bulk {action} completed successfully",
            "affected_count": affected_count
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to perform bulk action: {str(e)}"
        )
