version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: exoPiper
      POSTGRES_USER: exoPiper
      POSTGRES_PASSWORD: exoPiper_dev
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/sql/init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U exoPiper"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis for Celery
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # MinIO for S3-compatible storage
  minio:
    image: minio/minio:latest
    command: server /data --console-address ":9001"
    environment:
      MINIO_ROOT_USER: exoPiper
      MINIO_ROOT_PASSWORD: exoPiper_dev
    volumes:
      - minio_data:/data
    ports:
      - "9000:9000"
      - "9001:9001"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 10s
      retries: 3

  # FastAPI Backend
  backend:
    build: ./backend
    environment:
      DATABASE_URL: ************************************************/exoPiper
      REDIS_URL: redis://redis:6379/0
      MINIO_ENDPOINT: minio:9000
      MINIO_ACCESS_KEY: exoPiper
      MINIO_SECRET_KEY: exoPiper_dev
      SECRET_KEY: your-secret-key-change-in-production
      ENVIRONMENT: development
    ports:
      - "8000:8000"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      minio:
        condition: service_healthy
    volumes:
      - ./backend:/app
    command: uvicorn main:app --host 0.0.0.0 --port 8000 --reload

  # Celery Worker
  celery-worker:
    build: ./backend
    environment:
      DATABASE_URL: ************************************************/exoPiper
      REDIS_URL: redis://redis:6379/0
      MINIO_ENDPOINT: minio:9000
      MINIO_ACCESS_KEY: exoPiper
      MINIO_SECRET_KEY: exoPiper_dev
      SECRET_KEY: your-secret-key-change-in-production
      ENVIRONMENT: development
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - ./backend:/app
      - /var/run/docker.sock:/var/run/docker.sock
    command: celery -A app.core.celery_app worker --loglevel=info --queues=benchmarks,analysis,cleanup,notifications

  # Celery Beat (Scheduler)
  celery-beat:
    build: ./backend
    environment:
      DATABASE_URL: ************************************************/exoPiper
      REDIS_URL: redis://redis:6379/0
      SECRET_KEY: your-secret-key-change-in-production
      ENVIRONMENT: development
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - ./backend:/app
    command: celery -A app.core.celery_app beat --loglevel=info

  # Celery Worker for Payments
  celery-payments:
    build: ./backend
    environment:
      DATABASE_URL: ************************************************/exoPiper
      REDIS_URL: redis://redis:6379/0
      SECRET_KEY: your-secret-key-change-in-production
      ENVIRONMENT: development
      BSCSCAN_API_KEY: ${BSCSCAN_API_KEY:-YourApiKeyToken}
      ETHERSCAN_API_KEY: ${ETHERSCAN_API_KEY:-YourApiKeyToken}
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - ./backend:/app
    command: celery -A app.core.celery_app worker --loglevel=info --queues=payments --concurrency=2

  # Next.js Frontend
  frontend:
    build: ./frontend
    ports:
      - "3000:3000"
    environment:
      NEXT_PUBLIC_API_URL: http://localhost:8000
    volumes:
      - ./frontend:/app
      - /app/node_modules
    command: npm run dev

volumes:
  postgres_data:
  minio_data:

networks:
  default:
    name: exoPiper-network
