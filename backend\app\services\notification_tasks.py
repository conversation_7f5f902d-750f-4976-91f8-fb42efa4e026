"""
Notification tasks for alerts and reports
"""

import asyncio
import smtplib
import requests
from email.mime.text import MIMEText
from email.mime.multipart import MIME<PERSON><PERSON><PERSON>art
from typing import Dict, Any
from datetime import datetime

from ..core.celery_app import celery_app
from ..core.config import settings
from ..core.database import Async<PERSON><PERSON><PERSON><PERSON>oc<PERSON>
from ..models.user import User
from ..models.workload import Workload


@celery_app.task
def send_performance_alert(
    user_id: int,
    workload_id: int,
    alert_type: str,
    current_value: float,
    previous_value: float,
    delta: float
):
    """Send performance alert to user"""
    
    try:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        loop.run_until_complete(_send_alert(
            user_id, workload_id, alert_type, current_value, previous_value, delta
        ))
        loop.close()
        
    except Exception as e:
        print(f"Error sending alert: {e}")


async def _send_alert(
    user_id: int,
    workload_id: int,
    alert_type: str,
    current_value: float,
    previous_value: float,
    delta: float
):
    """Send alert implementation"""
    
    async with Async<PERSON>essionLocal() as db:
        # Get user and workload info
        user = await db.get(User, user_id)
        workload = await db.get(Workload, workload_id)
        
        if not user or not workload:
            return
        
        # Prepare alert message
        alert_message = _format_alert_message(
            alert_type, workload.name, current_value, previous_value, delta
        )
        
        # Send email if configured
        if settings.SMTP_HOST and user.email:
            await _send_email_alert(user.email, alert_message)
        
        # Send Slack notification if configured
        if settings.SLACK_WEBHOOK_URL:
            await _send_slack_alert(alert_message)


def _format_alert_message(
    alert_type: str,
    workload_name: str,
    current_value: float,
    previous_value: float,
    delta: float
) -> Dict[str, str]:
    """Format alert message"""
    
    if alert_type == "p_exponent_regression":
        subject = f"🚨 Performance Regression Detected - {workload_name}"
        message = f"""
Performance regression detected in workload: {workload_name}

Algorithm Complexity Change:
- Previous p exponent: {previous_value:.3f}
- Current p exponent: {current_value:.3f}
- Change (Δp): +{delta:.3f}

This indicates that the algorithm's complexity has increased, which may lead to:
- Slower execution times for larger inputs
- Reduced scalability
- Potential performance bottlenecks

Recommended Actions:
1. Review recent code changes
2. Check for algorithmic regressions
3. Analyze input data characteristics
4. Consider optimization opportunities

View detailed analysis: {settings.FRONTEND_URL}/workloads/{workload_name}
        """
    else:
        subject = f"🔔 Performance Alert - {workload_name}"
        message = f"""
Performance alert for workload: {workload_name}
Alert type: {alert_type}
Current value: {current_value}
Previous value: {previous_value}
Change: {delta}
        """
    
    return {
        "subject": subject,
        "message": message.strip()
    }


async def _send_email_alert(email: str, alert_data: Dict[str, str]):
    """Send email alert"""
    
    try:
        msg = MIMEMultipart()
        msg['From'] = settings.SMTP_USER
        msg['To'] = email
        msg['Subject'] = alert_data['subject']
        
        msg.attach(MIMEText(alert_data['message'], 'plain'))
        
        server = smtplib.SMTP(settings.SMTP_HOST, settings.SMTP_PORT)
        server.starttls()
        server.login(settings.SMTP_USER, settings.SMTP_PASSWORD)
        
        text = msg.as_string()
        server.sendmail(settings.SMTP_USER, email, text)
        server.quit()
        
        print(f"Email alert sent to {email}")
        
    except Exception as e:
        print(f"Failed to send email alert: {e}")


async def _send_slack_alert(alert_data: Dict[str, str]):
    """Send Slack alert"""
    
    try:
        payload = {
            "text": alert_data['subject'],
            "blocks": [
                {
                    "type": "section",
                    "text": {
                        "type": "mrkdwn",
                        "text": f"*{alert_data['subject']}*"
                    }
                },
                {
                    "type": "section",
                    "text": {
                        "type": "mrkdwn",
                        "text": f"```{alert_data['message']}```"
                    }
                }
            ]
        }
        
        response = requests.post(settings.SLACK_WEBHOOK_URL, json=payload)
        response.raise_for_status()
        
        print("Slack alert sent successfully")
        
    except Exception as e:
        print(f"Failed to send Slack alert: {e}")


@celery_app.task
def send_weekly_report(user_id: int):
    """Send weekly performance report"""
    
    try:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        loop.run_until_complete(_generate_weekly_report(user_id))
        loop.close()
        
    except Exception as e:
        print(f"Error generating weekly report: {e}")


async def _generate_weekly_report(user_id: int):
    """Generate and send weekly report"""
    
    async with AsyncSessionLocal() as db:
        user = await db.get(User, user_id)
        if not user:
            return
        
        # TODO: Implement weekly report generation
        # This would include:
        # - Summary of all workloads
        # - Performance trends
        # - Complexity analysis summaries
        # - Recommendations
        
        report_data = {
            "subject": f"📊 Weekly Performance Report - {datetime.now().strftime('%Y-%m-%d')}",
            "message": f"""
Weekly Performance Report for {user.email}

This feature is coming soon!

Summary:
- Total workloads: TBD
- Benchmarks executed: TBD
- Performance trends: TBD
- Alerts triggered: TBD

Best regards,
Exo Piper Team
            """
        }
        
        if settings.SMTP_HOST:
            await _send_email_alert(user.email, report_data)
