"""
Integration tests for Exo Piper system
Tests the complete flow from API to database
"""

import pytest
import asyncio
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from app.main import app
from app.core.database import get_db
from app.models.user import User
from app.models.workload import Workload
from app.models.payment import Payment, PaymentStatus
from app.models.benchmark import BenchmarkJob, JobStatus
from .conftest import test_db, test_user, auth_headers


class TestUserWorkflow:
    """Test complete user workflow"""
    
    @pytest.mark.asyncio
    async def test_user_registration_and_login(self):
        """Test user registration and login flow"""
        async with AsyncClient(app=app, base_url="http://test") as client:
            # Register new user
            register_data = {
                "username": "integration_user",
                "email": "<EMAIL>",
                "password": "testpassword123"
            }
            
            response = await client.post("/api/v1/auth/register", json=register_data)
            assert response.status_code == 201
            
            user_data = response.json()
            assert user_data["username"] == "integration_user"
            assert user_data["email"] == "<EMAIL>"
            assert user_data["plan_type"] == "free"
            
            # Login with new user
            login_data = {
                "email": "<EMAIL>",
                "password": "testpassword123"
            }
            
            response = await client.post("/api/v1/auth/login", json=login_data)
            assert response.status_code == 200
            
            login_response = response.json()
            assert "access_token" in login_response
            assert login_response["token_type"] == "bearer"
            
            # Verify token works
            headers = {"Authorization": f"Bearer {login_response['access_token']}"}
            response = await client.get("/api/v1/auth/me", headers=headers)
            assert response.status_code == 200
            
            me_data = response.json()
            assert me_data["username"] == "integration_user"
    
    @pytest.mark.asyncio
    async def test_workload_crud_flow(self, test_user, auth_headers):
        """Test complete workload CRUD operations"""
        async with AsyncClient(app=app, base_url="http://test") as client:
            # Create workload
            workload_data = {
                "name": "Integration Test Workload",
                "description": "Test workload for integration testing",
                "code": "def test_function():\n    return sum(range(1000))",
                "language": "python",
                "complexity_target": "O(n)"
            }
            
            response = await client.post(
                "/api/v1/workloads/", 
                json=workload_data, 
                headers=auth_headers
            )
            assert response.status_code == 201
            
            workload = response.json()
            workload_id = workload["id"]
            assert workload["name"] == workload_data["name"]
            assert workload["user_id"] == test_user.id
            
            # Read workload
            response = await client.get(f"/api/v1/workloads/{workload_id}", headers=auth_headers)
            assert response.status_code == 200
            
            retrieved_workload = response.json()
            assert retrieved_workload["id"] == workload_id
            assert retrieved_workload["name"] == workload_data["name"]
            
            # Update workload
            update_data = {
                "name": "Updated Integration Test Workload",
                "description": "Updated description"
            }
            
            response = await client.put(
                f"/api/v1/workloads/{workload_id}", 
                json=update_data, 
                headers=auth_headers
            )
            assert response.status_code == 200
            
            updated_workload = response.json()
            assert updated_workload["name"] == update_data["name"]
            assert updated_workload["description"] == update_data["description"]
            
            # List workloads
            response = await client.get("/api/v1/workloads/", headers=auth_headers)
            assert response.status_code == 200
            
            workloads = response.json()
            assert len(workloads) >= 1
            assert any(w["id"] == workload_id for w in workloads)
            
            # Delete workload
            response = await client.delete(f"/api/v1/workloads/{workload_id}", headers=auth_headers)
            assert response.status_code == 204
            
            # Verify deletion
            response = await client.get(f"/api/v1/workloads/{workload_id}", headers=auth_headers)
            assert response.status_code == 404


class TestBenchmarkWorkflow:
    """Test benchmark execution workflow"""
    
    @pytest.mark.asyncio
    async def test_benchmark_execution_flow(self, test_user, auth_headers, test_db):
        """Test complete benchmark execution flow"""
        async with AsyncClient(app=app, base_url="http://test") as client:
            # Create workload first
            workload_data = {
                "name": "Benchmark Test Workload",
                "description": "Workload for benchmark testing",
                "code": "def fibonacci(n):\n    if n <= 1:\n        return n\n    return fibonacci(n-1) + fibonacci(n-2)",
                "language": "python",
                "complexity_target": "O(2^n)"
            }
            
            response = await client.post(
                "/api/v1/workloads/", 
                json=workload_data, 
                headers=auth_headers
            )
            assert response.status_code == 201
            workload = response.json()
            workload_id = workload["id"]
            
            # Run benchmark
            benchmark_data = {
                "workload_id": workload_id,
                "input_sizes": [10, 15, 20],
                "iterations": 3
            }
            
            response = await client.post(
                "/api/v1/benchmarks/run", 
                json=benchmark_data, 
                headers=auth_headers
            )
            assert response.status_code == 202
            
            job_response = response.json()
            job_id = job_response["job_id"]
            assert "job_id" in job_response
            
            # Check job status
            response = await client.get(f"/api/v1/benchmarks/status/{job_id}", headers=auth_headers)
            assert response.status_code == 200
            
            status_response = response.json()
            assert status_response["job_id"] == job_id
            assert status_response["status"] in ["pending", "running", "completed", "failed"]
            
            # Verify job was created in database
            async with test_db() as db:
                job_query = select(BenchmarkJob).where(BenchmarkJob.id == job_id)
                job_result = await db.execute(job_query)
                job = job_result.scalar_one_or_none()
                
                assert job is not None
                assert job.user_id == test_user.id
                assert job.workload_id == workload_id


class TestPaymentWorkflow:
    """Test payment system workflow"""
    
    @pytest.mark.asyncio
    async def test_payment_creation_flow(self, test_user, auth_headers):
        """Test payment creation and status checking"""
        async with AsyncClient(app=app, base_url="http://test") as client:
            # Get available plans
            response = await client.get("/api/v1/payments/plans", headers=auth_headers)
            assert response.status_code == 200
            
            plans_data = response.json()
            assert "plans" in plans_data
            assert "payment_networks" in plans_data
            
            # Create payment request
            payment_data = {
                "plan_type": "basic",
                "network": "bep20"
            }
            
            response = await client.post(
                "/api/v1/payments/create", 
                json=payment_data, 
                headers=auth_headers
            )
            assert response.status_code == 200
            
            payment_response = response.json()
            assert payment_response["success"] is True
            assert "payment" in payment_response
            
            payment = payment_response["payment"]
            payment_id = payment["payment_id"]
            assert payment["amount"] == "50.000000"  # Basic plan price
            assert payment["currency"] == "USDT"
            assert payment["network"] == "BEP20"
            assert "qr_code" in payment
            
            # Check payment status
            response = await client.get(f"/api/v1/payments/status/{payment_id}", headers=auth_headers)
            assert response.status_code == 200
            
            status_response = response.json()
            assert status_response["status"] == "pending"
            
            # Get payment history
            response = await client.get("/api/v1/payments/history", headers=auth_headers)
            assert response.status_code == 200
            
            history_response = response.json()
            assert history_response["success"] is True
            assert len(history_response["payments"]) >= 1
            
            # Verify payment exists in history
            payment_found = any(p["id"] == payment_id for p in history_response["payments"])
            assert payment_found


class TestAdminWorkflow:
    """Test admin functionality workflow"""
    
    @pytest.mark.asyncio
    async def test_admin_analytics_flow(self, test_user, auth_headers):
        """Test admin analytics endpoints"""
        # Note: This test assumes the user has admin privileges
        # In a real scenario, you'd create an admin user
        
        async with AsyncClient(app=app, base_url="http://test") as client:
            # Test payment analytics
            response = await client.get("/api/v1/admin/analytics/payments", headers=auth_headers)
            # May return 403 if user is not admin, which is expected
            assert response.status_code in [200, 403]
            
            if response.status_code == 200:
                analytics_data = response.json()
                assert "analytics" in analytics_data
                assert "overview" in analytics_data["analytics"]
            
            # Test usage statistics
            response = await client.get("/api/v1/admin/usage/all", headers=auth_headers)
            assert response.status_code in [200, 403]
            
            if response.status_code == 200:
                usage_data = response.json()
                assert "user_stats" in usage_data or "summary" in usage_data


class TestSystemIntegration:
    """Test system-wide integration scenarios"""
    
    @pytest.mark.asyncio
    async def test_user_limits_enforcement(self, test_user, auth_headers, test_db):
        """Test that user limits are properly enforced"""
        async with AsyncClient(app=app, base_url="http://test") as client:
            # Check current limits
            response = await client.get("/api/v1/auth/limits", headers=auth_headers)
            assert response.status_code == 200
            
            limits_data = response.json()
            assert "workloads" in limits_data
            assert "monthly_jobs" in limits_data
            
            # Try to create workloads up to the limit
            workload_limit = limits_data["workloads"]["limit"]
            
            if workload_limit > 0:  # If not unlimited
                created_workloads = []
                
                for i in range(min(workload_limit + 1, 5)):  # Don't create too many in tests
                    workload_data = {
                        "name": f"Limit Test Workload {i}",
                        "description": f"Test workload {i}",
                        "code": f"def test_{i}():\n    return {i}",
                        "language": "python"
                    }
                    
                    response = await client.post(
                        "/api/v1/workloads/", 
                        json=workload_data, 
                        headers=auth_headers
                    )
                    
                    if i < workload_limit:
                        assert response.status_code == 201
                        created_workloads.append(response.json()["id"])
                    else:
                        # Should fail when exceeding limit
                        assert response.status_code == 400
                
                # Clean up created workloads
                for workload_id in created_workloads:
                    await client.delete(f"/api/v1/workloads/{workload_id}", headers=auth_headers)
    
    @pytest.mark.asyncio
    async def test_data_retention_integration(self, test_user, auth_headers, test_db):
        """Test data retention policies integration"""
        async with AsyncClient(app=app, base_url="http://test") as client:
            # Check user usage
            response = await client.get("/api/v1/auth/usage", headers=auth_headers)
            assert response.status_code == 200
            
            usage_data = response.json()
            assert "usage" in usage_data
            assert "plan_type" in usage_data
            
            # Verify retention policies are applied
            plan_type = usage_data["plan_type"]
            assert plan_type in ["free", "basic", "pro", "enterprise"]
    
    @pytest.mark.asyncio
    async def test_error_handling_integration(self, auth_headers):
        """Test system error handling"""
        async with AsyncClient(app=app, base_url="http://test") as client:
            # Test invalid workload ID
            response = await client.get("/api/v1/workloads/99999", headers=auth_headers)
            assert response.status_code == 404
            
            # Test invalid payment ID
            response = await client.get("/api/v1/payments/status/99999", headers=auth_headers)
            assert response.status_code == 404
            
            # Test invalid benchmark job ID
            response = await client.get("/api/v1/benchmarks/status/99999", headers=auth_headers)
            assert response.status_code == 404
            
            # Test malformed requests
            response = await client.post("/api/v1/workloads/", json={}, headers=auth_headers)
            assert response.status_code == 422  # Validation error


# Run integration tests
if __name__ == "__main__":
    pytest.main([__file__, "-v"])
