"""
Docker service for running benchmarks in containers
"""

import docker
import json
import asyncio
import time
from typing import Dict, List, Any
from ..core.config import settings


class DockerBenchmarkService:
    def __init__(self):
        self.client = docker.from_env()
        self.benchmark_image = settings.BENCHMARK_IMAGE

    async def run_benchmark(
        self,
        workload_type: str,
        config: Dict[str, Any],
        job_id: str
    ) -> Dict[str, Any]:
        """Run benchmark in Docker container using dedicated agent"""

        # Prepare environment variables
        env_vars = {
            'WORKLOAD_TYPE': workload_type,
            'WORKLOAD_CONFIG': json.dumps(config),
            'JOB_ID': job_id
        }

        try:
            # Try to use dedicated agent image first, fallback to inline scripts
            try:
                image_name = 'exopiper/agent:latest'
                # Check if image exists
                self.client.images.get(image_name)
                use_agent = True
            except docker.errors.ImageNotFound:
                image_name = 'python:3.11-slim'
                use_agent = False

            if use_agent:
                # Use dedicated agent
                container = self.client.containers.run(
                    image=image_name,
                    environment=env_vars,
                    detach=True,
                    remove=True,
                    mem_limit='2g',
                    cpu_count=2,
                    network_mode='bridge'
                )
            else:
                # Fallback to inline scripts
                command = self._get_benchmark_command(workload_type)
                container = self.client.containers.run(
                    image=image_name,
                    command=command,
                    environment=env_vars,
                    detach=True,
                    remove=True,
                    mem_limit='2g',
                    cpu_count=2,
                    network_mode='bridge'
                )

            # Wait for completion
            result = container.wait()
            logs = container.logs().decode('utf-8')

            if result['StatusCode'] != 0:
                raise Exception(f"Benchmark failed: {logs}")

            # Parse results based on agent type
            if use_agent:
                return self._parse_agent_output(logs)
            else:
                return self._parse_legacy_results(logs)

        except docker.errors.ContainerError as e:
            raise Exception(f"Container error: {e}")
        except docker.errors.ImageNotFound as e:
            raise Exception(f"Image not found: {e}")
        except Exception as e:
            raise Exception(f"Benchmark execution failed: {e}")

    def _get_benchmark_command(self, workload_type: str) -> List[str]:
        """Get command for specific benchmark type"""

        if workload_type == 'mergesort':
            return self._get_mergesort_command()
        elif workload_type == '3sat':
            return self._get_3sat_command()
        elif workload_type == 'vec2vec':
            return self._get_vec2vec_command()
        elif workload_type == 'mlmodel':
            return self._get_mlmodel_command()
        else:
            raise ValueError(f"Unknown workload type: {workload_type}")

    def _get_mergesort_command(self) -> List[str]:
        """Enhanced mergesort benchmark with overhead measurement"""
        return [
            'python', '-c', '''
import time
import random
import json
import os
import gc
import psutil
import threading

def measure_overhead():
    """Measure system overhead"""
    overhead_times = []
    for _ in range(100):
        start = time.perf_counter_ns()
        # Minimal operation
        x = 1 + 1
        end = time.perf_counter_ns()
        overhead_times.append(end - start)
    return sum(overhead_times) / len(overhead_times)

def get_hardware_info():
    """Get hardware information"""
    return {
        "cpu_count": psutil.cpu_count(),
        "cpu_freq": psutil.cpu_freq()._asdict() if psutil.cpu_freq() else None,
        "memory_total": psutil.virtual_memory().total,
        "python_version": "3.11"
    }

def mergesort(arr):
    if len(arr) <= 1:
        return arr

    mid = len(arr) // 2
    left = mergesort(arr[:mid])
    right = mergesort(arr[mid:])

    return merge(left, right)

def merge(left, right):
    result = []
    i = j = 0

    while i < len(left) and j < len(right):
        if left[i] <= right[j]:
            result.append(left[i])
            i += 1
        else:
            result.append(right[j])
            j += 1

    result.extend(left[i:])
    result.extend(right[j:])
    return result

# Load configuration
config = json.loads(os.environ["WORKLOAD_CONFIG"])
max_size = config.get("max_size", 100000)
iterations = config.get("iterations", 5)
step_factor = config.get("step_factor", 2)

# Measure overhead
overhead_ns = measure_overhead()
hardware_info = get_hardware_info()

results = []
size = 100

while size <= max_size:
    times = []
    memory_usage = []

    for iteration in range(iterations):
        # Force garbage collection
        gc.collect()

        # Generate test data
        arr = [random.randint(1, 10000) for _ in range(size)]

        # Measure memory before
        process = psutil.Process()
        mem_before = process.memory_info().rss / 1024 / 1024  # MB

        # Run benchmark
        start_time = time.perf_counter_ns()
        sorted_arr = mergesort(arr)
        end_time = time.perf_counter_ns()

        # Measure memory after
        mem_after = process.memory_info().rss / 1024 / 1024  # MB

        execution_time = end_time - start_time
        times.append(execution_time)
        memory_usage.append(mem_after - mem_before)

    # Calculate statistics
    avg_time = sum(times) / len(times)
    clean_time = max(0, avg_time - overhead_ns)
    avg_memory = sum(memory_usage) / len(memory_usage)

    results.append({
        "input_size": size,
        "execution_time_ns": int(avg_time),
        "overhead_time_ns": int(overhead_ns),
        "clean_time_ns": int(clean_time),
        "hardware_type": "CPU",
        "hardware_info": hardware_info,
        "memory_usage_mb": round(avg_memory, 2),
        "iterations": iterations,
        "metadata": {
            "algorithm": "mergesort",
            "data_type": "random_integers",
            "min_time_ns": int(min(times)),
            "max_time_ns": int(max(times))
        }
    })

    size = int(size * step_factor)

print("BENCHMARK_RESULTS_START")
print(json.dumps(results, indent=2))
print("BENCHMARK_RESULTS_END")
'''
        ]

    def _get_3sat_command(self) -> List[str]:
        """Enhanced 3-SAT benchmark"""
        return [
            'python', '-c', '''
import time
import random
import json
import os
import gc
import psutil

def measure_overhead():
    overhead_times = []
    for _ in range(100):
        start = time.perf_counter_ns()
        x = 1 + 1
        end = time.perf_counter_ns()
        overhead_times.append(end - start)
    return sum(overhead_times) / len(overhead_times)

def get_hardware_info():
    return {
        "cpu_count": psutil.cpu_count(),
        "cpu_freq": psutil.cpu_freq()._asdict() if psutil.cpu_freq() else None,
        "memory_total": psutil.virtual_memory().total,
        "python_version": "3.11"
    }

def generate_3sat_instance(num_vars, num_clauses):
    clauses = []
    for _ in range(num_clauses):
        clause = []
        for _ in range(3):
            var = random.randint(1, num_vars)
            if random.random() < 0.5:
                var = -var
            clause.append(var)
        clauses.append(clause)
    return clauses

def solve_3sat_bruteforce(clauses, num_vars):
    for assignment in range(min(2**num_vars, 2**20)):  # Limit to prevent infinite loops
        if evaluate_assignment(clauses, assignment, num_vars):
            return True
    return False

def evaluate_assignment(clauses, assignment, num_vars):
    for clause in clauses:
        satisfied = False
        for literal in clause:
            var_idx = abs(literal) - 1
            if var_idx >= num_vars:
                continue
            var_value = (assignment >> var_idx) & 1
            if (literal > 0 and var_value) or (literal < 0 and not var_value):
                satisfied = True
                break
        if not satisfied:
            return False
    return True

# Load configuration
config = json.loads(os.environ["WORKLOAD_CONFIG"])
max_vars = min(config.get("max_variables", 20), 20)  # Limit for safety
iterations = config.get("iterations", 3)
clause_ratio = config.get("clause_ratio", 4.3)

# Measure overhead
overhead_ns = measure_overhead()
hardware_info = get_hardware_info()

results = []
num_vars = 5

while num_vars <= max_vars:
    num_clauses = int(num_vars * clause_ratio)
    times = []

    for iteration in range(iterations):
        gc.collect()

        # Generate problem instance
        clauses = generate_3sat_instance(num_vars, num_clauses)

        # Measure memory before
        process = psutil.Process()
        mem_before = process.memory_info().rss / 1024 / 1024

        # Run benchmark
        start_time = time.perf_counter_ns()
        result = solve_3sat_bruteforce(clauses, num_vars)
        end_time = time.perf_counter_ns()

        # Measure memory after
        mem_after = process.memory_info().rss / 1024 / 1024

        execution_time = end_time - start_time
        times.append(execution_time)

    avg_time = sum(times) / len(times)
    clean_time = max(0, avg_time - overhead_ns)

    results.append({
        "input_size": num_vars,
        "execution_time_ns": int(avg_time),
        "overhead_time_ns": int(overhead_ns),
        "clean_time_ns": int(clean_time),
        "hardware_type": "CPU",
        "hardware_info": hardware_info,
        "memory_usage_mb": round(mem_after - mem_before, 2),
        "iterations": iterations,
        "metadata": {
            "algorithm": "3sat_bruteforce",
            "num_clauses": num_clauses,
            "clause_ratio": clause_ratio,
            "min_time_ns": int(min(times)),
            "max_time_ns": int(max(times))
        }
    })

    num_vars += 2

print("BENCHMARK_RESULTS_START")
print(json.dumps(results, indent=2))
print("BENCHMARK_RESULTS_END")
'''
        ]

    def _get_vec2vec_command(self) -> List[str]:
        """Vector operations benchmark"""
        return [
            'python', '-c', '''
import time
import random
import json
import os
import gc
import psutil
import math

def measure_overhead():
    overhead_times = []
    for _ in range(100):
        start = time.perf_counter_ns()
        x = 1 + 1
        end = time.perf_counter_ns()
        overhead_times.append(end - start)
    return sum(overhead_times) / len(overhead_times)

def dot_product(a, b):
    return sum(x * y for x, y in zip(a, b))

def vector_norm(a):
    return math.sqrt(sum(x * x for x in a))

def matrix_multiply(a, b, n):
    result = [[0] * n for _ in range(n)]
    for i in range(n):
        for j in range(n):
            for k in range(n):
                result[i][j] += a[i][k] * b[k][j]
    return result

# Load configuration
config = json.loads(os.environ["WORKLOAD_CONFIG"])
max_dim = config.get("max_dimension", 1000)
iterations = config.get("iterations", 10)
operations = config.get("operations", ["dot", "norm", "matmul"])

overhead_ns = measure_overhead()
hardware_info = {
    "cpu_count": psutil.cpu_count(),
    "memory_total": psutil.virtual_memory().total
}

results = []
dim = 10

while dim <= max_dim:
    for op in operations:
        times = []

        for iteration in range(iterations):
            gc.collect()

            if op in ["dot", "norm"]:
                # Generate vectors
                vec_a = [random.random() for _ in range(dim)]
                vec_b = [random.random() for _ in range(dim)]

                start_time = time.perf_counter_ns()
                if op == "dot":
                    result = dot_product(vec_a, vec_b)
                else:  # norm
                    result = vector_norm(vec_a)
                end_time = time.perf_counter_ns()

            elif op == "matmul":
                # Generate matrices (smaller for matrix multiply)
                mat_dim = min(dim, 100)  # Limit matrix size
                mat_a = [[random.random() for _ in range(mat_dim)] for _ in range(mat_dim)]
                mat_b = [[random.random() for _ in range(mat_dim)] for _ in range(mat_dim)]

                start_time = time.perf_counter_ns()
                result = matrix_multiply(mat_a, mat_b, mat_dim)
                end_time = time.perf_counter_ns()

            times.append(end_time - start_time)

        avg_time = sum(times) / len(times)
        clean_time = max(0, avg_time - overhead_ns)

        results.append({
            "input_size": dim,
            "execution_time_ns": int(avg_time),
            "overhead_time_ns": int(overhead_ns),
            "clean_time_ns": int(clean_time),
            "hardware_type": "CPU",
            "hardware_info": hardware_info,
            "iterations": iterations,
            "metadata": {
                "algorithm": f"vec2vec_{op}",
                "operation": op,
                "dimension": dim,
                "min_time_ns": int(min(times)),
                "max_time_ns": int(max(times))
            }
        })

    dim = int(dim * 1.5)

print("BENCHMARK_RESULTS_START")
print(json.dumps(results, indent=2))
print("BENCHMARK_RESULTS_END")
'''
        ]

    def _get_mlmodel_command(self) -> List[str]:
        """ML model inference benchmark"""
        return [
            'python', '-c', '''
import time
import random
import json
import os
import gc
import psutil

def measure_overhead():
    overhead_times = []
    for _ in range(100):
        start = time.perf_counter_ns()
        x = 1 + 1
        end = time.perf_counter_ns()
        overhead_times.append(end - start)
    return sum(overhead_times) / len(overhead_times)

def linear_model_inference(features, weights, bias):
    return sum(f * w for f, w in zip(features, weights)) + bias

def decision_tree_inference(features, depth=5):
    # Simplified decision tree simulation
    result = 0
    for i in range(depth):
        if i < len(features):
            if features[i] > 0.5:
                result += features[i]
            else:
                result -= features[i]
    return result

def neural_network_inference(features, hidden_size=100):
    # Simplified neural network simulation
    # Hidden layer
    hidden = []
    for i in range(hidden_size):
        neuron_sum = sum(f * random.random() for f in features)
        hidden.append(max(0, neuron_sum))  # ReLU activation

    # Output layer
    output = sum(h * random.random() for h in hidden)
    return output

# Load configuration
config = json.loads(os.environ["WORKLOAD_CONFIG"])
model_types = config.get("model_types", ["linear", "tree", "neural"])
max_features = config.get("max_features", 1000)
iterations = config.get("iterations", 5)

overhead_ns = measure_overhead()
hardware_info = {
    "cpu_count": psutil.cpu_count(),
    "memory_total": psutil.virtual_memory().total
}

results = []
num_features = 10

while num_features <= max_features:
    for model_type in model_types:
        times = []

        for iteration in range(iterations):
            gc.collect()

            # Generate features
            features = [random.random() for _ in range(num_features)]

            start_time = time.perf_counter_ns()

            if model_type == "linear":
                weights = [random.random() for _ in range(num_features)]
                bias = random.random()
                result = linear_model_inference(features, weights, bias)
            elif model_type == "tree":
                result = decision_tree_inference(features)
            elif model_type == "neural":
                result = neural_network_inference(features)

            end_time = time.perf_counter_ns()
            times.append(end_time - start_time)

        avg_time = sum(times) / len(times)
        clean_time = max(0, avg_time - overhead_ns)

        results.append({
            "input_size": num_features,
            "execution_time_ns": int(avg_time),
            "overhead_time_ns": int(overhead_ns),
            "clean_time_ns": int(clean_time),
            "hardware_type": "CPU",
            "hardware_info": hardware_info,
            "iterations": iterations,
            "metadata": {
                "algorithm": f"ml_{model_type}",
                "model_type": model_type,
                "num_features": num_features,
                "min_time_ns": int(min(times)),
                "max_time_ns": int(max(times))
            }
        })

    num_features = int(num_features * 2)

print("BENCHMARK_RESULTS_START")
print(json.dumps(results, indent=2))
print("BENCHMARK_RESULTS_END")
'''
        ]

    def _parse_agent_output(self, logs: str) -> Dict[str, Any]:
        """Parse output from dedicated agent"""

        # Look for JSON output in logs
        start_marker = "JSON_OUTPUT_START"
        end_marker = "JSON_OUTPUT_END"

        start_idx = logs.find(start_marker)
        end_idx = logs.find(end_marker)

        if start_idx == -1 or end_idx == -1:
            # Fallback: try to parse entire log as JSON
            try:
                return json.loads(logs)
            except:
                raise Exception("Could not find or parse agent output in logs")

        # Extract JSON content
        json_content = logs[start_idx + len(start_marker):end_idx].strip()

        try:
            output = json.loads(json_content)
            return output
        except json.JSONDecodeError as e:
            raise Exception(f"Failed to parse agent output: {e}")

    def _parse_legacy_results(self, logs: str) -> Dict[str, Any]:
        """Parse results from legacy inline scripts"""

        lines = logs.strip().split('\n')
        start_idx = None
        end_idx = None

        for i, line in enumerate(lines):
            if line.strip() == "BENCHMARK_RESULTS_START":
                start_idx = i + 1
            elif line.strip() == "BENCHMARK_RESULTS_END":
                end_idx = i
                break

        if start_idx is None or end_idx is None:
            raise Exception("Could not find benchmark results in logs")

        results_json = '\n'.join(lines[start_idx:end_idx])

        try:
            results = json.loads(results_json)
            results_list = results if isinstance(results, list) else [results]

            # Convert to agent format
            return {
                "status": "success",
                "results": results_list,
                "logs": logs,
                "artifacts": {
                    "legacy_execution": True,
                    "total_data_points": len(results_list)
                }
            }
        except json.JSONDecodeError as e:
            raise Exception(f"Failed to parse benchmark results: {e}")
