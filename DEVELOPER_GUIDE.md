# 🛠️ Exo Piper - Guia do Desenvolvedor

Bem-vindo ao guia completo para desenvolvedores do Exo Piper! Este documento fornece todas as informações necessárias para contribuir, integrar e estender o sistema.

## 📋 **Índice**

1. [Arquitetura do Sistema](#arquitetura)
2. [Setup do Ambiente de Desenvolvimento](#setup)
3. [Estrutura do Projeto](#estrutura)
4. [APIs e Integrações](#apis)
5. [Contribuindo](#contribuindo)
6. [Testes](#testes)
7. [Deploy e CI/CD](#deploy)
8. [Troubleshooting](#troubleshooting)

## 🏗️ **Arquitetura do Sistema** {#arquitetura}

### **Visão Geral**

O Exo Piper é construído com uma arquitetura de microserviços moderna:

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │    Backend      │    │   Agent Docker  │
│   (Next.js)     │◄──►│   (FastAPI)     │◄──►│   (Python)      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │                       ▼                       │
         │              ┌─────────────────┐              │
         │              │   PostgreSQL    │              │
         │              │   (Database)    │              │
         │              └─────────────────┘              │
         │                       │                       │
         │                       ▼                       │
         │              ┌─────────────────┐              │
         └──────────────►│     Redis       │◄─────────────┘
                        │   (Cache/Queue) │
                        └─────────────────┘
                                 │
                                 ▼
                        ┌─────────────────┐
                        │     Celery      │
                        │   (Workers)     │
                        └─────────────────┘
```

### **Componentes Principais**

#### **Frontend (Next.js 14)**
- **Framework:** Next.js com App Router
- **Styling:** Tailwind CSS
- **State Management:** React Context + Hooks
- **Charts:** Chart.js / Recharts
- **Authentication:** JWT tokens
- **Testing:** Playwright (E2E)

#### **Backend (FastAPI)**
- **Framework:** FastAPI com Pydantic
- **Database:** PostgreSQL com SQLAlchemy
- **Cache:** Redis
- **Queue:** Celery
- **Authentication:** JWT + OAuth2
- **Testing:** pytest + httpx

#### **Agent Docker**
- **Runtime:** Python 3.11 Alpine
- **Isolation:** Docker containers
- **Security:** Sandboxed execution
- **Monitoring:** Resource limits

#### **Infrastructure**
- **Reverse Proxy:** Nginx
- **Load Balancer:** Nginx + Docker Swarm
- **Monitoring:** Prometheus + Grafana
- **Storage:** MinIO (S3-compatible)

## 🚀 **Setup do Ambiente de Desenvolvimento** {#setup}

### **Pré-requisitos**

```bash
# Versões mínimas
Node.js >= 18.0.0
Python >= 3.11
Docker >= 24.0.0
Docker Compose >= 2.0.0
Git >= 2.30.0
```

### **Clonando o Repositório**

```bash
git clone https://github.com/exopiper/exopiper.git
cd exopiper
```

### **Setup Completo**

```bash
# Tornar scripts executáveis
chmod +x *.sh

# Setup automático
./setup-permissions.sh

# Ou setup manual:

# 1. Backend
cd backend
python -m venv venv
source venv/bin/activate  # Linux/Mac
# venv\Scripts\activate   # Windows
pip install -r requirements.txt
pip install -r requirements-dev.txt

# 2. Frontend
cd ../frontend
npm install

# 3. Banco de dados
cd ..
docker-compose up -d postgres redis

# 4. Migrações
cd backend
alembic upgrade head

# 5. Dados de teste
python scripts/seed_data.py
```

### **Variáveis de Ambiente**

Crie um arquivo `.env` na raiz:

```bash
# Database
DATABASE_URL=postgresql://exoPiper:password123@localhost:5432/exoPiper
POSTGRES_PASSWORD=password123

# Redis
REDIS_URL=redis://localhost:6379/0

# JWT
SECRET_KEY=your-super-secret-key-here
JWT_SECRET=your-jwt-secret-here

# MinIO
MINIO_ROOT_USER=admin
MINIO_ROOT_PASSWORD=password123

# Email (opcional para desenvolvimento)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password

# Blockchain (opcional)
BLOCKCHAIN_API_KEY=your-api-key
TANOS_API_KEY=your-tanos-key

# Development
DEBUG=true
LOG_LEVEL=DEBUG
```

### **Executando o Sistema**

```bash
# Opção 1: Docker Compose (recomendado)
docker-compose up -d

# Opção 2: Desenvolvimento local
# Terminal 1 - Backend
cd backend
uvicorn main:app --reload --host 0.0.0.0 --port 8000

# Terminal 2 - Frontend
cd frontend
npm run dev

# Terminal 3 - Celery
cd backend
celery -A app.core.celery_app worker --loglevel=info

# Terminal 4 - Celery Beat
cd backend
celery -A app.core.celery_app beat --loglevel=info
```

### **Verificando a Instalação**

```bash
# Testes rápidos
curl http://localhost:8000/health
curl http://localhost:3000

# Testes completos
./run-all-tests.sh
```

## 📁 **Estrutura do Projeto** {#estrutura}

```
exopiper/
├── backend/                 # API Backend (FastAPI)
│   ├── app/
│   │   ├── api/            # Endpoints da API
│   │   │   └── v1/         # Versão 1 da API
│   │   ├── core/           # Configurações centrais
│   │   ├── models/         # Modelos SQLAlchemy
│   │   ├── services/       # Lógica de negócio
│   │   └── utils/          # Utilitários
│   ├── alembic/            # Migrações do banco
│   ├── tests/              # Testes do backend
│   └── requirements.txt    # Dependências Python
├── frontend/               # Interface Web (Next.js)
│   ├── app/                # App Router (Next.js 14)
│   ├── components/         # Componentes React
│   ├── lib/                # Utilitários
│   ├── public/             # Assets estáticos
│   ├── tests/              # Testes E2E
│   └── package.json        # Dependências Node.js
├── agent/                  # Docker Agent
│   ├── Dockerfile          # Imagem do agent
│   ├── agent.py            # Executor de benchmarks
│   └── requirements.txt    # Dependências do agent
├── nginx/                  # Configurações Nginx
├── k8s/                    # Manifests Kubernetes
├── load-tests/             # Testes de carga (K6)
├── video-tutorials/        # Scripts de vídeos
├── docs/                   # Documentação
└── scripts/                # Scripts utilitários
```

### **Convenções de Código**

#### **Backend (Python)**
- **Style Guide:** PEP 8
- **Formatter:** Black
- **Linter:** Flake8 + mypy
- **Import Order:** isort

```python
# Exemplo de estrutura de arquivo
"""
Module docstring explaining the purpose.
"""

from typing import List, Optional
from fastapi import APIRouter, Depends
from sqlalchemy.ext.asyncio import AsyncSession

from ..core.database import get_db
from ..models.user import User

router = APIRouter()

@router.get("/users/")
async def get_users(
    skip: int = 0,
    limit: int = 100,
    db: AsyncSession = Depends(get_db)
) -> List[User]:
    """Get list of users with pagination."""
    # Implementation here
    pass
```

#### **Frontend (TypeScript/React)**
- **Style Guide:** Airbnb TypeScript
- **Formatter:** Prettier
- **Linter:** ESLint

```typescript
// Exemplo de componente
interface UserListProps {
  users: User[]
  onUserSelect: (user: User) => void
}

export default function UserList({ users, onUserSelect }: UserListProps) {
  return (
    <div className="space-y-4">
      {users.map((user) => (
        <UserCard 
          key={user.id} 
          user={user} 
          onClick={() => onUserSelect(user)} 
        />
      ))}
    </div>
  )
}
```

## 🔌 **APIs e Integrações** {#apis}

### **API REST**

A API segue padrões RESTful com documentação OpenAPI automática.

#### **Base URL**
```
Development: http://localhost:8000
Production: https://api.exopiper.com
```

#### **Autenticação**
```bash
# Login
POST /api/v1/auth/login
{
  "email": "<EMAIL>",
  "password": "password"
}

# Response
{
  "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "token_type": "bearer",
  "expires_in": 3600
}

# Usando o token
curl -H "Authorization: Bearer YOUR_TOKEN" \
     http://localhost:8000/api/v1/workloads/
```

#### **Endpoints Principais**

```bash
# Autenticação
POST   /api/v1/auth/login
POST   /api/v1/auth/register
POST   /api/v1/auth/refresh
GET    /api/v1/auth/me

# Workloads
GET    /api/v1/workloads/
POST   /api/v1/workloads/
GET    /api/v1/workloads/{id}
PUT    /api/v1/workloads/{id}
DELETE /api/v1/workloads/{id}

# Benchmarks
POST   /api/v1/benchmarks/run
GET    /api/v1/benchmarks/status/{job_id}
GET    /api/v1/benchmarks/results/{job_id}
GET    /api/v1/benchmarks/jobs

# Pagamentos
GET    /api/v1/payments/plans
POST   /api/v1/payments/create
GET    /api/v1/payments/status/{payment_id}
GET    /api/v1/payments/history

# Admin
GET    /api/v1/admin/users
GET    /api/v1/admin/analytics
POST   /api/v1/admin/cleanup
```

### **WebSockets**

Para atualizações em tempo real:

```javascript
// Frontend
const ws = new WebSocket('ws://localhost:8000/ws/benchmarks')

ws.onmessage = (event) => {
  const data = JSON.parse(event.data)
  if (data.type === 'benchmark_update') {
    updateBenchmarkStatus(data.job_id, data.status)
  }
}
```

### **Webhooks**

Para integrações externas:

```bash
# Configurar webhook
POST /api/v1/webhooks/
{
  "url": "https://your-app.com/webhook",
  "events": ["benchmark.completed", "payment.confirmed"],
  "secret": "your-webhook-secret"
}

# Payload de exemplo
{
  "event": "benchmark.completed",
  "data": {
    "job_id": "job_123",
    "workload_id": 456,
    "status": "completed",
    "results": {...}
  },
  "timestamp": "2024-01-15T10:30:00Z"
}
```

## 🤝 **Contribuindo** {#contribuindo}

### **Processo de Contribuição**

1. **Fork** o repositório
2. **Clone** seu fork
3. **Crie** uma branch para sua feature
4. **Implemente** as mudanças
5. **Teste** completamente
6. **Commit** com mensagens claras
7. **Push** para seu fork
8. **Abra** um Pull Request

### **Padrões de Commit**

```bash
# Formato
type(scope): description

# Tipos
feat: nova funcionalidade
fix: correção de bug
docs: documentação
style: formatação
refactor: refatoração
test: testes
chore: manutenção

# Exemplos
feat(auth): add OAuth2 integration
fix(benchmark): resolve memory leak in agent
docs(api): update authentication examples
test(payment): add USDT payment tests
```

### **Pull Request Template**

```markdown
## Descrição
Breve descrição das mudanças.

## Tipo de Mudança
- [ ] Bug fix
- [ ] Nova funcionalidade
- [ ] Breaking change
- [ ] Documentação

## Testes
- [ ] Testes unitários passando
- [ ] Testes de integração passando
- [ ] Testes E2E passando
- [ ] Testado manualmente

## Checklist
- [ ] Código segue padrões do projeto
- [ ] Self-review realizado
- [ ] Documentação atualizada
- [ ] Sem warnings de linter
```

### **Code Review Guidelines**

#### **Para Reviewers:**
- ✅ Funcionalidade funciona conforme esperado
- ✅ Código é legível e bem documentado
- ✅ Testes adequados estão incluídos
- ✅ Performance não foi degradada
- ✅ Segurança foi considerada

#### **Para Contributors:**
- 📝 Descreva claramente as mudanças
- 🧪 Inclua testes para novas funcionalidades
- 📚 Atualize documentação relevante
- 🔍 Faça self-review antes de submeter
- 💬 Responda feedback construtivamente

## 🧪 **Testes** {#testes}

### **Estrutura de Testes**

```
tests/
├── unit/                   # Testes unitários
│   ├── test_auth.py
│   ├── test_workloads.py
│   └── test_payments.py
├── integration/            # Testes de integração
│   ├── test_api_flow.py
│   └── test_benchmark_flow.py
├── e2e/                    # Testes End-to-End
│   ├── auth.spec.ts
│   ├── workloads.spec.ts
│   └── payments.spec.ts
└── load/                   # Testes de carga
    ├── auth-load-test.js
    └── benchmark-load-test.js
```

### **Executando Testes**

```bash
# Todos os testes
./run-all-tests.sh

# Backend apenas
cd backend
pytest

# Frontend apenas
cd frontend
npm test

# E2E
cd frontend
npm run test:e2e

# Testes de carga
./run-load-tests.sh

# Com coverage
cd backend
pytest --cov=app --cov-report=html
```

### **Escrevendo Testes**

#### **Teste Unitário (Backend)**
```python
import pytest
from httpx import AsyncClient
from app.main import app

@pytest.mark.asyncio
async def test_create_workload():
    async with AsyncClient(app=app, base_url="http://test") as ac:
        # Setup
        login_response = await ac.post("/api/v1/auth/login", json={
            "email": "<EMAIL>",
            "password": "testpass"
        })
        token = login_response.json()["access_token"]
        
        # Test
        response = await ac.post(
            "/api/v1/workloads/",
            json={
                "name": "Test Workload",
                "code": "def test(): return True",
                "language": "python"
            },
            headers={"Authorization": f"Bearer {token}"}
        )
        
        # Assert
        assert response.status_code == 201
        assert response.json()["name"] == "Test Workload"
```

#### **Teste E2E (Frontend)**
```typescript
import { test, expect } from '@playwright/test'

test('user can create workload', async ({ page }) => {
  // Login
  await page.goto('/login')
  await page.fill('[data-testid="email"]', '<EMAIL>')
  await page.fill('[data-testid="password"]', 'testpass')
  await page.click('[data-testid="login-button"]')
  
  // Navigate to workloads
  await page.click('[data-testid="workloads-link"]')
  await page.click('[data-testid="new-workload-button"]')
  
  // Fill form
  await page.fill('[data-testid="workload-name"]', 'Test Workload')
  await page.fill('[data-testid="workload-code"]', 'def test(): return True')
  await page.selectOption('[data-testid="language"]', 'python')
  
  // Submit
  await page.click('[data-testid="create-button"]')
  
  // Verify
  await expect(page.locator('[data-testid="success-message"]')).toBeVisible()
  await expect(page.locator('text=Test Workload')).toBeVisible()
})
```

## 🚀 **Deploy e CI/CD** {#deploy}

### **GitHub Actions**

O projeto usa GitHub Actions para CI/CD automático:

```yaml
# .github/workflows/ci-cd.yml
name: CI/CD Pipeline

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Run tests
        run: ./run-all-tests.sh
  
  deploy:
    needs: test
    if: github.ref == 'refs/heads/main'
    runs-on: ubuntu-latest
    steps:
      - name: Deploy to production
        run: ./deploy-production.sh
```

### **Ambientes**

#### **Development**
```bash
# Local development
docker-compose up -d
```

#### **Staging**
```bash
# Deploy to staging
export ENVIRONMENT=staging
./deploy-production.sh
```

#### **Production**
```bash
# Deploy to production
export ENVIRONMENT=production
export DOMAIN=exopiper.com
./deploy-production.sh

# Ou com Kubernetes
./deploy-k8s.sh
```

### **Monitoramento**

#### **Health Checks**
```bash
# Backend health
curl http://localhost:8000/health

# Frontend health
curl http://localhost:3000

# Database health
docker-compose exec postgres pg_isready
```

#### **Logs**
```bash
# Application logs
docker-compose logs -f backend

# System logs
./monitor-performance.sh

# Error tracking
tail -f backend/logs/error.log
```

## 🔧 **Troubleshooting** {#troubleshooting}

### **Problemas Comuns**

#### **Backend não inicia**
```bash
# Verificar dependências
pip install -r requirements.txt

# Verificar banco de dados
docker-compose up -d postgres
alembic upgrade head

# Verificar variáveis de ambiente
cat .env
```

#### **Frontend não carrega**
```bash
# Limpar cache
rm -rf .next node_modules
npm install
npm run build

# Verificar API connection
curl http://localhost:8000/health
```

#### **Benchmarks falham**
```bash
# Verificar Docker
docker ps
docker images | grep exopiper

# Rebuild agent
./build-agent.sh

# Verificar logs
docker-compose logs celery
```

#### **Testes falham**
```bash
# Limpar ambiente de teste
docker-compose down -v
docker-compose up -d

# Executar testes individualmente
cd backend
pytest tests/test_auth.py -v

cd frontend
npm run test:e2e -- --headed
```

### **Performance Issues**

#### **Banco de dados lento**
```bash
# Verificar índices
./optimize-system.sh database

# Analisar queries
docker-compose exec postgres psql -U exoPiper -c "
SELECT query, mean_time, calls 
FROM pg_stat_statements 
ORDER BY mean_time DESC 
LIMIT 10;"
```

#### **Frontend lento**
```bash
# Analisar bundle
npm run analyze

# Otimizar imagens
npm run optimize-images

# Verificar lighthouse
lighthouse http://localhost:3000
```

### **Debugging**

#### **Backend Debug**
```python
# Adicionar breakpoints
import pdb; pdb.set_trace()

# Ou usar debugger do VS Code
# Configurar launch.json
{
  "name": "FastAPI Debug",
  "type": "python",
  "request": "launch",
  "program": "main.py",
  "args": ["--reload"]
}
```

#### **Frontend Debug**
```typescript
// Browser DevTools
console.log('Debug info:', data)

// React DevTools
// Instalar extensão do browser

// Next.js Debug
// Configurar VS Code
{
  "name": "Next.js Debug",
  "type": "node",
  "request": "launch",
  "program": "${workspaceFolder}/node_modules/.bin/next",
  "args": ["dev"]
}
```

### **Suporte**

- **📧 Email:** <EMAIL>
- **💬 Discord:** https://discord.gg/exopiper
- **📚 Docs:** https://docs.exopiper.com
- **🐛 Issues:** https://github.com/exopiper/exopiper/issues

---

## 📚 **Recursos Adicionais**

- [API Documentation](API_DOCUMENTATION.md)
- [User Guide](USER_GUIDE.md)
- [Deployment Guide](deploy-production.sh)
- [Contributing Guidelines](CONTRIBUTING.md)
- [Code of Conduct](CODE_OF_CONDUCT.md)

**Happy coding! 🚀**
