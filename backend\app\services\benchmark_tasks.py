"""
Celery tasks for benchmark execution
"""

import asyncio
import json
import docker
import time
from datetime import datetime
from typing import Dict, Any, List
from celery import current_task
from sqlalchemy.ext.asyncio import AsyncSession

from ..core.celery_app import celery_app
from ..core.database import AsyncSessionLocal
from ..core.config import settings
from ..models.benchmark import <PERSON><PERSON><PERSON><PERSON><PERSON>, BenchmarkResult, JobStatus
from ..models.workload import Workload
from ..models.user import User
from .docker_service import DockerBenchmarkService
from .analysis_service import ComplexityAnalysisService
from .storage_service import storage_service
from .retention_service import retention_service
from .payment_service import payment_service
from .notification_service import notification_service


@celery_app.task(bind=True)
def run_benchmark_job(self, job_id: int):
    """Execute a benchmark job asynchronously"""

    # Update task ID in job
    task_id = self.request.id

    try:
        # Run the async function in event loop
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        result = loop.run_until_complete(_execute_benchmark_job(job_id, task_id))
        loop.close()
        return result

    except Exception as e:
        # Update job status to failed
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        loop.run_until_complete(_update_job_status(job_id, JobStatus.FAILED, str(e)))
        loop.close()
        raise


async def _execute_benchmark_job(job_id: int, task_id: str) -> Dict[str, Any]:
    """Execute benchmark job implementation"""

    async with AsyncSessionLocal() as db:
        # Get job details
        job = await db.get(BenchmarkJob, job_id)
        if not job:
            raise ValueError(f"Job {job_id} not found")

        # Get workload and user
        workload = await db.get(Workload, job.workload_id)
        user = await db.get(User, job.user_id)

        if not workload or not user:
            raise ValueError("Workload or user not found")

        # Update job status
        job.status = JobStatus.RUNNING
        job.started_at = datetime.utcnow()
        await db.commit()

        try:
            # Initialize Docker service
            docker_service = DockerBenchmarkService()

            # Execute benchmark
            benchmark_output = await docker_service.run_benchmark(
                workload_type=workload.workload_type,
                config=workload.config,
                job_id=job.job_id
            )

            results = benchmark_output.get('results', [])
            logs = benchmark_output.get('logs', '')
            artifacts = benchmark_output.get('artifacts', {})

            # Upload logs and artifacts to storage
            try:
                if logs:
                    log_key = storage_service.upload_benchmark_logs(
                        job_id=job.job_id,
                        logs=logs,
                        log_type="execution"
                    )
                    job.log_file_path = log_key

                if artifacts:
                    artifact_key = storage_service.upload_benchmark_artifacts(
                        job_id=job.job_id,
                        artifacts=artifacts
                    )
                    job.artifact_file_path = artifact_key

            except Exception as e:
                print(f"Warning: Failed to upload artifacts for job {job.job_id}: {e}")

            # Store results in database
            for result_data in results:
                benchmark_result = BenchmarkResult(
                    job_id=job.id,
                    input_size=result_data['input_size'],
                    execution_time_ns=result_data['execution_time_ns'],
                    clean_time_ns=result_data.get('clean_time_ns', result_data['execution_time_ns']),
                    hardware_type=result_data.get('hardware_type', 'CPU'),
                    hardware_info=result_data.get('hardware_info'),
                    memory_usage_mb=result_data.get('memory_usage_mb'),
                    cpu_usage_percent=result_data.get('cpu_usage_percent'),
                    metadata=result_data.get('metadata', {})
                )
                db.add(benchmark_result)

            # Update job status
            job.status = JobStatus.COMPLETED
            job.completed_at = datetime.utcnow()
            job.duration_seconds = (job.completed_at - job.started_at).total_seconds()

            await db.commit()

            # Queue complexity analysis
            analyze_benchmark_complexity.delay(job.id)

            return {
                "job_id": job.job_id,
                "status": "completed",
                "results_count": len(results),
                "duration_seconds": job.duration_seconds
            }

        except Exception as e:
            # Update job with error
            job.status = JobStatus.FAILED
            job.error_message = str(e)
            job.completed_at = datetime.utcnow()
            await db.commit()
            raise


async def _update_job_status(job_id: int, status: JobStatus, error_message: str = None):
    """Update job status in database"""
    async with AsyncSessionLocal() as db:
        job = await db.get(BenchmarkJob, job_id)
        if job:
            job.status = status
            if error_message:
                job.error_message = error_message
            job.completed_at = datetime.utcnow()
            await db.commit()


@celery_app.task
def analyze_benchmark_complexity(job_id: int):
    """Analyze benchmark results for complexity metrics (λ and p)"""

    try:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        result = loop.run_until_complete(_analyze_complexity(job_id))
        loop.close()
        return result

    except Exception as e:
        print(f"Error analyzing complexity for job {job_id}: {e}")
        raise


async def _analyze_complexity(job_id: int) -> Dict[str, Any]:
    """Perform complexity analysis on benchmark results"""

    async with AsyncSessionLocal() as db:
        # Get job and results
        job = await db.get(BenchmarkJob, job_id)
        if not job:
            raise ValueError(f"Job {job_id} not found")

        # Get all results for this job
        from sqlalchemy import select
        result = await db.execute(
            select(BenchmarkResult).where(BenchmarkResult.job_id == job.id)
        )
        results = result.scalars().all()

        if len(results) < 3:
            print(f"Not enough data points for analysis: {len(results)}")
            return {"status": "insufficient_data"}

        # Initialize analysis service
        analysis_service = ComplexityAnalysisService()

        # Perform log-log regression analysis
        analysis_result = analysis_service.analyze_complexity(results)

        # Update results with λ and p values
        for result_obj in results:
            result_obj.lambda_value = analysis_result['lambda_value']
            result_obj.p_exponent = analysis_result['p_exponent']

        await db.commit()

        # Check for performance alerts
        check_performance_regression.delay(job.user_id, job.workload_id, analysis_result)

        return analysis_result


@celery_app.task
def check_performance_regression(user_id: int, workload_id: int, current_analysis: Dict[str, Any]):
    """Check for performance regressions and trigger alerts"""

    try:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        loop.run_until_complete(_check_regression(user_id, workload_id, current_analysis))
        loop.close()

    except Exception as e:
        print(f"Error checking regression for user {user_id}, workload {workload_id}: {e}")


async def _check_regression(user_id: int, workload_id: int, current_analysis: Dict[str, Any]):
    """Check for performance regression against historical data"""

    async with AsyncSessionLocal() as db:
        # Get historical analysis for comparison
        from sqlalchemy import select, and_
        from ..models.workload import Workload

        # Get recent completed jobs for this workload
        result = await db.execute(
            select(BenchmarkJob).where(
                and_(
                    BenchmarkJob.user_id == user_id,
                    BenchmarkJob.workload_id == workload_id,
                    BenchmarkJob.status == JobStatus.COMPLETED
                )
            ).order_by(BenchmarkJob.completed_at.desc()).limit(5)
        )
        recent_jobs = result.scalars().all()

        if len(recent_jobs) < 2:
            return  # Not enough historical data

        # Get results from previous job
        prev_job = recent_jobs[1]  # Second most recent
        prev_result = await db.execute(
            select(BenchmarkResult).where(BenchmarkResult.job_id == prev_job.id).limit(1)
        )
        prev_benchmark = prev_result.scalar_one_or_none()

        if not prev_benchmark or not prev_benchmark.p_exponent:
            return

        # Check for significant changes
        current_p = current_analysis.get('p_exponent')
        prev_p = prev_benchmark.p_exponent

        if current_p and prev_p:
            delta_p = abs(current_p - prev_p)

            if delta_p > settings.DELTA_P_THRESHOLD:
                # Trigger alert
                from .notification_tasks import send_performance_alert
                send_performance_alert.delay(
                    user_id=user_id,
                    workload_id=workload_id,
                    alert_type="p_exponent_regression",
                    current_value=current_p,
                    previous_value=prev_p,
                    delta=delta_p
                )


@celery_app.task(bind=True)
def cleanup_user_data_task(self, user_id: int = None):
    """
    Celery task to clean up old data based on retention policies
    If user_id is provided, clean only that user's data
    Otherwise, clean all users' data
    """
    try:
        if user_id:
            # Clean specific user
            return asyncio.run(_cleanup_specific_user(user_id))
        else:
            # Clean all users
            return asyncio.run(_cleanup_all_users())
    except Exception as e:
        print(f"Error in cleanup task: {e}")
        raise


async def _cleanup_specific_user(user_id: int) -> Dict[str, Any]:
    """Clean up data for a specific user"""
    async with AsyncSessionLocal() as db:
        try:
            # Get user
            from sqlalchemy import select
            user_query = select(User).where(User.id == user_id)
            user_result = await db.execute(user_query)
            user = user_result.scalar_one_or_none()

            if not user:
                return {"error": f"User {user_id} not found"}

            # Run cleanup
            cleanup_stats = await retention_service.cleanup_user_data(user, db)
            return {
                "status": "success",
                "user_id": user_id,
                "stats": cleanup_stats
            }
        except Exception as e:
            return {
                "status": "error",
                "user_id": user_id,
                "error": str(e)
            }


async def _cleanup_all_users() -> Dict[str, Any]:
    """Clean up data for all users"""
    try:
        cleanup_stats = await retention_service.cleanup_all_users()
        return {
            "status": "success",
            "stats": cleanup_stats
        }
    except Exception as e:
        return {
            "status": "error",
            "error": str(e)
        }


@celery_app.task(bind=True)
def monitor_pending_payments_task(self):
    """
    Celery task to monitor pending payments and check for confirmations
    Should be run periodically (every 5-10 minutes)
    """
    try:
        return asyncio.run(_monitor_pending_payments())
    except Exception as e:
        print(f"Error in payment monitoring task: {e}")
        raise


async def _monitor_pending_payments() -> Dict[str, Any]:
    """Monitor pending payments for confirmations"""

    async with AsyncSessionLocal() as db:
        try:
            from sqlalchemy import select, and_
            from ..models.payment import Payment, PaymentStatus

            # Get all pending payments that haven't expired
            pending_payments_query = select(Payment).where(
                and_(
                    Payment.status == PaymentStatus.PENDING,
                    Payment.expires_at > datetime.utcnow()
                )
            )

            pending_result = await db.execute(pending_payments_query)
            pending_payments = pending_result.scalars().all()

            confirmed_count = 0
            expired_count = 0
            errors = []

            for payment in pending_payments:
                try:
                    # Check payment status
                    status_info = await payment_service.check_payment_status(payment.id)

                    if status_info["status"] == "confirmed":
                        confirmed_count += 1
                    elif status_info["status"] == "expired":
                        expired_count += 1

                except Exception as e:
                    errors.append(f"Error checking payment {payment.id}: {e}")

            # Clean up expired payments
            await payment_service.cleanup_expired_payments()

            return {
                "status": "success",
                "checked_payments": len(pending_payments),
                "confirmed_payments": confirmed_count,
                "expired_payments": expired_count,
                "errors": errors
            }

        except Exception as e:
            return {
                "status": "error",
                "error": str(e)
            }


@celery_app.task(bind=True)
def check_user_limits_task(self):
    """
    Celery task to check user limits and send notifications
    Should be run periodically (every hour)
    """
    try:
        return asyncio.run(_check_all_user_limits())
    except Exception as e:
        print(f"Error in user limits check task: {e}")
        raise


async def _check_all_user_limits() -> Dict[str, Any]:
    """Check limits for all users and send notifications"""

    try:
        results = await notification_service.check_all_users_limits()

        # Count notifications sent
        total_notifications = 0
        users_notified = 0

        for result in results:
            if "notifications_sent" in result:
                notifications_count = len(result["notifications_sent"])
                total_notifications += notifications_count
                if notifications_count > 0:
                    users_notified += 1

        return {
            "status": "success",
            "users_checked": len(results),
            "users_notified": users_notified,
            "total_notifications": total_notifications,
            "timestamp": datetime.utcnow().isoformat()
        }

    except Exception as e:
        return {
            "status": "error",
            "error": str(e)
        }
