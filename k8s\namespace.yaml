apiVersion: v1
kind: Namespace
metadata:
  name: exopiper
  labels:
    name: exopiper
    app: exopiper
    environment: production
---
apiVersion: v1
kind: ResourceQuota
metadata:
  name: exopiper-quota
  namespace: exopiper
spec:
  hard:
    requests.cpu: "10"
    requests.memory: 20Gi
    limits.cpu: "20"
    limits.memory: 40Gi
    persistentvolumeclaims: "10"
    services: "10"
    secrets: "10"
    configmaps: "10"
---
apiVersion: v1
kind: LimitRange
metadata:
  name: exopiper-limits
  namespace: exopiper
spec:
  limits:
  - default:
      cpu: "1"
      memory: "1Gi"
    defaultRequest:
      cpu: "100m"
      memory: "128Mi"
    type: Container
