"""
Notification models for user alerts and system notifications
"""

import enum
from datetime import datetime
from sqlalchemy import <PERSON>umn, Integer, String, Text, DateTime, Boolean, <PERSON><PERSON>ey, <PERSON>SO<PERSON>
from sqlalchemy.dialects.postgresql import ENUM
from sqlalchemy.orm import relationship

from ..core.database import Base


class NotificationType(enum.Enum):
    """Types of notifications"""
    WORKLOAD_LIMIT_WARNING = "workload_limit_warning"
    MONTHLY_JOBS_LIMIT_WARNING = "monthly_jobs_limit_warning"
    STORAGE_LIMIT_WARNING = "storage_limit_warning"
    PLAN_UPGRADED = "plan_upgraded"
    PAYMENT_CONFIRMED = "payment_confirmed"
    PAYMENT_FAILED = "payment_failed"
    BENCHMARK_COMPLETED = "benchmark_completed"
    BENCHMARK_FAILED = "benchmark_failed"
    PERFORMANCE_ALERT = "performance_alert"
    SYSTEM_MAINTENANCE = "system_maintenance"
    SECURITY_ALERT = "security_alert"


class NotificationStatus(enum.Enum):
    """Status of notifications"""
    PENDING = "pending"
    SENT = "sent"
    DELIVERED = "delivered"
    FAILED = "failed"
    READ = "read"


class NotificationChannel(enum.Enum):
    """Notification delivery channels"""
    EMAIL = "email"
    SLACK = "slack"
    WEBHOOK = "webhook"
    IN_APP = "in_app"
    SMS = "sms"


class Notification(Base):
    """User notifications and alerts"""
    
    __tablename__ = "notifications"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id", ondelete="CASCADE"), nullable=False)
    
    # Notification details
    type = Column(ENUM(NotificationType), nullable=False)
    title = Column(String(200), nullable=False)
    message = Column(Text, nullable=False)
    metadata = Column(JSON, nullable=True)  # Additional data for the notification
    
    # Status tracking
    status = Column(ENUM(NotificationStatus), default=NotificationStatus.PENDING)
    priority = Column(String(20), default="normal")  # low, normal, high, urgent
    
    # Delivery tracking
    email_sent = Column(Boolean, default=False)
    slack_sent = Column(Boolean, default=False)
    webhook_sent = Column(Boolean, default=False)
    in_app_read = Column(Boolean, default=False)
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    sent_at = Column(DateTime, nullable=True)
    delivered_at = Column(DateTime, nullable=True)
    read_at = Column(DateTime, nullable=True)
    expires_at = Column(DateTime, nullable=True)
    
    # Retry tracking
    retry_count = Column(Integer, default=0)
    max_retries = Column(Integer, default=3)
    next_retry_at = Column(DateTime, nullable=True)
    
    # Relationships
    user = relationship("User", back_populates="notifications")
    
    def __repr__(self):
        return f"<Notification(id={self.id}, type={self.type}, user_id={self.user_id})>"
    
    @property
    def is_expired(self) -> bool:
        """Check if notification has expired"""
        if not self.expires_at:
            return False
        return datetime.utcnow() > self.expires_at
    
    @property
    def can_retry(self) -> bool:
        """Check if notification can be retried"""
        return (
            self.status == NotificationStatus.FAILED and
            self.retry_count < self.max_retries and
            not self.is_expired
        )


class NotificationPreference(Base):
    """User notification preferences"""
    
    __tablename__ = "notification_preferences"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id", ondelete="CASCADE"), nullable=False, unique=True)
    
    # Email preferences
    email_enabled = Column(Boolean, default=True)
    email_limit_warnings = Column(Boolean, default=True)
    email_payment_updates = Column(Boolean, default=True)
    email_benchmark_results = Column(Boolean, default=True)
    email_performance_alerts = Column(Boolean, default=True)
    email_system_updates = Column(Boolean, default=False)
    
    # Slack preferences
    slack_enabled = Column(Boolean, default=False)
    slack_webhook_url = Column(String(500), nullable=True)
    slack_limit_warnings = Column(Boolean, default=False)
    slack_payment_updates = Column(Boolean, default=False)
    slack_benchmark_results = Column(Boolean, default=False)
    slack_performance_alerts = Column(Boolean, default=True)
    
    # In-app preferences
    in_app_enabled = Column(Boolean, default=True)
    in_app_limit_warnings = Column(Boolean, default=True)
    in_app_payment_updates = Column(Boolean, default=True)
    in_app_benchmark_results = Column(Boolean, default=True)
    in_app_performance_alerts = Column(Boolean, default=True)
    
    # Webhook preferences
    webhook_enabled = Column(Boolean, default=False)
    webhook_url = Column(String(500), nullable=True)
    webhook_secret = Column(String(100), nullable=True)
    
    # Frequency settings
    digest_frequency = Column(String(20), default="daily")  # immediate, daily, weekly, never
    quiet_hours_start = Column(Integer, default=22)  # 22:00
    quiet_hours_end = Column(Integer, default=8)     # 08:00
    timezone = Column(String(50), default="UTC")
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    user = relationship("User", back_populates="notification_preferences")
    
    def __repr__(self):
        return f"<NotificationPreference(user_id={self.user_id})>"
    
    def is_channel_enabled(self, channel: NotificationChannel, notification_type: NotificationType) -> bool:
        """Check if a specific channel is enabled for a notification type"""
        
        if channel == NotificationChannel.EMAIL:
            if not self.email_enabled:
                return False
            
            type_mapping = {
                NotificationType.WORKLOAD_LIMIT_WARNING: self.email_limit_warnings,
                NotificationType.MONTHLY_JOBS_LIMIT_WARNING: self.email_limit_warnings,
                NotificationType.STORAGE_LIMIT_WARNING: self.email_limit_warnings,
                NotificationType.PAYMENT_CONFIRMED: self.email_payment_updates,
                NotificationType.PAYMENT_FAILED: self.email_payment_updates,
                NotificationType.BENCHMARK_COMPLETED: self.email_benchmark_results,
                NotificationType.BENCHMARK_FAILED: self.email_benchmark_results,
                NotificationType.PERFORMANCE_ALERT: self.email_performance_alerts,
                NotificationType.SYSTEM_MAINTENANCE: self.email_system_updates,
            }
            
            return type_mapping.get(notification_type, True)
        
        elif channel == NotificationChannel.SLACK:
            if not self.slack_enabled or not self.slack_webhook_url:
                return False
            
            type_mapping = {
                NotificationType.WORKLOAD_LIMIT_WARNING: self.slack_limit_warnings,
                NotificationType.MONTHLY_JOBS_LIMIT_WARNING: self.slack_limit_warnings,
                NotificationType.STORAGE_LIMIT_WARNING: self.slack_limit_warnings,
                NotificationType.PAYMENT_CONFIRMED: self.slack_payment_updates,
                NotificationType.PAYMENT_FAILED: self.slack_payment_updates,
                NotificationType.BENCHMARK_COMPLETED: self.slack_benchmark_results,
                NotificationType.BENCHMARK_FAILED: self.slack_benchmark_results,
                NotificationType.PERFORMANCE_ALERT: self.slack_performance_alerts,
            }
            
            return type_mapping.get(notification_type, False)
        
        elif channel == NotificationChannel.IN_APP:
            if not self.in_app_enabled:
                return False
            
            type_mapping = {
                NotificationType.WORKLOAD_LIMIT_WARNING: self.in_app_limit_warnings,
                NotificationType.MONTHLY_JOBS_LIMIT_WARNING: self.in_app_limit_warnings,
                NotificationType.STORAGE_LIMIT_WARNING: self.in_app_limit_warnings,
                NotificationType.PAYMENT_CONFIRMED: self.in_app_payment_updates,
                NotificationType.PAYMENT_FAILED: self.in_app_payment_updates,
                NotificationType.BENCHMARK_COMPLETED: self.in_app_benchmark_results,
                NotificationType.BENCHMARK_FAILED: self.in_app_benchmark_results,
                NotificationType.PERFORMANCE_ALERT: self.in_app_performance_alerts,
            }
            
            return type_mapping.get(notification_type, True)
        
        elif channel == NotificationChannel.WEBHOOK:
            return self.webhook_enabled and bool(self.webhook_url)
        
        return False


class NotificationTemplate(Base):
    """Templates for different types of notifications"""
    
    __tablename__ = "notification_templates"
    
    id = Column(Integer, primary_key=True, index=True)
    type = Column(ENUM(NotificationType), nullable=False, unique=True)
    channel = Column(ENUM(NotificationChannel), nullable=False)
    
    # Template content
    subject_template = Column(String(200), nullable=False)
    body_template = Column(Text, nullable=False)
    
    # Template variables (JSON list of variable names)
    variables = Column(JSON, nullable=True)
    
    # Metadata
    language = Column(String(10), default="pt-BR")
    is_active = Column(Boolean, default=True)
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def __repr__(self):
        return f"<NotificationTemplate(type={self.type}, channel={self.channel})>"
