# Exo Piper Environment Configuration
# Copy this file to .env and update the values

# Application
APP_NAME=Exo Piper
VERSION=1.0.0
ENVIRONMENT=development
DEBUG=true

# Security
SECRET_KEY=your-secret-key-change-in-production-make-it-long-and-random
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# Database
DATABASE_URL=postgresql://exoPiper:exoPiper_dev@localhost:5432/exoPiper

# Redis
REDIS_URL=redis://localhost:6379/0

# MinIO/S3
MINIO_ENDPOINT=localhost:9000
MINIO_ACCESS_KEY=exoPiper
MINIO_SECRET_KEY=exoPiper_dev
MINIO_SECURE=false
BUCKET_NAME=exoPiper-storage

# CORS
ALLOWED_HOSTS=["http://localhost:3000", "http://localhost:8000"]

# Celery
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0

# Docker
DOCKER_SOCKET=/var/run/docker.sock
BENCHMARK_IMAGE=exoPiper/agent:latest

# Billing & Plans (Stripe)
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# Plan Limits
FREE_PLAN_WORKLOADS=2
FREE_PLAN_RETENTION_DAYS=7
PRO_PLAN_WORKLOADS=5
PRO_PLAN_RETENTION_DAYS=30
TEAM_PLAN_WORKLOADS=20
TEAM_PLAN_RETENTION_DAYS=90

# Performance Thresholds
DELTA_P_THRESHOLD=0.05
DELTA_LAMBDA_THRESHOLD=0.1

# Email Settings (for alerts)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password

# Slack Webhook (for alerts)
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK

# Frontend
NEXT_PUBLIC_API_URL=http://localhost:8000
FRONTEND_URL=http://localhost:3000
