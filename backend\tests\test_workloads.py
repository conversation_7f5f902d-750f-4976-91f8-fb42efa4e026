"""
Tests for workload endpoints
"""

import pytest
from httpx import As<PERSON><PERSON><PERSON>
from sqlalchemy.ext.asyncio import AsyncSession

from app.models.user import User
from app.models.workload import Workload


class TestWorkloads:
    """Test workload CRUD operations"""
    
    async def test_create_workload(self, authenticated_client: AsyncClient, test_user: User):
        """Test creating a new workload"""
        workload_data = {
            "name": "test_3sat",
            "workload_type": "3sat",
            "description": "Test 3-SAT workload",
            "config": {
                "max_variables": 20,
                "iterations": 3,
                "clause_ratio": 4.3
            }
        }
        
        response = await authenticated_client.post("/api/v1/workloads/", json=workload_data)
        
        assert response.status_code == 201
        data = response.json()
        assert data["name"] == workload_data["name"]
        assert data["workload_type"] == workload_data["workload_type"]
        assert data["description"] == workload_data["description"]
        assert data["config"] == workload_data["config"]
        assert data["user_id"] == test_user.id
        assert data["is_active"] is True
        assert "id" in data
        assert "created_at" in data
    
    async def test_create_workload_unauthorized(self, test_client: AsyncClient):
        """Test creating workload without authentication"""
        workload_data = {
            "name": "test_workload",
            "workload_type": "mergesort",
            "description": "Test workload",
            "config": {"max_size": 1000}
        }
        
        response = await test_client.post("/api/v1/workloads/", json=workload_data)
        
        assert response.status_code == 401
    
    async def test_get_workloads(self, authenticated_client: AsyncClient, test_workload: Workload):
        """Test getting user's workloads"""
        response = await authenticated_client.get("/api/v1/workloads/")
        
        assert response.status_code == 200
        data = response.json()
        assert "workloads" in data
        assert "total" in data
        assert "page" in data
        assert "size" in data
        
        workloads = data["workloads"]
        assert len(workloads) >= 1
        
        # Check if our test workload is in the list
        workload_ids = [w["id"] for w in workloads]
        assert test_workload.id in workload_ids
    
    async def test_get_workload_by_id(self, authenticated_client: AsyncClient, test_workload: Workload):
        """Test getting specific workload by ID"""
        response = await authenticated_client.get(f"/api/v1/workloads/{test_workload.id}")
        
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == test_workload.id
        assert data["name"] == test_workload.name
        assert data["workload_type"] == test_workload.workload_type
        assert data["config"] == test_workload.config
    
    async def test_get_nonexistent_workload(self, authenticated_client: AsyncClient):
        """Test getting non-existent workload"""
        response = await authenticated_client.get("/api/v1/workloads/99999")
        
        assert response.status_code == 404
    
    async def test_update_workload(self, authenticated_client: AsyncClient, test_workload: Workload):
        """Test updating workload"""
        update_data = {
            "name": "updated_mergesort",
            "description": "Updated description",
            "config": {
                "max_size": 2000,
                "iterations": 5,
                "step_factor": 2
            }
        }
        
        response = await authenticated_client.put(f"/api/v1/workloads/{test_workload.id}", json=update_data)
        
        assert response.status_code == 200
        data = response.json()
        assert data["name"] == update_data["name"]
        assert data["description"] == update_data["description"]
        assert data["config"] == update_data["config"]
        assert data["workload_type"] == test_workload.workload_type  # Should not change
    
    async def test_delete_workload(self, authenticated_client: AsyncClient, test_session: AsyncSession, test_user: User):
        """Test deleting workload"""
        # Create a workload to delete
        workload = Workload(
            user_id=test_user.id,
            name="to_delete",
            workload_type="mergesort",
            description="Workload to delete",
            config={"max_size": 500},
            is_active=True
        )
        test_session.add(workload)
        await test_session.commit()
        await test_session.refresh(workload)
        
        response = await authenticated_client.delete(f"/api/v1/workloads/{workload.id}")
        
        assert response.status_code == 200
        
        # Verify workload is marked as inactive
        get_response = await authenticated_client.get(f"/api/v1/workloads/{workload.id}")
        assert get_response.status_code == 200
        data = get_response.json()
        assert data["is_active"] is False
    
    async def test_workload_access_control(self, test_client: AsyncClient, test_session: AsyncSession, test_workload: Workload):
        """Test that users can only access their own workloads"""
        # Create another user
        from app.core.security import get_password_hash, create_access_token
        
        other_user = User(
            username="otheruser",
            email="<EMAIL>",
            hashed_password=get_password_hash("password"),
            plan_type="free",
            is_active=True
        )
        test_session.add(other_user)
        await test_session.commit()
        await test_session.refresh(other_user)
        
        # Create token for other user
        other_token = create_access_token(data={"sub": other_user.email})
        
        # Try to access test_workload with other user's token
        headers = {"Authorization": f"Bearer {other_token}"}
        response = await test_client.get(f"/api/v1/workloads/{test_workload.id}", headers=headers)
        
        assert response.status_code == 404  # Should not find workload belonging to different user


class TestWorkloadValidation:
    """Test workload input validation"""
    
    async def test_create_workload_invalid_type(self, authenticated_client: AsyncClient):
        """Test creating workload with invalid type"""
        workload_data = {
            "name": "test_invalid",
            "workload_type": "invalid_type",
            "description": "Invalid workload type",
            "config": {}
        }
        
        response = await authenticated_client.post("/api/v1/workloads/", json=workload_data)
        
        assert response.status_code == 422  # Validation error
    
    async def test_create_workload_missing_fields(self, authenticated_client: AsyncClient):
        """Test creating workload with missing required fields"""
        # Missing name
        workload_data = {
            "workload_type": "mergesort",
            "description": "Missing name",
            "config": {}
        }
        
        response = await authenticated_client.post("/api/v1/workloads/", json=workload_data)
        assert response.status_code == 422
        
        # Missing workload_type
        workload_data = {
            "name": "test_workload",
            "description": "Missing type",
            "config": {}
        }
        
        response = await authenticated_client.post("/api/v1/workloads/", json=workload_data)
        assert response.status_code == 422
    
    async def test_create_workload_invalid_config(self, authenticated_client: AsyncClient):
        """Test creating workload with invalid config"""
        workload_data = {
            "name": "test_invalid_config",
            "workload_type": "mergesort",
            "description": "Invalid config",
            "config": "not_a_dict"  # Should be a dictionary
        }
        
        response = await authenticated_client.post("/api/v1/workloads/", json=workload_data)
        
        assert response.status_code == 422  # Validation error
    
    async def test_update_workload_partial(self, authenticated_client: AsyncClient, test_workload: Workload):
        """Test partial update of workload"""
        update_data = {
            "description": "Only updating description"
        }
        
        response = await authenticated_client.put(f"/api/v1/workloads/{test_workload.id}", json=update_data)
        
        assert response.status_code == 200
        data = response.json()
        assert data["description"] == update_data["description"]
        assert data["name"] == test_workload.name  # Should remain unchanged
        assert data["config"] == test_workload.config  # Should remain unchanged
