apiVersion: v1
kind: ConfigMap
metadata:
  name: exopiper-config
  namespace: exopiper
data:
  # Database configuration
  POSTGRES_DB: "exoPiper"
  POSTGRES_USER: "exoPiper"
  REDIS_URL: "redis://redis-service:6379/0"
  
  # Application configuration
  ENVIRONMENT: "production"
  LOG_LEVEL: "INFO"
  
  # MinIO configuration
  MINIO_ENDPOINT: "minio-service:9000"
  MINIO_BUCKET: "exopiper-storage"
  
  # Celery configuration
  CELERY_BROKER_URL: "redis://redis-service:6379/0"
  CELERY_RESULT_BACKEND: "redis://redis-service:6379/1"
  
  # API configuration
  API_V1_STR: "/api/v1"
  CORS_ORIGINS: '["https://exopiper.com", "https://www.exopiper.com"]'
  
  # Rate limiting
  RATE_LIMIT_ENABLED: "true"
  RATE_LIMIT_PER_MINUTE: "60"
  
  # Monitoring
  METRICS_ENABLED: "true"
  HEALTH_CHECK_INTERVAL: "30"

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: nginx-config
  namespace: exopiper
data:
  nginx.conf: |
    user nginx;
    worker_processes auto;
    error_log /var/log/nginx/error.log warn;
    pid /var/run/nginx.pid;

    events {
        worker_connections 1024;
        use epoll;
        multi_accept on;
    }

    http {
        include /etc/nginx/mime.types;
        default_type application/octet-stream;
        
        log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                        '$status $body_bytes_sent "$http_referer" '
                        '"$http_user_agent" "$http_x_forwarded_for"';
        
        access_log /var/log/nginx/access.log main;
        
        sendfile on;
        tcp_nopush on;
        tcp_nodelay on;
        keepalive_timeout 65;
        types_hash_max_size 2048;
        server_tokens off;
        
        gzip on;
        gzip_vary on;
        gzip_min_length 1024;
        gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
        
        upstream backend {
            least_conn;
            server backend-service:8000 max_fails=3 fail_timeout=30s;
        }
        
        upstream frontend {
            least_conn;
            server frontend-service:3000 max_fails=3 fail_timeout=30s;
        }
        
        server {
            listen 80;
            server_name _;
            
            location /health {
                access_log off;
                return 200 "healthy\n";
                add_header Content-Type text/plain;
            }
            
            location /api/ {
                proxy_pass http://backend;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                
                proxy_connect_timeout 5s;
                proxy_send_timeout 300s;
                proxy_read_timeout 300s;
            }
            
            location / {
                proxy_pass http://frontend;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                
                proxy_connect_timeout 5s;
                proxy_send_timeout 60s;
                proxy_read_timeout 60s;
            }
        }
    }

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: postgres-config
  namespace: exopiper
data:
  postgresql.conf: |
    # PostgreSQL configuration for Kubernetes
    listen_addresses = '*'
    port = 5432
    max_connections = 200
    shared_buffers = 256MB
    effective_cache_size = 1GB
    work_mem = 4MB
    maintenance_work_mem = 64MB
    checkpoint_completion_target = 0.9
    wal_buffers = 16MB
    default_statistics_target = 100
    random_page_cost = 1.1
    effective_io_concurrency = 200
    
    # Logging
    log_destination = 'stderr'
    logging_collector = on
    log_directory = 'pg_log'
    log_filename = 'postgresql-%Y-%m-%d_%H%M%S.log'
    log_min_duration_statement = 1000
    log_checkpoints = on
    log_connections = on
    log_disconnections = on
    log_lock_waits = on
    
    # Replication (for future use)
    wal_level = replica
    max_wal_senders = 3
    wal_keep_segments = 32
