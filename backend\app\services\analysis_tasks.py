"""
Analysis tasks for data cleanup and monitoring
"""

import asyncio
from datetime import datetime, timed<PERSON><PERSON>
from typing import List
from sqlalchemy import select, and_, delete

from ..core.celery_app import celery_app
from ..core.config import settings
from ..core.database import AsyncSessionLocal
from ..models.user import User, PlanType
from ..models.benchmark import <PERSON><PERSON><PERSON><PERSON><PERSON>, BenchmarkResult, JobStatus
from ..models.workload import Workload


@celery_app.task
def cleanup_old_results():
    """Clean up old benchmark results based on user plan retention"""
    
    try:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        result = loop.run_until_complete(_cleanup_old_results())
        loop.close()
        return result
        
    except Exception as e:
        print(f"Error in cleanup task: {e}")
        return {"error": str(e)}


async def _cleanup_old_results() -> dict:
    """Cleanup implementation"""
    
    async with AsyncSessionLocal() as db:
        cleanup_stats = {
            "free_plan_cleaned": 0,
            "pro_plan_cleaned": 0,
            "team_plan_cleaned": 0,
            "total_cleaned": 0
        }
        
        # Get all users
        result = await db.execute(select(User))
        users = result.scalars().all()
        
        for user in users:
            # Determine retention period based on plan
            if user.plan_type == PlanType.FREE:
                retention_days = settings.FREE_PLAN_RETENTION_DAYS
                plan_key = "free_plan_cleaned"
            elif user.plan_type == PlanType.PRO:
                retention_days = settings.PRO_PLAN_RETENTION_DAYS
                plan_key = "pro_plan_cleaned"
            elif user.plan_type == PlanType.TEAM:
                retention_days = settings.TEAM_PLAN_RETENTION_DAYS
                plan_key = "team_plan_cleaned"
            else:
                continue
            
            # Calculate cutoff date
            cutoff_date = datetime.utcnow() - timedelta(days=retention_days)
            
            # Find old jobs for this user
            old_jobs_result = await db.execute(
                select(BenchmarkJob).where(
                    and_(
                        BenchmarkJob.user_id == user.id,
                        BenchmarkJob.created_at < cutoff_date
                    )
                )
            )
            old_jobs = old_jobs_result.scalars().all()
            
            # Delete results for old jobs
            for job in old_jobs:
                # Delete benchmark results
                await db.execute(
                    delete(BenchmarkResult).where(BenchmarkResult.job_id == job.id)
                )
                
                # Delete the job itself
                await db.delete(job)
                
                cleanup_stats[plan_key] += 1
                cleanup_stats["total_cleaned"] += 1
        
        await db.commit()
        
        print(f"Cleanup completed: {cleanup_stats}")
        return cleanup_stats


@celery_app.task
def check_performance_alerts():
    """Check for performance alerts across all users"""
    
    try:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        result = loop.run_until_complete(_check_performance_alerts())
        loop.close()
        return result
        
    except Exception as e:
        print(f"Error checking performance alerts: {e}")
        return {"error": str(e)}


async def _check_performance_alerts() -> dict:
    """Check for performance alerts implementation"""
    
    async with AsyncSessionLocal() as db:
        alert_stats = {
            "users_checked": 0,
            "alerts_triggered": 0,
            "workloads_analyzed": 0
        }
        
        # Get all active users with Pro or Team plans (alerts feature)
        result = await db.execute(
            select(User).where(
                and_(
                    User.is_active == True,
                    User.plan_type.in_([PlanType.PRO, PlanType.TEAM])
                )
            )
        )
        users = result.scalars().all()
        
        for user in users:
            alert_stats["users_checked"] += 1
            
            # Get user's workloads
            workloads_result = await db.execute(
                select(Workload).where(
                    and_(
                        Workload.user_id == user.id,
                        Workload.is_active == True
                    )
                )
            )
            workloads = workloads_result.scalars().all()
            
            for workload in workloads:
                alert_stats["workloads_analyzed"] += 1
                
                # Get recent completed jobs for this workload
                recent_jobs_result = await db.execute(
                    select(BenchmarkJob).where(
                        and_(
                            BenchmarkJob.user_id == user.id,
                            BenchmarkJob.workload_id == workload.id,
                            BenchmarkJob.status == JobStatus.COMPLETED
                        )
                    ).order_by(BenchmarkJob.completed_at.desc()).limit(5)
                )
                recent_jobs = recent_jobs_result.scalars().all()
                
                if len(recent_jobs) >= 2:
                    # Check for performance regression
                    regression_detected = await _check_workload_regression(
                        db, recent_jobs[0], recent_jobs[1]
                    )
                    
                    if regression_detected:
                        alert_stats["alerts_triggered"] += 1
                        
                        # Trigger alert
                        from .notification_tasks import send_performance_alert
                        send_performance_alert.delay(
                            user_id=user.id,
                            workload_id=workload.id,
                            alert_type="automated_regression_check",
                            current_value=regression_detected["current_p"],
                            previous_value=regression_detected["previous_p"],
                            delta=regression_detected["delta_p"]
                        )
        
        print(f"Performance alert check completed: {alert_stats}")
        return alert_stats


async def _check_workload_regression(db, current_job: BenchmarkJob, previous_job: BenchmarkJob) -> dict:
    """Check for regression between two jobs"""
    
    # Get results for both jobs
    current_results = await db.execute(
        select(BenchmarkResult).where(BenchmarkResult.job_id == current_job.id)
    )
    current_data = current_results.scalars().all()
    
    previous_results = await db.execute(
        select(BenchmarkResult).where(BenchmarkResult.job_id == previous_job.id)
    )
    previous_data = previous_results.scalars().all()
    
    # Check if we have p_exponent values
    current_p = None
    previous_p = None
    
    if current_data and current_data[0].p_exponent is not None:
        current_p = current_data[0].p_exponent
    
    if previous_data and previous_data[0].p_exponent is not None:
        previous_p = previous_data[0].p_exponent
    
    if current_p is not None and previous_p is not None:
        delta_p = current_p - previous_p
        
        # Check if regression exceeds threshold
        if delta_p > settings.DELTA_P_THRESHOLD:
            return {
                "current_p": current_p,
                "previous_p": previous_p,
                "delta_p": delta_p,
                "regression_detected": True
            }
    
    return None


@celery_app.task
def generate_performance_insights():
    """Generate performance insights for all users"""
    
    try:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        result = loop.run_until_complete(_generate_performance_insights())
        loop.close()
        return result
        
    except Exception as e:
        print(f"Error generating insights: {e}")
        return {"error": str(e)}


async def _generate_performance_insights() -> dict:
    """Generate insights implementation"""
    
    # TODO: Implement comprehensive performance insights
    # This could include:
    # - Trend analysis across workloads
    # - Comparative analysis between users
    # - Optimization recommendations
    # - Hardware utilization insights
    
    return {
        "status": "insights_generation_not_implemented",
        "message": "This feature will be implemented in a future version"
    }


@celery_app.task
def backup_critical_data():
    """Backup critical performance data"""
    
    try:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        result = loop.run_until_complete(_backup_critical_data())
        loop.close()
        return result
        
    except Exception as e:
        print(f"Error in backup task: {e}")
        return {"error": str(e)}


async def _backup_critical_data() -> dict:
    """Backup implementation"""
    
    # TODO: Implement backup to MinIO or external storage
    # This should backup:
    # - User configurations
    # - Workload definitions
    # - Critical benchmark results
    # - Analysis results
    
    return {
        "status": "backup_not_implemented",
        "message": "Backup functionality will be implemented in a future version"
    }
