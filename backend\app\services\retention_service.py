"""
Data retention service based on user plan
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, delete, and_

from ..core.database import AsyncSessionLocal
from ..models.user import User, PlanType
from ..models.benchmark import BenchmarkJ<PERSON>, BenchmarkResult
from ..models.workload import Workload
from .storage_service import storage_service

logger = logging.getLogger(__name__)


class RetentionService:
    """Service for managing data retention based on user plans"""
    
    # Retention policies by plan type (in days)
    RETENTION_POLICIES = {
        PlanType.FREE: {
            "benchmark_results": 30,      # 30 days
            "benchmark_jobs": 30,         # 30 days
            "storage_artifacts": 7,       # 7 days
            "max_workloads": 3,           # Max 3 workloads
            "max_jobs_per_month": 50      # Max 50 jobs per month
        },
        PlanType.BASIC: {
            "benchmark_results": 90,      # 90 days
            "benchmark_jobs": 90,         # 90 days
            "storage_artifacts": 30,      # 30 days
            "max_workloads": 10,          # Max 10 workloads
            "max_jobs_per_month": 200     # Max 200 jobs per month
        },
        PlanType.PRO: {
            "benchmark_results": 365,     # 1 year
            "benchmark_jobs": 365,        # 1 year
            "storage_artifacts": 90,      # 90 days
            "max_workloads": 50,          # Max 50 workloads
            "max_jobs_per_month": 1000    # Max 1000 jobs per month
        },
        PlanType.ENTERPRISE: {
            "benchmark_results": -1,      # Unlimited
            "benchmark_jobs": -1,         # Unlimited
            "storage_artifacts": 365,     # 1 year
            "max_workloads": -1,          # Unlimited
            "max_jobs_per_month": -1      # Unlimited
        }
    }
    
    def __init__(self):
        self.storage = storage_service
    
    async def get_user_retention_policy(self, user: User) -> Dict[str, Any]:
        """Get retention policy for a specific user"""
        plan_type = PlanType(user.plan_type)
        return self.RETENTION_POLICIES.get(plan_type, self.RETENTION_POLICIES[PlanType.FREE])
    
    async def check_user_limits(self, user: User, db: AsyncSession) -> Dict[str, Any]:
        """Check if user is within their plan limits"""
        policy = await self.get_user_retention_policy(user)
        
        # Count current workloads
        workload_count_query = select(Workload).where(
            and_(Workload.user_id == user.id, Workload.is_active == True)
        )
        workload_result = await db.execute(workload_count_query)
        current_workloads = len(workload_result.scalars().all())
        
        # Count jobs this month
        month_start = datetime.utcnow().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        jobs_this_month_query = select(BenchmarkJob).where(
            and_(
                BenchmarkJob.user_id == user.id,
                BenchmarkJob.created_at >= month_start
            )
        )
        jobs_result = await db.execute(jobs_this_month_query)
        jobs_this_month = len(jobs_result.scalars().all())
        
        # Check limits
        limits_status = {
            "workloads": {
                "current": current_workloads,
                "limit": policy["max_workloads"],
                "exceeded": policy["max_workloads"] != -1 and current_workloads >= policy["max_workloads"]
            },
            "monthly_jobs": {
                "current": jobs_this_month,
                "limit": policy["max_jobs_per_month"],
                "exceeded": policy["max_jobs_per_month"] != -1 and jobs_this_month >= policy["max_jobs_per_month"]
            },
            "plan_type": user.plan_type,
            "retention_days": {
                "results": policy["benchmark_results"],
                "jobs": policy["benchmark_jobs"],
                "artifacts": policy["storage_artifacts"]
            }
        }
        
        return limits_status
    
    async def cleanup_user_data(self, user: User, db: AsyncSession) -> Dict[str, Any]:
        """Clean up old data for a specific user based on their plan"""
        policy = await self.get_user_retention_policy(user)
        cleanup_stats = {
            "user_id": user.id,
            "plan_type": user.plan_type,
            "deleted_results": 0,
            "deleted_jobs": 0,
            "deleted_artifacts": 0,
            "errors": []
        }
        
        try:
            # Clean up benchmark results
            if policy["benchmark_results"] > 0:
                cutoff_date = datetime.utcnow() - timedelta(days=policy["benchmark_results"])
                
                # Get jobs to delete
                old_jobs_query = select(BenchmarkJob).where(
                    and_(
                        BenchmarkJob.user_id == user.id,
                        BenchmarkJob.created_at < cutoff_date
                    )
                )
                old_jobs_result = await db.execute(old_jobs_query)
                old_jobs = old_jobs_result.scalars().all()
                
                for job in old_jobs:
                    # Delete associated results
                    delete_results_query = delete(BenchmarkResult).where(
                        BenchmarkResult.job_id == job.id
                    )
                    result = await db.execute(delete_results_query)
                    cleanup_stats["deleted_results"] += result.rowcount
                    
                    # Clean up storage artifacts
                    try:
                        if job.log_file_path:
                            await self._cleanup_storage_object(job.log_file_path)
                        if job.artifact_file_path:
                            await self._cleanup_storage_object(job.artifact_file_path)
                        cleanup_stats["deleted_artifacts"] += 1
                    except Exception as e:
                        cleanup_stats["errors"].append(f"Storage cleanup error for job {job.id}: {e}")
                
                # Delete old jobs
                delete_jobs_query = delete(BenchmarkJob).where(
                    and_(
                        BenchmarkJob.user_id == user.id,
                        BenchmarkJob.created_at < cutoff_date
                    )
                )
                result = await db.execute(delete_jobs_query)
                cleanup_stats["deleted_jobs"] = result.rowcount
                
                await db.commit()
            
            # Clean up storage artifacts separately (may have different retention)
            if policy["storage_artifacts"] > 0 and policy["storage_artifacts"] != policy["benchmark_jobs"]:
                await self._cleanup_old_storage_artifacts(user.id, policy["storage_artifacts"])
            
        except Exception as e:
            await db.rollback()
            cleanup_stats["errors"].append(f"Database cleanup error: {e}")
            logger.error(f"Error cleaning up data for user {user.id}: {e}")
        
        return cleanup_stats
    
    async def _cleanup_storage_object(self, object_key: str) -> None:
        """Clean up a specific storage object"""
        try:
            # This would call MinIO delete, but we'll implement it safely
            # For now, just log the action
            logger.info(f"Would delete storage object: {object_key}")
            # storage_service.delete_object(object_key)
        except Exception as e:
            logger.error(f"Error deleting storage object {object_key}: {e}")
            raise
    
    async def _cleanup_old_storage_artifacts(self, user_id: int, retention_days: int) -> None:
        """Clean up old storage artifacts for a user"""
        try:
            # This would implement storage-specific cleanup
            # For now, just log the action
            logger.info(f"Would cleanup storage artifacts for user {user_id} older than {retention_days} days")
        except Exception as e:
            logger.error(f"Error cleaning up storage artifacts for user {user_id}: {e}")
    
    async def cleanup_all_users(self) -> Dict[str, Any]:
        """Run cleanup for all users"""
        total_stats = {
            "processed_users": 0,
            "total_deleted_results": 0,
            "total_deleted_jobs": 0,
            "total_deleted_artifacts": 0,
            "errors": [],
            "user_stats": []
        }
        
        async with AsyncSessionLocal() as db:
            try:
                # Get all users
                users_query = select(User).where(User.is_active == True)
                users_result = await db.execute(users_query)
                users = users_result.scalars().all()
                
                for user in users:
                    try:
                        user_stats = await self.cleanup_user_data(user, db)
                        
                        total_stats["processed_users"] += 1
                        total_stats["total_deleted_results"] += user_stats["deleted_results"]
                        total_stats["total_deleted_jobs"] += user_stats["deleted_jobs"]
                        total_stats["total_deleted_artifacts"] += user_stats["deleted_artifacts"]
                        total_stats["user_stats"].append(user_stats)
                        
                        if user_stats["errors"]:
                            total_stats["errors"].extend(user_stats["errors"])
                        
                        logger.info(f"Cleaned up data for user {user.id}: {user_stats}")
                        
                    except Exception as e:
                        error_msg = f"Error processing user {user.id}: {e}"
                        total_stats["errors"].append(error_msg)
                        logger.error(error_msg)
                
            except Exception as e:
                error_msg = f"Error in cleanup_all_users: {e}"
                total_stats["errors"].append(error_msg)
                logger.error(error_msg)
        
        return total_stats
    
    async def get_user_usage_stats(self, user: User, db: AsyncSession) -> Dict[str, Any]:
        """Get usage statistics for a user"""
        policy = await self.get_user_retention_policy(user)
        
        # Count workloads
        workloads_query = select(Workload).where(
            and_(Workload.user_id == user.id, Workload.is_active == True)
        )
        workloads_result = await db.execute(workloads_query)
        workloads_count = len(workloads_result.scalars().all())
        
        # Count total jobs
        jobs_query = select(BenchmarkJob).where(BenchmarkJob.user_id == user.id)
        jobs_result = await db.execute(jobs_query)
        total_jobs = len(jobs_result.scalars().all())
        
        # Count jobs this month
        month_start = datetime.utcnow().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        monthly_jobs_query = select(BenchmarkJob).where(
            and_(
                BenchmarkJob.user_id == user.id,
                BenchmarkJob.created_at >= month_start
            )
        )
        monthly_jobs_result = await db.execute(monthly_jobs_query)
        monthly_jobs = len(monthly_jobs_result.scalars().all())
        
        # Count total results
        results_query = select(BenchmarkResult).join(BenchmarkJob).where(
            BenchmarkJob.user_id == user.id
        )
        results_result = await db.execute(results_query)
        total_results = len(results_result.scalars().all())
        
        return {
            "user_id": user.id,
            "plan_type": user.plan_type,
            "usage": {
                "workloads": {
                    "current": workloads_count,
                    "limit": policy["max_workloads"]
                },
                "jobs": {
                    "total": total_jobs,
                    "this_month": monthly_jobs,
                    "monthly_limit": policy["max_jobs_per_month"]
                },
                "results": {
                    "total": total_results
                }
            },
            "retention_policy": policy,
            "limits_exceeded": {
                "workloads": policy["max_workloads"] != -1 and workloads_count >= policy["max_workloads"],
                "monthly_jobs": policy["max_jobs_per_month"] != -1 and monthly_jobs >= policy["max_jobs_per_month"]
            }
        }


# Global retention service instance
retention_service = RetentionService()
