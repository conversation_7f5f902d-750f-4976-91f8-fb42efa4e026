#!/bin/bash

# Deploy Exo Piper Frontend to CDN
# Supports CloudFlare, AWS CloudFront, and other CDN providers

set -e

echo "🌐 Exo Piper CDN Deployment"
echo "=========================="

# Colors
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Configuration
CDN_PROVIDER=${CDN_PROVIDER:-"cloudflare"}
DOMAIN=${DOMAIN:-"exopiper.com"}
CDN_DOMAIN=${CDN_DOMAIN:-"cdn.exopiper.com"}
BUILD_DIR="frontend/out"
DIST_DIR="frontend/.next"

# Check dependencies
check_dependencies() {
    log_info "Checking dependencies..."
    
    # Check Node.js
    if ! command -v node &> /dev/null; then
        log_error "Node.js is not installed"
        exit 1
    fi
    
    # Check npm
    if ! command -v npm &> /dev/null; then
        log_error "npm is not installed"
        exit 1
    fi
    
    # Check CDN provider tools
    case $CDN_PROVIDER in
        "cloudflare")
            if ! command -v wrangler &> /dev/null; then
                log_warning "Wrangler CLI not found. Installing..."
                npm install -g wrangler
            fi
            ;;
        "aws")
            if ! command -v aws &> /dev/null; then
                log_error "AWS CLI is not installed"
                exit 1
            fi
            ;;
        "vercel")
            if ! command -v vercel &> /dev/null; then
                log_warning "Vercel CLI not found. Installing..."
                npm install -g vercel
            fi
            ;;
    esac
    
    log_success "Dependencies check completed"
}

# Build frontend for production
build_frontend() {
    log_info "Building frontend for CDN deployment..."
    
    cd frontend
    
    # Install dependencies
    log_info "Installing frontend dependencies..."
    npm ci
    
    # Set environment variables for static export
    export NODE_ENV=production
    export STATIC_EXPORT=true
    export CDN_URL="https://${CDN_DOMAIN}"
    export NEXT_PUBLIC_API_URL="https://api.${DOMAIN}"
    
    # Build the application
    log_info "Building Next.js application..."
    npm run build
    
    # Verify build output
    if [ ! -d "out" ]; then
        log_error "Build failed - output directory not found"
        exit 1
    fi
    
    log_success "Frontend build completed"
    cd ..
}

# Optimize assets for CDN
optimize_assets() {
    log_info "Optimizing assets for CDN..."
    
    # Install optimization tools if needed
    if ! command -v imagemin &> /dev/null; then
        log_info "Installing imagemin for image optimization..."
        npm install -g imagemin-cli imagemin-webp imagemin-mozjpeg imagemin-pngquant
    fi
    
    # Optimize images
    log_info "Optimizing images..."
    find $BUILD_DIR -name "*.jpg" -o -name "*.jpeg" -o -name "*.png" | while read img; do
        imagemin "$img" --out-dir="$(dirname "$img")" --plugin=mozjpeg --plugin=pngquant
    done
    
    # Generate WebP versions
    log_info "Generating WebP versions..."
    find $BUILD_DIR -name "*.jpg" -o -name "*.jpeg" -o -name "*.png" | while read img; do
        imagemin "$img" --out-dir="$(dirname "$img")" --plugin=webp
    done
    
    # Compress CSS and JS (if not already done by Next.js)
    if command -v terser &> /dev/null; then
        log_info "Compressing JavaScript files..."
        find $BUILD_DIR -name "*.js" ! -name "*.min.js" | while read js; do
            terser "$js" -o "${js%.js}.min.js" -c -m
        done
    fi
    
    log_success "Asset optimization completed"
}

# Deploy to CloudFlare
deploy_cloudflare() {
    log_info "Deploying to CloudFlare..."
    
    # Create wrangler.toml if it doesn't exist
    if [ ! -f "wrangler.toml" ]; then
        log_info "Creating wrangler.toml configuration..."
        cat > wrangler.toml << EOF
name = "exopiper-frontend"
type = "webpack"
account_id = "${CLOUDFLARE_ACCOUNT_ID}"
workers_dev = false
route = "${CDN_DOMAIN}/*"
zone_id = "${CLOUDFLARE_ZONE_ID}"

[site]
bucket = "./${BUILD_DIR}"
entry-point = "workers-site"

[env.production]
name = "exopiper-frontend-prod"
route = "${CDN_DOMAIN}/*"

[build]
command = "npm run build"
cwd = "frontend"
watch_dir = "frontend"

[build.upload]
format = "service-worker"
EOF
    fi
    
    # Deploy using Wrangler
    wrangler publish --env production
    
    log_success "CloudFlare deployment completed"
}

# Deploy to AWS CloudFront
deploy_aws() {
    log_info "Deploying to AWS CloudFront..."
    
    # S3 bucket name
    S3_BUCKET="exopiper-frontend-${DOMAIN}"
    
    # Create S3 bucket if it doesn't exist
    if ! aws s3 ls "s3://${S3_BUCKET}" 2>&1 | grep -q 'NoSuchBucket'; then
        log_info "Creating S3 bucket..."
        aws s3 mb "s3://${S3_BUCKET}"
        
        # Configure bucket for static website hosting
        aws s3 website "s3://${S3_BUCKET}" \
            --index-document index.html \
            --error-document 404.html
    fi
    
    # Sync files to S3
    log_info "Syncing files to S3..."
    aws s3 sync $BUILD_DIR "s3://${S3_BUCKET}" \
        --delete \
        --cache-control "public, max-age=31536000" \
        --exclude "*.html" \
        --exclude "service-worker.js"
    
    # Upload HTML files with shorter cache
    aws s3 sync $BUILD_DIR "s3://${S3_BUCKET}" \
        --delete \
        --cache-control "public, max-age=3600" \
        --include "*.html" \
        --include "service-worker.js"
    
    # Create CloudFront distribution if needed
    if [ ! -f "cloudfront-distribution-id.txt" ]; then
        log_info "Creating CloudFront distribution..."
        
        DISTRIBUTION_CONFIG=$(cat << EOF
{
    "CallerReference": "exopiper-$(date +%s)",
    "Aliases": {
        "Quantity": 1,
        "Items": ["${CDN_DOMAIN}"]
    },
    "DefaultRootObject": "index.html",
    "Origins": {
        "Quantity": 1,
        "Items": [
            {
                "Id": "S3-${S3_BUCKET}",
                "DomainName": "${S3_BUCKET}.s3.amazonaws.com",
                "S3OriginConfig": {
                    "OriginAccessIdentity": ""
                }
            }
        ]
    },
    "DefaultCacheBehavior": {
        "TargetOriginId": "S3-${S3_BUCKET}",
        "ViewerProtocolPolicy": "redirect-to-https",
        "MinTTL": 0,
        "ForwardedValues": {
            "QueryString": false,
            "Cookies": {"Forward": "none"}
        }
    },
    "Comment": "Exo Piper Frontend CDN",
    "Enabled": true
}
EOF
        )
        
        DISTRIBUTION_ID=$(aws cloudfront create-distribution \
            --distribution-config "$DISTRIBUTION_CONFIG" \
            --query 'Distribution.Id' \
            --output text)
        
        echo $DISTRIBUTION_ID > cloudfront-distribution-id.txt
        log_success "CloudFront distribution created: $DISTRIBUTION_ID"
    else
        DISTRIBUTION_ID=$(cat cloudfront-distribution-id.txt)
        log_info "Using existing CloudFront distribution: $DISTRIBUTION_ID"
    fi
    
    # Invalidate CloudFront cache
    log_info "Invalidating CloudFront cache..."
    aws cloudfront create-invalidation \
        --distribution-id $DISTRIBUTION_ID \
        --paths "/*"
    
    log_success "AWS CloudFront deployment completed"
}

# Deploy to Vercel
deploy_vercel() {
    log_info "Deploying to Vercel..."
    
    cd frontend
    
    # Deploy using Vercel CLI
    vercel --prod --yes
    
    log_success "Vercel deployment completed"
    cd ..
}

# Configure DNS
configure_dns() {
    log_info "DNS configuration instructions:"
    
    case $CDN_PROVIDER in
        "cloudflare")
            echo "1. Add CNAME record: ${CDN_DOMAIN} -> your-worker.your-subdomain.workers.dev"
            echo "2. Enable 'Proxied' status in CloudFlare dashboard"
            ;;
        "aws")
            echo "1. Add CNAME record: ${CDN_DOMAIN} -> your-distribution.cloudfront.net"
            echo "2. Configure SSL certificate in AWS Certificate Manager"
            ;;
        "vercel")
            echo "1. Configure custom domain in Vercel dashboard"
            echo "2. Add CNAME record as instructed by Vercel"
            ;;
    esac
}

# Performance testing
test_performance() {
    log_info "Running performance tests..."
    
    # Install Lighthouse if not available
    if ! command -v lighthouse &> /dev/null; then
        log_info "Installing Lighthouse..."
        npm install -g lighthouse
    fi
    
    # Test main domain
    log_info "Testing performance of https://${DOMAIN}..."
    lighthouse "https://${DOMAIN}" \
        --output=html \
        --output-path=./performance-report.html \
        --chrome-flags="--headless"
    
    # Test CDN domain
    log_info "Testing performance of https://${CDN_DOMAIN}..."
    lighthouse "https://${CDN_DOMAIN}" \
        --output=html \
        --output-path=./cdn-performance-report.html \
        --chrome-flags="--headless"
    
    log_success "Performance tests completed"
    log_info "Reports saved: performance-report.html, cdn-performance-report.html"
}

# Main deployment function
main() {
    log_info "Starting CDN deployment for provider: $CDN_PROVIDER"
    
    check_dependencies
    build_frontend
    optimize_assets
    
    case $CDN_PROVIDER in
        "cloudflare")
            deploy_cloudflare
            ;;
        "aws")
            deploy_aws
            ;;
        "vercel")
            deploy_vercel
            ;;
        *)
            log_error "Unsupported CDN provider: $CDN_PROVIDER"
            log_info "Supported providers: cloudflare, aws, vercel"
            exit 1
            ;;
    esac
    
    configure_dns
    
    if [ "$1" = "--test" ]; then
        test_performance
    fi
    
    log_success "CDN deployment completed successfully!"
    
    echo
    echo "🎉 Deployment Summary:"
    echo "✅ Frontend built and optimized"
    echo "✅ Assets deployed to CDN"
    echo "✅ Cache configured"
    echo
    echo "🌐 URLs:"
    echo "Main site: https://${DOMAIN}"
    echo "CDN: https://${CDN_DOMAIN}"
    echo
    echo "📊 Next steps:"
    echo "1. Configure DNS records"
    echo "2. Test the deployment"
    echo "3. Monitor performance"
}

# Handle command line arguments
case "$1" in
    "cloudflare")
        CDN_PROVIDER="cloudflare"
        main "$2"
        ;;
    "aws")
        CDN_PROVIDER="aws"
        main "$2"
        ;;
    "vercel")
        CDN_PROVIDER="vercel"
        main "$2"
        ;;
    "test")
        test_performance
        ;;
    *)
        main "$@"
        ;;
esac
