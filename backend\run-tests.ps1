# Run tests for Exo Piper backend (PowerShell)

Write-Host "🧪 Running Exo Piper Backend Tests..." -ForegroundColor Cyan

# Function to print colored output
function Write-Status {
    param(
        [string]$Status,
        [string]$Message
    )
    
    switch ($Status) {
        "SUCCESS" { Write-Host "✅ $Message" -ForegroundColor Green }
        "ERROR" { Write-Host "❌ $Message" -ForegroundColor Red }
        "WARNING" { Write-Host "⚠️  $Message" -ForegroundColor Yellow }
        "INFO" { Write-Host "ℹ️  $Message" -ForegroundColor Blue }
    }
}

# Check if we're in the backend directory
if (-not (Test-Path "requirements.txt")) {
    Write-Status "ERROR" "Please run this script from the backend directory"
    exit 1
}

# Check if virtual environment exists
if (-not (Test-Path "venv")) {
    Write-Status "INFO" "Creating virtual environment..."
    python -m venv venv
}

# Activate virtual environment
Write-Status "INFO" "Activating virtual environment..."
& "venv\Scripts\Activate.ps1"

# Install dependencies
Write-Status "INFO" "Installing dependencies..."
pip install -r requirements.txt

# Set environment variables for testing
$env:ENVIRONMENT = "testing"
$env:DATABASE_URL = "sqlite+aiosqlite:///:memory:"
$env:SECRET_KEY = "test-secret-key"
$env:REDIS_URL = "redis://localhost:6379/1"

# Run tests with coverage
Write-Status "INFO" "Running tests..."

# Install coverage if not present
pip install coverage

# Run tests with coverage
coverage run -m pytest tests/ -v --tb=short

if ($LASTEXITCODE -eq 0) {
    Write-Status "SUCCESS" "All tests passed!"
    
    # Generate coverage report
    Write-Status "INFO" "Generating coverage report..."
    coverage report
    coverage html
    
    Write-Status "SUCCESS" "Coverage report generated in htmlcov/"
    
} else {
    Write-Status "ERROR" "Some tests failed"
    exit 1
}

# Deactivate virtual environment
deactivate

Write-Status "SUCCESS" "Test run completed!"
