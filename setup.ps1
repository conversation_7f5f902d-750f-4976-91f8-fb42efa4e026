# Exo Piper Setup Script for Windows
# This script sets up the development environment on Windows

Write-Host "🚀 Setting up Exo Piper Development Environment" -ForegroundColor Green
Write-Host "================================================" -ForegroundColor Green

# Check if Docker is installed
try {
    docker --version | Out-Null
    Write-Host "✅ Docker is installed" -ForegroundColor Green
} catch {
    Write-Host "❌ Docker is not installed. Please install Docker Desktop first." -ForegroundColor Red
    exit 1
}

# Check if Docker Compose is installed
try {
    docker-compose --version | Out-Null
    Write-Host "✅ Docker Compose is installed" -ForegroundColor Green
} catch {
    Write-Host "❌ Docker Compose is not installed. Please install Docker Compose first." -ForegroundColor Red
    exit 1
}

# Check if Node.js is installed
try {
    node --version | Out-Null
    Write-Host "✅ Node.js is installed" -ForegroundColor Green
} catch {
    Write-Host "❌ Node.js is not installed. Please install Node.js first." -ForegroundColor Red
    exit 1
}

# Check if Python is installed
try {
    python --version | Out-Null
    Write-Host "✅ Python is installed" -ForegroundColor Green
} catch {
    Write-Host "❌ Python is not installed. Please install Python first." -ForegroundColor Red
    exit 1
}

Write-Host "✅ Prerequisites check passed" -ForegroundColor Green

# Create .env file if it doesn't exist
if (-not (Test-Path ".env")) {
    Write-Host "📝 Creating .env file from template..." -ForegroundColor Yellow
    Copy-Item ".env.example" ".env"
    Write-Host "⚠️  Please edit .env file with your configuration" -ForegroundColor Yellow
} else {
    Write-Host "✅ .env file already exists" -ForegroundColor Green
}

# Setup backend
Write-Host "🔧 Setting up backend..." -ForegroundColor Cyan
Set-Location backend

# Create virtual environment if it doesn't exist
if (-not (Test-Path "venv")) {
    Write-Host "📦 Creating Python virtual environment..." -ForegroundColor Yellow
    python -m venv venv
}

# Activate virtual environment
& "venv\Scripts\Activate.ps1"

# Install Python dependencies
Write-Host "📦 Installing Python dependencies..." -ForegroundColor Yellow
pip install -r requirements.txt

Set-Location ..

# Setup frontend
Write-Host "🌐 Setting up frontend..." -ForegroundColor Cyan
Set-Location frontend

# Install Node.js dependencies
Write-Host "📦 Installing Node.js dependencies..." -ForegroundColor Yellow
npm install

Set-Location ..

# Setup CLI
Write-Host "💻 Setting up CLI..." -ForegroundColor Cyan
Set-Location cli

# Install CLI in development mode
Write-Host "📦 Installing CLI in development mode..." -ForegroundColor Yellow
pip install -e .

Set-Location ..

Write-Host "🐳 Starting Docker services..." -ForegroundColor Cyan
docker-compose up -d postgres redis minio

# Wait for services to be ready
Write-Host "⏳ Waiting for services to be ready..." -ForegroundColor Yellow
Start-Sleep -Seconds 10

Write-Host "✅ Setup completed successfully!" -ForegroundColor Green
Write-Host ""
Write-Host "🚀 To start the development environment:" -ForegroundColor Cyan
Write-Host "   1. Start all services: docker-compose up -d"
Write-Host "   2. Start backend: cd backend && venv\Scripts\Activate.ps1 && uvicorn main:app --reload"
Write-Host "   3. Start frontend: cd frontend && npm run dev"
Write-Host "   4. Start Celery worker: cd backend && venv\Scripts\Activate.ps1 && celery -A app.core.celery_app worker --loglevel=info"
Write-Host ""
Write-Host "🌐 Access points:" -ForegroundColor Cyan
Write-Host "   - Frontend: http://localhost:3000"
Write-Host "   - Backend API: http://localhost:8000"
Write-Host "   - API Docs: http://localhost:8000/docs"
Write-Host "   - MinIO Console: http://localhost:9001"
Write-Host ""
Write-Host "📚 Next steps:" -ForegroundColor Cyan
Write-Host "   1. Edit .env file with your configuration"
Write-Host "   2. Test CLI: perf-audit --help"
Write-Host "   3. Create a user account via API or frontend"
Write-Host "   4. Start benchmarking!"
