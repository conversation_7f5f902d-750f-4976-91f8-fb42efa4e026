"""
Benchmarks API endpoints
"""

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from typing import List
import uuid

from ..core.database import get_db
from ..core.security import get_current_user
from ..models.user import User
from ..models.workload import Workload
from ..models.benchmark import BenchmarkJob, JobStatus

router = APIRouter()


@router.post("/run/{workload_id}")
async def run_benchmark(
    workload_id: int,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Start a benchmark job for a workload"""

    # Get workload
    result = await db.execute(
        select(Workload).where(
            (Workload.id == workload_id) & (Workload.user_id == current_user.id)
        )
    )
    workload = result.scalar_one_or_none()

    if not workload:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Workload not found"
        )

    if not workload.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Workload is not active"
        )

    # Create benchmark job
    job_id = str(uuid.uuid4())
    new_job = BenchmarkJob(
        user_id=current_user.id,
        workload_id=workload_id,
        job_id=job_id,
        status=JobStatus.PENDING
    )

    db.add(new_job)
    await db.commit()
    await db.refresh(new_job)

    # Queue job with Celery
    from ..services.benchmark_tasks import run_benchmark_job
    task = run_benchmark_job.delay(new_job.id)

    return {
        "job_id": job_id,
        "status": "pending",
        "message": "Benchmark job queued successfully"
    }


@router.get("/jobs")
async def list_benchmark_jobs(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """List user's benchmark jobs"""

    result = await db.execute(
        select(BenchmarkJob)
        .where(BenchmarkJob.user_id == current_user.id)
        .order_by(BenchmarkJob.created_at.desc())
        .limit(50)
    )
    jobs = result.scalars().all()

    return {"jobs": jobs}


@router.get("/jobs/{job_id}")
async def get_benchmark_job(
    job_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get a specific benchmark job"""

    result = await db.execute(
        select(BenchmarkJob).where(
            (BenchmarkJob.job_id == job_id) & (BenchmarkJob.user_id == current_user.id)
        )
    )
    job = result.scalar_one_or_none()

    if not job:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Benchmark job not found"
        )

    return job


@router.post("/jobs/{job_id}/cancel")
async def cancel_benchmark_job(
    job_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Cancel a benchmark job"""

    result = await db.execute(
        select(BenchmarkJob).where(
            (BenchmarkJob.job_id == job_id) & (BenchmarkJob.user_id == current_user.id)
        )
    )
    job = result.scalar_one_or_none()

    if not job:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Benchmark job not found"
        )

    if job.status in [JobStatus.COMPLETED, JobStatus.FAILED, JobStatus.CANCELLED]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Job cannot be cancelled"
        )

    job.status = JobStatus.CANCELLED
    await db.commit()

    return {"message": "Job cancelled successfully"}
