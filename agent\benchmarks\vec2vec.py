"""
Vector-to-Vector operations benchmark implementation
"""

import numpy as np
from typing import List, Dict, Any
from .base import BaseBenchmark


class Vec2VecBenchmark(BaseBenchmark):
    """Vector and matrix operations benchmark"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.validate_config(['max_dimension'])
        
        # Supported operations
        self.operations = self.config.get('operations', ['dot', 'matmul', 'norm'])
        
    def get_algorithm_name(self) -> str:
        return "vec2vec"
    
    def generate_vectors(self, dimension: int) -> tuple:
        """Generate random vectors for testing"""
        np.random.seed(42)  # For reproducible results
        vector_a = np.random.randn(dimension).astype(np.float64)
        vector_b = np.random.randn(dimension).astype(np.float64)
        return vector_a, vector_b
    
    def generate_matrices(self, dimension: int) -> tuple:
        """Generate random matrices for testing"""
        np.random.seed(42)  # For reproducible results
        matrix_a = np.random.randn(dimension, dimension).astype(np.float64)
        matrix_b = np.random.randn(dimension, dimension).astype(np.float64)
        return matrix_a, matrix_b
    
    def benchmark_dot_product(self, dimension: int) -> None:
        """Benchmark vector dot product"""
        vector_a, vector_b = self.generate_vectors(dimension)
        result = np.dot(vector_a, vector_b)
        
        # Verify result is a scalar
        if not np.isscalar(result):
            raise ValueError(f"Dot product should return scalar, got {type(result)}")
    
    def benchmark_matrix_multiply(self, dimension: int) -> None:
        """Benchmark matrix multiplication"""
        matrix_a, matrix_b = self.generate_matrices(dimension)
        result = np.matmul(matrix_a, matrix_b)
        
        # Verify result shape
        expected_shape = (dimension, dimension)
        if result.shape != expected_shape:
            raise ValueError(f"Matrix multiply should return {expected_shape}, got {result.shape}")
    
    def benchmark_vector_norm(self, dimension: int) -> None:
        """Benchmark vector norm calculation"""
        vector_a, _ = self.generate_vectors(dimension)
        
        # Calculate different norms
        l1_norm = np.linalg.norm(vector_a, ord=1)
        l2_norm = np.linalg.norm(vector_a, ord=2)
        inf_norm = np.linalg.norm(vector_a, ord=np.inf)
        
        # Verify norms are positive scalars
        for norm_val in [l1_norm, l2_norm, inf_norm]:
            if not (np.isscalar(norm_val) and norm_val >= 0):
                raise ValueError(f"Norm should be non-negative scalar, got {norm_val}")
    
    def benchmark_eigenvalues(self, dimension: int) -> None:
        """Benchmark eigenvalue computation"""
        matrix_a, _ = self.generate_matrices(dimension)
        
        # Make matrix symmetric for stable eigenvalues
        symmetric_matrix = (matrix_a + matrix_a.T) / 2
        
        eigenvalues = np.linalg.eigvals(symmetric_matrix)
        
        # Verify we got the right number of eigenvalues
        if len(eigenvalues) != dimension:
            raise ValueError(f"Should get {dimension} eigenvalues, got {len(eigenvalues)}")
    
    def get_input_sizes(self) -> List[int]:
        """Generate dimensions for benchmarking"""
        max_dim = self.config.get('max_dimension', 1000)
        min_dim = self.config.get('min_dimension', 10)
        step_factor = self.config.get('step_factor', 2)
        
        sizes = []
        current = min_dim
        
        while current <= max_dim:
            sizes.append(current)
            current = int(current * step_factor)
        
        return sizes
    
    def run_operation_benchmark(self, operation: str, dimension: int) -> Dict[str, Any]:
        """Run benchmark for a specific operation and dimension"""
        
        operation_map = {
            'dot': self.benchmark_dot_product,
            'matmul': self.benchmark_matrix_multiply,
            'norm': self.benchmark_vector_norm,
            'eigen': self.benchmark_eigenvalues,
        }
        
        if operation not in operation_map:
            raise ValueError(f"Unknown operation: {operation}")
        
        benchmark_func = operation_map[operation]
        result = self.run_multiple_iterations(benchmark_func, dimension)
        
        # Add operation-specific metadata
        result['metadata'] = {
            'operation': operation,
            'dimension': dimension,
            'data_type': 'float64',
            'library': 'numpy'
        }
        
        return result
    
    def run(self) -> List[Dict[str, Any]]:
        """Run vector operations benchmark"""
        self.log("Starting Vec2Vec benchmark")
        
        input_sizes = self.get_input_sizes()
        results = []
        
        for operation in self.operations:
            self.log(f"Benchmarking operation: {operation}")
            
            for dimension in input_sizes:
                self.log(f"Running {operation} with dimension: {dimension}")
                
                try:
                    result = self.run_operation_benchmark(operation, dimension)
                    
                    # Modify algorithm name to include operation
                    result['algorithm'] = f"vec2vec_{operation}"
                    
                    results.append(result)
                    
                    self.log(f"Completed {operation}({dimension}): {result['execution_time_ns']} ns")
                    
                except Exception as e:
                    self.log(f"Error with {operation}({dimension}): {e}")
                    continue
        
        self.log(f"Vec2Vec benchmark completed: {len(results)} data points")
        return results
